import { Entity, Column, Index, ManyToOne, Join<PERSON>olumn } from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { Employee } from './employee.entity';

@Entity('employee_certifications')
@Index(['employeeId'])
export class EmployeeCertification extends TenantAwareEntity {
  @Column({ type: 'uuid', comment: 'Employee ID' })
  employeeId: string;

  @Column({ type: 'varchar', length: 255, comment: 'Certification name' })
  name: string;

  @Column({ type: 'varchar', length: 255, comment: 'Issuing organization' })
  issuingOrganization: string;

  @Column({ type: 'date', comment: 'Issue date' })
  issueDate: Date;

  @Column({ type: 'date', nullable: true, comment: 'Expiry date' })
  expiryDate?: Date;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: 'Credential ID' })
  credentialId?: string;

  @Column({ type: 'varchar', length: 500, nullable: true, comment: 'Credential URL' })
  credentialUrl?: string;

  @ManyToOne(() => Employee, employee => employee.certifications, { eager: false })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;
}
