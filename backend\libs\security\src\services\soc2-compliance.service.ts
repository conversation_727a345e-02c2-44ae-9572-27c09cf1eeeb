import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { AuditLog } from '@app/database/entities/audit-log.entity';
import { User } from '@app/database/entities/user.entity';
import { AuditService } from './audit.service';

export interface SOC2Control {
  id: string;
  category: 'security' | 'availability' | 'processing_integrity' | 'confidentiality' | 'privacy';
  name: string;
  description: string;
  implementation: string;
  testProcedure: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually';
  lastTested?: Date;
  status: 'compliant' | 'non_compliant' | 'not_tested';
  evidence: string[];
  responsible: string;
  exceptions: string[];
}

export interface SecurityIncident {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'data_breach' | 'unauthorized_access' | 'system_compromise' | 'policy_violation' | 'other';
  reportedBy: string;
  reportedAt: Date;
  status: 'open' | 'investigating' | 'resolved' | 'closed';
  affectedSystems: string[];
  affectedUsers: number;
  containmentActions: string[];
  rootCause?: string;
  remediation?: string;
  lessonsLearned?: string;
  closedAt?: Date;
}

export interface ComplianceMetrics {
  controlsTotal: number;
  controlsCompliant: number;
  controlsNonCompliant: number;
  controlsNotTested: number;
  incidentsOpen: number;
  incidentsResolved: number;
  averageIncidentResolutionTime: number;
  lastAuditDate?: Date;
  nextAuditDate?: Date;
  complianceScore: number;
}

@Injectable()
export class SOC2ComplianceService {
  private readonly logger = new Logger(SOC2ComplianceService.name);
  private readonly controls = new Map<string, SOC2Control>();
  private readonly incidents = new Map<string, SecurityIncident>();

  constructor(
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly auditService: AuditService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initializeSOC2Controls();
  }

  /**
   * Initialize SOC 2 controls framework
   */
  private initializeSOC2Controls(): void {
    const controls: SOC2Control[] = [
      // Security Controls
      {
        id: 'SEC-001',
        category: 'security',
        name: 'Access Control Management',
        description: 'Logical and physical access is restricted to authorized users',
        implementation: 'Role-based access control with MFA and regular access reviews',
        testProcedure: 'Review user access lists and test authentication mechanisms',
        frequency: 'monthly',
        status: 'not_tested',
        evidence: [],
        responsible: 'Security Team',
        exceptions: [],
      },
      {
        id: 'SEC-002',
        category: 'security',
        name: 'Data Encryption',
        description: 'Sensitive data is encrypted in transit and at rest',
        implementation: 'AES-256 encryption for data at rest, TLS 1.3 for data in transit',
        testProcedure: 'Verify encryption implementation and key management',
        frequency: 'quarterly',
        status: 'not_tested',
        evidence: [],
        responsible: 'Security Team',
        exceptions: [],
      },
      {
        id: 'SEC-003',
        category: 'security',
        name: 'Vulnerability Management',
        description: 'Systems are regularly scanned and vulnerabilities are remediated',
        implementation: 'Automated vulnerability scanning and patch management',
        testProcedure: 'Review vulnerability scan results and remediation timelines',
        frequency: 'monthly',
        status: 'not_tested',
        evidence: [],
        responsible: 'DevOps Team',
        exceptions: [],
      },
      {
        id: 'SEC-004',
        category: 'security',
        name: 'Incident Response',
        description: 'Security incidents are detected, reported, and responded to promptly',
        implementation: 'Automated monitoring with incident response procedures',
        testProcedure: 'Test incident detection and response procedures',
        frequency: 'quarterly',
        status: 'not_tested',
        evidence: [],
        responsible: 'Security Team',
        exceptions: [],
      },

      // Availability Controls
      {
        id: 'AVL-001',
        category: 'availability',
        name: 'System Monitoring',
        description: 'Systems are monitored for availability and performance',
        implementation: 'Real-time monitoring with alerting and automated failover',
        testProcedure: 'Review monitoring logs and test alerting mechanisms',
        frequency: 'monthly',
        status: 'not_tested',
        evidence: [],
        responsible: 'DevOps Team',
        exceptions: [],
      },
      {
        id: 'AVL-002',
        category: 'availability',
        name: 'Backup and Recovery',
        description: 'Data is regularly backed up and recovery procedures are tested',
        implementation: 'Automated daily backups with quarterly recovery testing',
        testProcedure: 'Test backup integrity and recovery procedures',
        frequency: 'quarterly',
        status: 'not_tested',
        evidence: [],
        responsible: 'DevOps Team',
        exceptions: [],
      },
      {
        id: 'AVL-003',
        category: 'availability',
        name: 'Capacity Management',
        description: 'System capacity is monitored and scaled as needed',
        implementation: 'Auto-scaling with capacity monitoring and planning',
        testProcedure: 'Review capacity metrics and scaling procedures',
        frequency: 'monthly',
        status: 'not_tested',
        evidence: [],
        responsible: 'DevOps Team',
        exceptions: [],
      },

      // Processing Integrity Controls
      {
        id: 'PI-001',
        category: 'processing_integrity',
        name: 'Data Validation',
        description: 'Data inputs are validated and processing is accurate',
        implementation: 'Input validation and data integrity checks',
        testProcedure: 'Test data validation rules and processing accuracy',
        frequency: 'monthly',
        status: 'not_tested',
        evidence: [],
        responsible: 'Development Team',
        exceptions: [],
      },
      {
        id: 'PI-002',
        category: 'processing_integrity',
        name: 'Change Management',
        description: 'System changes are authorized, tested, and documented',
        implementation: 'CI/CD pipeline with approval workflows and testing',
        testProcedure: 'Review change management procedures and documentation',
        frequency: 'monthly',
        status: 'not_tested',
        evidence: [],
        responsible: 'Development Team',
        exceptions: [],
      },

      // Confidentiality Controls
      {
        id: 'CON-001',
        category: 'confidentiality',
        name: 'Data Classification',
        description: 'Data is classified and handled according to sensitivity',
        implementation: 'Data classification scheme with handling procedures',
        testProcedure: 'Review data classification and handling procedures',
        frequency: 'quarterly',
        status: 'not_tested',
        evidence: [],
        responsible: 'Security Team',
        exceptions: [],
      },
      {
        id: 'CON-002',
        category: 'confidentiality',
        name: 'Data Loss Prevention',
        description: 'Measures are in place to prevent unauthorized data disclosure',
        implementation: 'DLP tools and data access monitoring',
        testProcedure: 'Test DLP controls and review access logs',
        frequency: 'monthly',
        status: 'not_tested',
        evidence: [],
        responsible: 'Security Team',
        exceptions: [],
      },

      // Privacy Controls
      {
        id: 'PRI-001',
        category: 'privacy',
        name: 'Privacy Notice',
        description: 'Users are informed about data collection and use',
        implementation: 'Privacy policy and consent management',
        testProcedure: 'Review privacy notices and consent mechanisms',
        frequency: 'quarterly',
        status: 'not_tested',
        evidence: [],
        responsible: 'Legal Team',
        exceptions: [],
      },
      {
        id: 'PRI-002',
        category: 'privacy',
        name: 'Data Subject Rights',
        description: 'Data subject rights are supported and honored',
        implementation: 'GDPR compliance procedures and tools',
        testProcedure: 'Test data subject request handling procedures',
        frequency: 'quarterly',
        status: 'not_tested',
        evidence: [],
        responsible: 'Legal Team',
        exceptions: [],
      },
    ];

    controls.forEach(control => this.controls.set(control.id, control));
    this.logger.log(`Initialized ${controls.length} SOC 2 controls`);
  }

  /**
   * Test a specific control
   */
  async testControl(controlId: string, testResults: {
    status: 'compliant' | 'non_compliant';
    evidence: string[];
    exceptions?: string[];
    notes?: string;
  }): Promise<void> {
    const control = this.controls.get(controlId);
    if (!control) {
      throw new Error(`Control ${controlId} not found`);
    }

    control.status = testResults.status;
    control.evidence = testResults.evidence;
    control.exceptions = testResults.exceptions || [];
    control.lastTested = new Date();

    // Audit the control test
    await this.auditService.logUserAction({
      userId: 'system',
      action: 'soc2_control_tested',
      resource: 'compliance_control',
      resourceId: controlId,
      success: true,
      metadata: {
        controlName: control.name,
        status: testResults.status,
        evidence: testResults.evidence,
        notes: testResults.notes,
      },
    });

    // Emit event for non-compliant controls
    if (testResults.status === 'non_compliant') {
      this.eventEmitter.emit('soc2.control.non_compliant', {
        controlId,
        control,
        testResults,
      });
    }

    this.logger.log(`Control ${controlId} tested with status: ${testResults.status}`);
  }

  /**
   * Report a security incident
   */
  async reportIncident(incident: Omit<SecurityIncident, 'id' | 'reportedAt' | 'status'>): Promise<string> {
    const incidentId = `INC-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const securityIncident: SecurityIncident = {
      id: incidentId,
      reportedAt: new Date(),
      status: 'open',
      ...incident,
    };

    this.incidents.set(incidentId, securityIncident);

    // Audit the incident report
    await this.auditService.logUserAction({
      userId: incident.reportedBy,
      action: 'security_incident_reported',
      resource: 'security_incident',
      resourceId: incidentId,
      success: true,
      metadata: {
        title: incident.title,
        severity: incident.severity,
        category: incident.category,
        affectedSystems: incident.affectedSystems,
      },
    });

    // Emit event for high/critical incidents
    if (incident.severity === 'high' || incident.severity === 'critical') {
      this.eventEmitter.emit('soc2.incident.critical', {
        incidentId,
        incident: securityIncident,
      });
    }

    this.logger.warn(`Security incident reported: ${incidentId} - ${incident.title}`);
    return incidentId;
  }

  /**
   * Update incident status
   */
  async updateIncident(incidentId: string, updates: Partial<SecurityIncident>): Promise<void> {
    const incident = this.incidents.get(incidentId);
    if (!incident) {
      throw new Error(`Incident ${incidentId} not found`);
    }

    Object.assign(incident, updates);

    if (updates.status === 'closed') {
      incident.closedAt = new Date();
    }

    // Audit the incident update
    await this.auditService.logUserAction({
      userId: 'system',
      action: 'security_incident_updated',
      resource: 'security_incident',
      resourceId: incidentId,
      success: true,
      metadata: { updates },
    });

    this.logger.log(`Security incident ${incidentId} updated`);
  }

  /**
   * Get compliance metrics
   */
  getComplianceMetrics(): ComplianceMetrics {
    const controls = Array.from(this.controls.values());
    const incidents = Array.from(this.incidents.values());

    const controlsCompliant = controls.filter(c => c.status === 'compliant').length;
    const controlsNonCompliant = controls.filter(c => c.status === 'non_compliant').length;
    const controlsNotTested = controls.filter(c => c.status === 'not_tested').length;

    const incidentsOpen = incidents.filter(i => i.status === 'open' || i.status === 'investigating').length;
    const incidentsResolved = incidents.filter(i => i.status === 'resolved' || i.status === 'closed').length;

    // Calculate average resolution time
    const resolvedIncidents = incidents.filter(i => i.closedAt);
    const averageResolutionTime = resolvedIncidents.length > 0
      ? resolvedIncidents.reduce((sum, incident) => {
          const resolutionTime = incident.closedAt!.getTime() - incident.reportedAt.getTime();
          return sum + resolutionTime;
        }, 0) / resolvedIncidents.length / (1000 * 60 * 60) // Convert to hours
      : 0;

    // Calculate compliance score
    const complianceScore = controls.length > 0
      ? (controlsCompliant / controls.length) * 100
      : 0;

    return {
      controlsTotal: controls.length,
      controlsCompliant,
      controlsNonCompliant,
      controlsNotTested,
      incidentsOpen,
      incidentsResolved,
      averageIncidentResolutionTime,
      complianceScore,
    };
  }

  /**
   * Generate compliance report
   */
  async generateComplianceReport(): Promise<{
    reportDate: Date;
    metrics: ComplianceMetrics;
    controlsByCategory: Record<string, SOC2Control[]>;
    recentIncidents: SecurityIncident[];
    recommendations: string[];
  }> {
    const metrics = this.getComplianceMetrics();
    const controls = Array.from(this.controls.values());
    const incidents = Array.from(this.incidents.values());

    // Group controls by category
    const controlsByCategory = controls.reduce((acc, control) => {
      if (!acc[control.category]) {
        acc[control.category] = [];
      }
      acc[control.category].push(control);
      return acc;
    }, {} as Record<string, SOC2Control[]>);

    // Get recent incidents (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentIncidents = incidents.filter(i => i.reportedAt >= thirtyDaysAgo);

    // Generate recommendations
    const recommendations = this.generateRecommendations(controls, incidents);

    return {
      reportDate: new Date(),
      metrics,
      controlsByCategory,
      recentIncidents,
      recommendations,
    };
  }

  /**
   * Automated compliance monitoring
   */
  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async performAutomatedCompliance(): Promise<void> {
    this.logger.log('Starting automated compliance monitoring');

    try {
      // Check for overdue control tests
      const overdueControls = this.getOverdueControls();
      
      for (const control of overdueControls) {
        this.eventEmitter.emit('soc2.control.overdue', {
          controlId: control.id,
          control,
        });
      }

      // Check for long-running incidents
      const stalledIncidents = this.getStalledIncidents();
      
      for (const incident of stalledIncidents) {
        this.eventEmitter.emit('soc2.incident.stalled', {
          incidentId: incident.id,
          incident,
        });
      }

      // Generate daily metrics
      const metrics = this.getComplianceMetrics();
      
      await this.auditService.logUserAction({
        userId: 'system',
        action: 'compliance_monitoring',
        resource: 'soc2_compliance',
        success: true,
        metadata: {
          metrics,
          overdueControls: overdueControls.length,
          stalledIncidents: stalledIncidents.length,
        },
      });

      this.logger.log('Automated compliance monitoring completed');
    } catch (error) {
      this.logger.error('Automated compliance monitoring failed:', error);
    }
  }

  /**
   * Get controls that are overdue for testing
   */
  private getOverdueControls(): SOC2Control[] {
    const now = new Date();
    
    return Array.from(this.controls.values()).filter(control => {
      if (!control.lastTested) {
        return true; // Never tested
      }

      const nextTestDate = new Date(control.lastTested);
      switch (control.frequency) {
        case 'daily':
          nextTestDate.setDate(nextTestDate.getDate() + 1);
          break;
        case 'weekly':
          nextTestDate.setDate(nextTestDate.getDate() + 7);
          break;
        case 'monthly':
          nextTestDate.setMonth(nextTestDate.getMonth() + 1);
          break;
        case 'quarterly':
          nextTestDate.setMonth(nextTestDate.getMonth() + 3);
          break;
        case 'annually':
          nextTestDate.setFullYear(nextTestDate.getFullYear() + 1);
          break;
      }

      return now > nextTestDate;
    });
  }

  /**
   * Get incidents that have been open too long
   */
  private getStalledIncidents(): SecurityIncident[] {
    const now = new Date();
    const stalledThreshold = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

    return Array.from(this.incidents.values()).filter(incident => {
      if (incident.status === 'resolved' || incident.status === 'closed') {
        return false;
      }

      const timeSinceReported = now.getTime() - incident.reportedAt.getTime();
      return timeSinceReported > stalledThreshold;
    });
  }

  /**
   * Generate compliance recommendations
   */
  private generateRecommendations(controls: SOC2Control[], incidents: SecurityIncident[]): string[] {
    const recommendations: string[] = [];

    // Check for non-compliant controls
    const nonCompliantControls = controls.filter(c => c.status === 'non_compliant');
    if (nonCompliantControls.length > 0) {
      recommendations.push(`Address ${nonCompliantControls.length} non-compliant controls immediately`);
    }

    // Check for untested controls
    const untestedControls = controls.filter(c => c.status === 'not_tested');
    if (untestedControls.length > 0) {
      recommendations.push(`Test ${untestedControls.length} controls that have not been tested`);
    }

    // Check for high-severity incidents
    const highSeverityIncidents = incidents.filter(i => 
      (i.severity === 'high' || i.severity === 'critical') && 
      (i.status === 'open' || i.status === 'investigating')
    );
    if (highSeverityIncidents.length > 0) {
      recommendations.push(`Prioritize resolution of ${highSeverityIncidents.length} high-severity incidents`);
    }

    // Check for recurring incident patterns
    const incidentCategories = incidents.reduce((acc, incident) => {
      acc[incident.category] = (acc[incident.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const mostCommonCategory = Object.entries(incidentCategories)
      .sort(([,a], [,b]) => b - a)[0];

    if (mostCommonCategory && mostCommonCategory[1] > 3) {
      recommendations.push(`Review and strengthen controls for ${mostCommonCategory[0]} incidents (${mostCommonCategory[1]} occurrences)`);
    }

    return recommendations;
  }

  /**
   * Get all controls
   */
  getAllControls(): SOC2Control[] {
    return Array.from(this.controls.values());
  }

  /**
   * Get all incidents
   */
  getAllIncidents(): SecurityIncident[] {
    return Array.from(this.incidents.values());
  }

  /**
   * Get control by ID
   */
  getControl(controlId: string): SOC2Control | undefined {
    return this.controls.get(controlId);
  }

  /**
   * Get incident by ID
   */
  getIncident(incidentId: string): SecurityIncident | undefined {
    return this.incidents.get(incidentId);
  }
}
