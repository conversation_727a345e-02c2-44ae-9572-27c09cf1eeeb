import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { MfaService } from '../services/mfa.service';

export const REQUIRE_MFA_KEY = 'requireMfa';

/**
 * Guard to enforce Multi-Factor Authentication for sensitive operations
 */
@Injectable()
export class MfaGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private mfaService: MfaService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const requireMfa = this.reflector.getAllAndOverride<boolean>(
      REQUIRE_MFA_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requireMfa) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new UnauthorizedException('User not authenticated');
    }

    // Check if user has MFA enabled
    const hasMfaEnabled = await this.mfaService.isMfaEnabled(user.id);
    
    if (!hasMfaEnabled) {
      throw new UnauthorizedException(
        'Multi-factor authentication is required for this operation',
      );
    }

    // Check if current session has completed MFA verification
    const sessionId = request.sessionId;
    const isMfaVerified = await this.mfaService.isSessionMfaVerified(
      user.id,
      sessionId,
    );

    if (!isMfaVerified) {
      throw new UnauthorizedException(
        'Multi-factor authentication verification required',
      );
    }

    return true;
  }
}
