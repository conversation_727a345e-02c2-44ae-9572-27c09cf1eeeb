import { Routes, Route, Navigate } from 'react-router-dom';
import { Suspense, lazy } from 'react';

import { useAuthContext } from '@/components/providers/auth-provider';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { ProtectedRoute } from '@/components/auth/protected-route';
import { Layout } from '@/components/layout/layout';

// Lazy load pages for better performance
const LoginPage = lazy(() => import('@/pages/auth/login-page'));
const DashboardPage = lazy(() => import('@/pages/dashboard/dashboard-page'));
const EmployeesPage = lazy(() => import('@/pages/employees/employees-page'));
const EmployeeDetailPage = lazy(() => import('@/pages/employees/employee-detail-page'));
const PayrollPage = lazy(() => import('@/pages/payroll/payroll-page'));
const PerformancePage = lazy(() => import('@/pages/performance/performance-page'));
const ReportsPage = lazy(() => import('@/pages/reports/reports-page'));
const SettingsPage = lazy(() => import('@/pages/settings/settings-page'));
const ProfilePage = lazy(() => import('@/pages/profile/profile-page'));
const NotFoundPage = lazy(() => import('@/pages/error/not-found-page'));
const UnauthorizedPage = lazy(() => import('@/pages/error/unauthorized-page'));

// Loading component for Suspense fallback
const PageLoader = () => (
  <div className="flex items-center justify-center min-h-screen">
    <LoadingSpinner size="lg" />
  </div>
);

function App() {
  const { isAuthenticated, isLoading } = useAuthContext();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <div className="min-h-screen bg-background">
      <Suspense fallback={<PageLoader />}>
        <Routes>
          {/* Public routes */}
          <Route
            path="/login"
            element={
              isAuthenticated ? (
                <Navigate to="/dashboard" replace />
              ) : (
                <LoginPage />
              )
            }
          />

          {/* Protected routes */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }
          >
            {/* Dashboard */}
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<DashboardPage />} />

            {/* Employee Management */}
            <Route path="employees" element={<EmployeesPage />} />
            <Route path="employees/:id" element={<EmployeeDetailPage />} />

            {/* Payroll */}
            <Route path="payroll" element={<PayrollPage />} />

            {/* Performance Management */}
            <Route path="performance" element={<PerformancePage />} />

            {/* Reports & Analytics */}
            <Route path="reports" element={<ReportsPage />} />

            {/* Settings */}
            <Route path="settings" element={<SettingsPage />} />

            {/* User Profile */}
            <Route path="profile" element={<ProfilePage />} />
          </Route>

          {/* Unauthorized Page */}
          <Route path="/unauthorized" element={<UnauthorizedPage />} />

          {/* 404 Page */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </Suspense>
    </div>
  );
}

export default App;
