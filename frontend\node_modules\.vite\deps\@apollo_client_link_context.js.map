{"version": 3, "sources": ["../../../../node_modules/@apollo/src/link/context/index.ts"], "sourcesContent": ["import type { Operation, GraphQLRequest, NextLink } from \"../core/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport type { ObservableSubscription } from \"../../utilities/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport type { DefaultContext } from \"../../core/index.js\";\n\nexport type ContextSetter = (\n  operation: GraphQLRequest,\n  prevContext: DefaultContext\n) => Promise<DefaultContext> | DefaultContext;\n\nexport function setContext(setter: ContextSetter): ApolloLink {\n  return new ApolloLink((operation: Operation, forward: NextLink) => {\n    const { ...request } = operation;\n\n    return new Observable((observer) => {\n      let handle: ObservableSubscription;\n      let closed = false;\n      Promise.resolve(request)\n        .then((req) => setter(req, operation.getContext()))\n        .then(operation.setContext)\n        .then(() => {\n          // if the observer is already closed, no need to subscribe.\n          if (closed) return;\n          handle = forward(operation).subscribe({\n            next: observer.next.bind(observer),\n            error: observer.error.bind(observer),\n            complete: observer.complete.bind(observer),\n          });\n        })\n        .catch(observer.error.bind(observer));\n\n      return () => {\n        closed = true;\n        if (handle) handle.unsubscribe();\n      };\n    });\n  });\n}\n"], "mappings": ";;;;;;;;;;;AAWM,SAAU,WAAW,QAAqB;AAC9C,SAAO,IAAI,WAAW,SAAC,WAAsB,SAAiB;AAC5D,QAAW,UAAO,OAAK,WAAjB,CAAA,CAAc;AAEpB,WAAO,IAAI,WAAW,SAAC,UAAQ;AAC7B,UAAI;AACJ,UAAI,SAAS;AACb,cAAQ,QAAQ,OAAO,EACpB,KAAK,SAAC,KAAG;AAAK,eAAA,OAAO,KAAK,UAAU,WAAU,CAAE;MAAlC,CAAmC,EACjD,KAAK,UAAU,UAAU,EACzB,KAAK,WAAA;AAEJ,YAAI;AAAQ;AACZ,iBAAS,QAAQ,SAAS,EAAE,UAAU;UACpC,MAAM,SAAS,KAAK,KAAK,QAAQ;UACjC,OAAO,SAAS,MAAM,KAAK,QAAQ;UACnC,UAAU,SAAS,SAAS,KAAK,QAAQ;SAC1C;MACH,CAAC,EACA,MAAM,SAAS,MAAM,KAAK,QAAQ,CAAC;AAEtC,aAAO,WAAA;AACL,iBAAS;AACT,YAAI;AAAQ,iBAAO,YAAW;MAChC;IACF,CAAC;EACH,CAAC;AACH;", "names": []}