fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0
python-multipart==0.0.6
httpx==0.25.2
aiofiles==23.2.1
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.13.0
redis==5.0.1
celery==5.3.4
kombu==5.3.4

# AI/ML Libraries
numpy==1.24.3
pandas==2.0.3
scikit-learn==1.3.2
scipy==1.11.4
joblib==1.3.2
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# NLP Libraries
spacy==3.7.2
nltk==3.8.1
transformers==4.36.2
torch==2.1.2
sentence-transformers==2.2.2
textblob==0.17.1
langdetect==1.0.9

# Document Processing
PyPDF2==3.0.1
python-docx==1.1.0
openpyxl==3.1.2
Pillow==10.1.0
pytesseract==0.3.10

# Computer Vision
opencv-python==********
opencv-contrib-python==********

# Time Series Analysis
statsmodels==0.14.0
prophet==1.1.5

# Deep Learning
tensorflow==2.15.0
keras==2.15.0

# API Documentation
fastapi-users==12.1.2
fastapi-pagination==0.12.13

# Monitoring and Logging
prometheus-client==0.19.0
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
factory-boy==3.3.0

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Security
cryptography==41.0.8
bcrypt==4.1.2

# Database
psycopg2-binary==2.9.9
pymongo==4.6.0

# Message Queue
pika==1.3.2

# Configuration
dynaconf==3.2.4

# Utilities
python-dateutil==2.8.2
pytz==2023.3
click==8.1.7
rich==13.7.0
typer==0.9.0
