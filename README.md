# PeopleNest HRMS - AI-Enabled Enterprise Human Resource Management System

## 📋 Project Overview

PeopleNest is a sophisticated, AI-enabled Enterprise Human Resource Management System designed for multinational corporations with 100,000+ employees and 10,000+ concurrent users. The system combines cutting-edge microservices architecture with advanced AI capabilities to streamline HR operations, ensure enterprise-grade compliance, and provide predictive insights at scale.

## 🏗️ Enterprise Architecture & Technology Stack

### Backend Infrastructure
- **Runtime**: Node.js 20+ with TypeScript 5+
- **Framework**: NestJS with GraphQL Federation
- **Databases**: PostgreSQL 15+, MongoDB 7+, Redis 7+, ClickHouse
- **Message Queue**: Apache Kafka with Schema Registry
- **Authentication**: OAuth 2.0/OpenID Connect with Auth0
- **API Gateway**: Kong with rate limiting and analytics

### Frontend Technology
- **Framework**: React 18+ with TypeScript 5+
- **State Management**: Redux Toolkit with RTK Query
- **Styling**: Tailwind CSS with custom component library
- **Build Tool**: Vite with SWC compiler
- **Testing**: Jest, React Testing Library, Playwright

### Infrastructure & DevOps
- **Containerization**: Docker with multi-stage builds
- **Orchestration**: Kubernetes with Helm charts
- **CI/CD**: GitLab CI/CD with ArgoCD
- **Monitoring**: Prometheus, Grafana, ELK Stack
- **Security**: Zero-trust architecture, SOC 2 compliance

## 🚀 Quick Start

### Prerequisites
- Node.js 20+
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7+
- Python 3.11+

### Development Setup

```bash
# Clone the repository
git clone https://github.com/your-org/peoplenest-hrms.git
cd peoplenest-hrms

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Start development environment
docker-compose up -d

# Run database migrations
npm run db:migrate

# Start development servers
npm run dev
```

### Default Admin Credentials
- **Username**: `awadhesh`
- **Password**: `awadhesh123`
- **Role**: Super Admin

⚠️ **Security Notice**: Change default credentials immediately after first login.

## 📁 Project Structure

```
peoplenest-hrms/
├── backend/                    # NestJS Backend Services
│   ├── apps/                   # Microservices
│   │   ├── api-gateway/        # Main API Gateway
│   │   ├── auth-service/       # Authentication Service
│   │   ├── employee-service/   # Employee Management
│   │   ├── payroll-service/    # Payroll Processing
│   │   └── notification-service/ # Notifications
│   ├── libs/                   # Shared Libraries
│   │   ├── common/             # Common utilities
│   │   ├── database/           # Database configurations
│   │   └── security/           # Security modules
│   └── tools/                  # Development tools
├── frontend/                   # React Frontend Application
│   ├── src/
│   │   ├── components/         # Reusable components
│   │   ├── pages/              # Page components
│   │   ├── store/              # Redux store
│   │   ├── services/           # API services
│   │   └── utils/              # Utility functions
│   └── public/                 # Static assets
├── ai-services/                # Python AI/ML Services
│   ├── resume-parser/          # Resume parsing service
│   ├── sentiment-analysis/     # Sentiment analysis
│   ├── predictive-analytics/   # Predictive models
│   └── nlp-query/              # Natural language queries
├── infrastructure/             # Infrastructure as Code
│   ├── docker/                 # Docker configurations
│   ├── kubernetes/             # K8s manifests
│   ├── terraform/              # Infrastructure provisioning
│   └── helm/                   # Helm charts
├── docs/                       # Documentation
├── tests/                      # End-to-end tests
└── scripts/                    # Utility scripts
```

## 🔐 Security Features

- **Multi-Factor Authentication**: TOTP, SMS, Email, Hardware tokens
- **Role-Based Access Control**: 5-tier permission system
- **Data Encryption**: AES-256 at rest, TLS 1.3 in transit
- **Audit Logging**: Comprehensive activity tracking
- **Zero-Trust Architecture**: Never trust, always verify
- **Compliance**: SOC 2, GDPR, ISO 27001, HIPAA ready

## 🤖 AI/ML Capabilities

- **Resume Parsing**: 95% accuracy skill extraction
- **Sentiment Analysis**: Employee feedback analysis
- **Predictive Analytics**: Attrition risk modeling
- **Natural Language Queries**: ChatGPT-style data exploration
- **Anomaly Detection**: Payroll fraud detection
- **Performance Prediction**: Employee performance forecasting

## 🌍 Multi-Country Support

- **Payroll Processing**: UK, Germany, US, Canada tax engines
- **Compliance Monitoring**: Country-specific labor laws
- **Currency Support**: Multi-currency processing
- **Banking Integration**: Local payment methods

## 📊 Performance Metrics

- **Response Time**: <200ms for 95th percentile
- **Concurrent Users**: 10,000+ supported
- **Uptime**: 99.9% availability SLA
- **Test Coverage**: 85% backend, 80% frontend
- **Security**: Zero critical vulnerabilities

## 🔧 Development Commands

```bash
# Backend development
npm run backend:dev          # Start backend services
npm run backend:test         # Run backend tests
npm run backend:build        # Build backend services

# Frontend development
npm run frontend:dev         # Start frontend dev server
npm run frontend:test        # Run frontend tests
npm run frontend:build       # Build frontend application

# Database operations
npm run db:migrate          # Run migrations
npm run db:seed             # Seed database
npm run db:reset            # Reset database

# AI services
npm run ai:dev              # Start AI services
npm run ai:train            # Train ML models
npm run ai:deploy           # Deploy AI models

# Infrastructure
npm run infra:up            # Start infrastructure
npm run infra:down          # Stop infrastructure
npm run infra:deploy        # Deploy to production
```

## 📈 Monitoring & Observability

- **Application Metrics**: Prometheus + Grafana dashboards
- **Logging**: Centralized logging with ELK Stack
- **Tracing**: Distributed tracing with Jaeger
- **Health Checks**: Comprehensive service health monitoring
- **Alerting**: Real-time alerts for critical issues

## 🤝 Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and development process.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📧 Support

- **Email**: <EMAIL>
- **Documentation**: https://docs.peoplenest.com
- **Community**: https://community.peoplenest.com

---

**Built with ❤️ for the future of enterprise HR technology**
