export interface JwtPayload {
  /** Subject (user ID) */
  sub: string;
  
  /** User email */
  email: string;
  
  /** User role */
  role: string;
  
  /** Tenant ID for multi-tenancy */
  tenantId?: string;
  
  /** Session ID */
  sessionId: string;
  
  /** Token type (access or refresh) */
  tokenType: 'access' | 'refresh';
  
  /** JWT ID (for refresh tokens) */
  jti?: string;
  
  /** Issued at timestamp */
  iat: number;
  
  /** Expiration timestamp */
  exp: number;
  
  /** Issuer */
  iss: string;
  
  /** Audience */
  aud: string;
}
