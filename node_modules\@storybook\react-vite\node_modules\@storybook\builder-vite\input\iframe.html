<!doctype html>
<!--suppress HtmlUnknownTarget -->
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Storybook</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <style>
      @font-face {
        font-family: 'Nunito Sans';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url('./sb-common-assets/nunito-sans-regular.woff2') format('woff2');
      }

      @font-face {
        font-family: 'Nunito Sans';
        font-style: italic;
        font-weight: 400;
        font-display: swap;
        src: url('./sb-common-assets/nunito-sans-italic.woff2') format('woff2');
      }

      @font-face {
        font-family: 'Nunito Sans';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url('./sb-common-assets/nunito-sans-bold.woff2') format('woff2');
      }

      @font-face {
        font-family: 'Nunito Sans';
        font-style: italic;
        font-weight: 700;
        font-display: swap;
        src: url('./sb-common-assets/nunito-sans-bold-italic.woff2') format('woff2');
      }
    </style>

    <script>
      window.CONFIG_TYPE = '[CONFIG_TYPE HERE]';
      window.LOGLEVEL = '[LOGLEVEL HERE]';
      window.FRAMEWORK_OPTIONS = '[FRAMEWORK_OPTIONS HERE]';
      window.CHANNEL_OPTIONS = '[CHANNEL_OPTIONS HERE]';
      window.FEATURES = '[FEATURES HERE]';
      window.STORIES = '[STORIES HERE]';
      window.DOCS_OPTIONS = '[DOCS_OPTIONS HERE]';
      window.TAGS_OPTIONS = '[TAGS_OPTIONS HERE]';

      ('OTHER_GLOBALS HERE');

      // We do this so that "module && module.hot" etc. in Storybook source code
      // doesn't fail (it will simply be disabled)
      window.module = undefined;
      window.global = window;
    </script>
    <!-- [HEAD HTML SNIPPET HERE] -->
  </head>

  <body>
    <!-- [BODY HTML SNIPPET HERE] -->
    <div id="storybook-root"></div>
    <div id="storybook-docs"></div>
    <script>
      function __onViteAppLoadingError(event) {
        const hostname = globalThis.location.hostname;
        if (hostname !== 'localhost' && globalThis.CONFIG_TYPE === 'DEVELOPMENT') {
          const message = `Failed to load the Storybook preview file 'vite-app.js':

It looks like you're visiting the Storybook development server on another hostname than localhost: '${hostname}', but you haven't configured the necessary security features to support this.
Please re-run your Storybook development server with the '--host ${hostname}' flag, or manually configure your Vite allowedHosts configuration with viteFinal.

See:`;
          const docs = [
            'https://storybook.js.org/docs/api/cli-options#dev',
            'https://storybook.js.org/docs/api/main-config/main-config-vite-final',
            'https://vite.dev/config/server-options.html#server-allowedhosts',
          ];
          console.error(`${message}\n${docs.map((doc) => `- ${doc}`).join('\n')}`);

          document.getElementById('storybook-root').innerHTML =
            `<p style="color: red; max-width: 70ch">${message.replaceAll(
              '\n',
              '<br/>'
            )}<ul>${docs.map((doc) => `<li><a href='${doc}' target='_blank'>${doc}</a></li>`).join('')}<ul></p>`;
          return;
        }
      }
    </script>
    <script
      type="module"
      src="virtual:/@storybook/builder-vite/vite-app.js"
      onerror="__onViteAppLoadingError(event)"
    ></script>
  </body>
</html>
