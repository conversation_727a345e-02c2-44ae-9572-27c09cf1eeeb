import { child<PERSON>onte<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, code<PERSON><PERSON><PERSON><PERSON><PERSON>, componentDocblock<PERSON><PERSON><PERSON>, component<PERSON>eth<PERSON><PERSON><PERSON><PERSON>, componentMethodsJsD<PERSON><PERSON><PERSON><PERSON>, contextType<PERSON><PERSON><PERSON>, default<PERSON><PERSON><PERSON><PERSON><PERSON>, display<PERSON><PERSON><PERSON><PERSON><PERSON>, propDoc<PERSON><PERSON><PERSON><PERSON>, propType<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, propType<PERSON>and<PERSON>, } from './handlers/index.js';
import { fsImporter } from './importer/index.js';
import { ChainResolver, FindAnnotatedDefinitionsResolver, FindExportedDefinitionsResolver, } from './resolver/index.js';
const defaultResolvers = [
    new FindExportedDefinitionsResolver({
        limit: 1,
    }),
    new FindAnnotatedDefinitionsResolver(),
];
const defaultResolver = new ChainResolver(defaultResolvers, {
    chainingLogic: ChainResolver.Logic.ALL,
});
const defaultImporter = fsImporter;
export const defaultHandlers = [
    propTypeHandler,
    contextTypeHandler,
    childContextType<PERSON><PERSON><PERSON>,
    propType<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    prop<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    code<PERSON><PERSON><PERSON><PERSON><PERSON>,
    default<PERSON><PERSON><PERSON><PERSON><PERSON>,
    componentDoc<PERSON><PERSON><PERSON><PERSON>,
    display<PERSON><PERSON><PERSON><PERSON><PERSON>,
    componentMethods<PERSON>and<PERSON>,
    componentMethodsJsDocHandler,
];
export function createConfig(inputConfig) {
    const { babelOptions, filename, handlers, importer, resolver } = inputConfig;
    const config = {
        babelOptions: { ...babelOptions },
        handlers: handlers ?? defaultHandlers,
        importer: importer ?? defaultImporter,
        resolver: resolver ?? defaultResolver,
    };
    if (filename) {
        config.babelOptions.filename = filename;
    }
    return config;
}
