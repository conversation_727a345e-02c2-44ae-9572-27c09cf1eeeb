import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Target,
  TrendingUp,
  Calendar,
  Star,
  MessageSquare,
  Award,
  BarChart3,
  Plus,
  Filter,
  Download,
  Clock,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';
import { performanceService } from '@/services/performance-service';

interface PerformanceReview {
  id: string;
  employee: {
    id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
    position: {
      title: string;
      department: {
        name: string;
      };
    };
  };
  reviewPeriod: {
    startDate: string;
    endDate: string;
  };
  status: 'draft' | 'in_progress' | 'completed' | 'approved';
  overallRating?: number;
  dueDate: string;
  reviewer: {
    firstName: string;
    lastName: string;
  };
}

interface Goal {
  id: string;
  title: string;
  description: string;
  category: 'performance' | 'development' | 'behavioral';
  priority: 'low' | 'medium' | 'high';
  status: 'not_started' | 'in_progress' | 'completed' | 'overdue';
  progress: number;
  targetDate: string;
  employee: {
    firstName: string;
    lastName: string;
  };
}

interface FeedbackRequest {
  id: string;
  employee: {
    firstName: string;
    lastName: string;
    profilePicture?: string;
  };
  requester: {
    firstName: string;
    lastName: string;
  };
  type: 'self' | 'manager' | 'peer' | 'subordinate';
  status: 'pending' | 'completed';
  dueDate: string;
  createdAt: string;
}

export default function PerformancePage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedTab, setSelectedTab] = useState('overview');
  const [reviewFilter, setReviewFilter] = useState('all');
  const [goalFilter, setGoalFilter] = useState('all');

  const {
    data: performanceOverview,
    isLoading: overviewLoading,
  } = useQuery({
    queryKey: ['performance-overview'],
    queryFn: () => performanceService.getOverview(),
  });

  const {
    data: reviews,
    isLoading: reviewsLoading,
  } = useQuery({
    queryKey: ['performance-reviews', reviewFilter],
    queryFn: () => performanceService.getReviews({ status: reviewFilter !== 'all' ? reviewFilter : undefined }),
  });

  const {
    data: goals,
    isLoading: goalsLoading,
  } = useQuery({
    queryKey: ['performance-goals', goalFilter],
    queryFn: () => performanceService.getGoals({ status: goalFilter !== 'all' ? goalFilter : undefined }),
  });

  const {
    data: feedbackRequests,
    isLoading: feedbackLoading,
  } = useQuery({
    queryKey: ['feedback-requests'],
    queryFn: () => performanceService.getFeedbackRequests(),
  });

  const getStatusBadge = (status: string, type: 'review' | 'goal' | 'feedback' = 'review') => {
    const variants: Record<string, any> = {
      review: {
        draft: { variant: 'outline', label: 'Draft' },
        in_progress: { variant: 'default', label: 'In Progress' },
        completed: { variant: 'secondary', label: 'Completed' },
        approved: { variant: 'default', label: 'Approved' },
      },
      goal: {
        not_started: { variant: 'outline', label: 'Not Started' },
        in_progress: { variant: 'default', label: 'In Progress' },
        completed: { variant: 'default', label: 'Completed' },
        overdue: { variant: 'destructive', label: 'Overdue' },
      },
      feedback: {
        pending: { variant: 'outline', label: 'Pending' },
        completed: { variant: 'default', label: 'Completed' },
      },
    };

    const config = variants[type]?.[status] || { variant: 'outline', label: status };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge variant="destructive">High</Badge>;
      case 'medium':
        return <Badge variant="default">Medium</Badge>;
      case 'low':
        return <Badge variant="secondary">Low</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  const getRatingStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  if (overviewLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Performance Management</h1>
          <p className="text-muted-foreground">
            Track performance reviews, goals, and feedback across your organization.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Review
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Reviews</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {performanceOverview?.activeReviews || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {performanceOverview?.pendingReviews || 0} pending completion
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {performanceOverview?.averageRating?.toFixed(1) || '0.0'}
            </div>
            <div className="flex items-center mt-1">
              {getRatingStars(Math.round(performanceOverview?.averageRating || 0))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Goals Completed</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {performanceOverview?.completedGoals || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {performanceOverview?.totalGoals || 0} total goals
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Feedback Requests</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {performanceOverview?.pendingFeedback || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Awaiting your response
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="reviews">Reviews</TabsTrigger>
          <TabsTrigger value="goals">Goals</TabsTrigger>
          <TabsTrigger value="feedback">Feedback</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Recent Reviews */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Reviews</CardTitle>
              </CardHeader>
              <CardContent>
                {reviewsLoading ? (
                  <div className="flex items-center justify-center h-[200px]">
                    <LoadingSpinner />
                  </div>
                ) : (
                  <div className="space-y-4">
                    {reviews?.slice(0, 3).map((review: PerformanceReview) => (
                      <div key={review.id} className="flex items-center space-x-4">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={review.employee.profilePicture} />
                          <AvatarFallback>
                            {review.employee.firstName[0]}{review.employee.lastName[0]}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 space-y-1">
                          <p className="text-sm font-medium">
                            {review.employee.firstName} {review.employee.lastName}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {review.employee.position.title}
                          </p>
                        </div>
                        <div className="text-right space-y-1">
                          {getStatusBadge(review.status, 'review')}
                          {review.overallRating && (
                            <div className="flex items-center">
                              {getRatingStars(review.overallRating)}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Active Goals */}
            <Card>
              <CardHeader>
                <CardTitle>Active Goals</CardTitle>
              </CardHeader>
              <CardContent>
                {goalsLoading ? (
                  <div className="flex items-center justify-center h-[200px]">
                    <LoadingSpinner />
                  </div>
                ) : (
                  <div className="space-y-4">
                    {goals?.slice(0, 3).map((goal: Goal) => (
                      <div key={goal.id} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium">{goal.title}</p>
                          {getPriorityBadge(goal.priority)}
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center justify-between text-sm">
                            <span>Progress</span>
                            <span>{goal.progress}%</span>
                          </div>
                          <Progress value={goal.progress} />
                        </div>
                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                          <span>Due: {new Date(goal.targetDate).toLocaleDateString()}</span>
                          {getStatusBadge(goal.status, 'goal')}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="reviews" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Performance Reviews</h2>
            <div className="flex items-center space-x-2">
              <Select value={reviewFilter} onValueChange={setReviewFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Reviews</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                </SelectContent>
              </Select>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Review
              </Button>
            </div>
          </div>

          {reviewsLoading ? (
            <div className="flex items-center justify-center h-[300px]">
              <LoadingSpinner />
            </div>
          ) : (
            <div className="grid gap-4">
              {reviews?.map((review: PerformanceReview) => (
                <Card key={review.id}>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={review.employee.profilePicture} />
                          <AvatarFallback>
                            {review.employee.firstName[0]}{review.employee.lastName[0]}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="font-semibold">
                            {review.employee.firstName} {review.employee.lastName}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            {review.employee.position.title} • {review.employee.position.department.name}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Review Period: {new Date(review.reviewPeriod.startDate).toLocaleDateString()} - {new Date(review.reviewPeriod.endDate).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="text-right space-y-2">
                        {getStatusBadge(review.status, 'review')}
                        {review.overallRating && (
                          <div className="flex items-center justify-end">
                            {getRatingStars(review.overallRating)}
                            <span className="ml-2 text-sm font-medium">
                              {review.overallRating.toFixed(1)}
                            </span>
                          </div>
                        )}
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Calendar className="h-4 w-4 mr-1" />
                          Due: {new Date(review.dueDate).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="goals" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Goals & Objectives</h2>
            <div className="flex items-center space-x-2">
              <Select value={goalFilter} onValueChange={setGoalFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Goals</SelectItem>
                  <SelectItem value="not_started">Not Started</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="overdue">Overdue</SelectItem>
                </SelectContent>
              </Select>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Goal
              </Button>
            </div>
          </div>

          {goalsLoading ? (
            <div className="flex items-center justify-center h-[300px]">
              <LoadingSpinner />
            </div>
          ) : (
            <div className="grid gap-4">
              {goals?.map((goal: Goal) => (
                <Card key={goal.id}>
                  <CardContent className="pt-6">
                    <div className="space-y-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <h3 className="font-semibold">{goal.title}</h3>
                          <p className="text-sm text-muted-foreground">
                            {goal.description}
                          </p>
                          <div className="flex items-center space-x-4">
                            <Badge variant="outline">{goal.category}</Badge>
                            {getPriorityBadge(goal.priority)}
                            {getStatusBadge(goal.status, 'goal')}
                          </div>
                        </div>
                        <div className="text-right space-y-2">
                          <div className="text-sm text-muted-foreground">
                            Assigned to: {goal.employee.firstName} {goal.employee.lastName}
                          </div>
                          <div className="flex items-center text-sm text-muted-foreground">
                            <Calendar className="h-4 w-4 mr-1" />
                            Due: {new Date(goal.targetDate).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span>Progress</span>
                          <span>{goal.progress}%</span>
                        </div>
                        <Progress value={goal.progress} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="feedback" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Feedback Requests</h2>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Request Feedback
            </Button>
          </div>

          {feedbackLoading ? (
            <div className="flex items-center justify-center h-[300px]">
              <LoadingSpinner />
            </div>
          ) : (
            <div className="grid gap-4">
              {feedbackRequests?.map((request: FeedbackRequest) => (
                <Card key={request.id}>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={request.employee.profilePicture} />
                          <AvatarFallback>
                            {request.employee.firstName[0]}{request.employee.lastName[0]}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="font-semibold">
                            Feedback for {request.employee.firstName} {request.employee.lastName}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            Requested by: {request.requester.firstName} {request.requester.lastName}
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="outline">{request.type}</Badge>
                            {getStatusBadge(request.status, 'feedback')}
                          </div>
                        </div>
                      </div>
                      <div className="text-right space-y-1">
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Clock className="h-4 w-4 mr-1" />
                          Due: {new Date(request.dueDate).toLocaleDateString()}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Created: {new Date(request.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Performance Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                  Performance trend chart would go here
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Goal Completion Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                  Goal completion chart would go here
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
