apiVersion: v1
kind: Namespace
metadata:
  name: peoplenest
  labels:
    name: peoplenest
    app.kubernetes.io/name: peoplenest
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/component: namespace
    app.kubernetes.io/part-of: peoplenest-hrms
    app.kubernetes.io/managed-by: kubernetes
  annotations:
    description: "PeopleNest HRMS Application Namespace"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: peoplenest-quota
  namespace: peoplenest
spec:
  hard:
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: peoplenest-limits
  namespace: peoplenest
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "512Mi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim
