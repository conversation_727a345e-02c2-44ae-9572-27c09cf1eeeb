/**
 * User Role Enumeration
 * Defines the 5-tier RBAC system for PeopleNest HRMS
 */
export enum UserRole {
  /**
   * Employee - Basic user with access to personal information and self-service features
   * Permissions: View own profile, submit requests, view payslips, update personal info
   */
  EMPLOYEE = 'employee',

  /**
   * Manager - Team lead with access to team management features
   * Permissions: Employee permissions + manage direct reports, approve requests, view team analytics
   */
  MANAGER = 'manager',

  /**
   * HR - Human Resources personnel with access to HR operations
   * Permissions: Manager permissions + employee lifecycle management, policy management, reporting
   */
  HR = 'hr',

  /**
   * Finance - Finance team with access to payroll and financial operations
   * Permissions: HR permissions + payroll processing, financial reporting, budget management
   */
  FINANCE = 'finance',

  /**
   * Admin - System administrator with full access to system configuration
   * Permissions: All permissions + system configuration, user management, security settings
   */
  ADMIN = 'admin',

  /**
   * Super Admin - Highest level access for system owners
   * Permissions: All permissions + tenant management, system maintenance, audit logs
   */
  SUPER_ADMIN = 'super_admin',
}

/**
 * Role hierarchy mapping for permission inheritance
 */
export const ROLE_HIERARCHY = {
  [UserRole.EMPLOYEE]: [],
  [UserRole.MANAGER]: [UserRole.EMPLOYEE],
  [UserRole.HR]: [UserRole.MANAGER, UserRole.EMPLOYEE],
  [UserRole.FINANCE]: [UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE],
  [UserRole.ADMIN]: [UserRole.FINANCE, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE],
  [UserRole.SUPER_ADMIN]: [UserRole.ADMIN, UserRole.FINANCE, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE],
};

/**
 * Role display names for UI
 */
export const ROLE_DISPLAY_NAMES = {
  [UserRole.EMPLOYEE]: 'Employee',
  [UserRole.MANAGER]: 'Manager',
  [UserRole.HR]: 'HR Personnel',
  [UserRole.FINANCE]: 'Finance Team',
  [UserRole.ADMIN]: 'Administrator',
  [UserRole.SUPER_ADMIN]: 'Super Administrator',
};

/**
 * Role descriptions for documentation
 */
export const ROLE_DESCRIPTIONS = {
  [UserRole.EMPLOYEE]: 'Basic user with access to personal information and self-service features',
  [UserRole.MANAGER]: 'Team lead with access to team management and approval workflows',
  [UserRole.HR]: 'Human Resources personnel with access to employee lifecycle management',
  [UserRole.FINANCE]: 'Finance team with access to payroll and financial operations',
  [UserRole.ADMIN]: 'System administrator with full access to system configuration',
  [UserRole.SUPER_ADMIN]: 'Highest level access for system owners and maintenance',
};
