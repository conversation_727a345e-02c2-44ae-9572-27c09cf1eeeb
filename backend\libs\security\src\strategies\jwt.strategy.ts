import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { AuthService } from '../services/auth.service';
import { JwtPayload } from '../interfaces/jwt-payload.interface';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    private authService: AuthService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
      issuer: 'peoplenest-hrms',
      audience: 'peoplenest-users',
    });
  }

  async validate(payload: JwtPayload) {
    const { sub: userId, email, sessionId, tokenType } = payload;

    // Validate token type
    if (tokenType !== 'access') {
      throw new UnauthorizedException('Invalid token type');
    }

    // Get user from database
    const user = await this.authService.validateUserById(userId);
    
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Check if user is active
    if (user.status !== 'active') {
      throw new UnauthorizedException(`User account is ${user.status}`);
    }

    // Verify email matches
    if (user.email !== email) {
      throw new UnauthorizedException('Token email mismatch');
    }

    // Return user object that will be attached to request
    return {
      id: user.id,
      email: user.email,
      role: user.role,
      status: user.status,
      tenantId: user.tenantId,
      sessionId,
      permissions: user.permissions || [],
    };
  }
}
