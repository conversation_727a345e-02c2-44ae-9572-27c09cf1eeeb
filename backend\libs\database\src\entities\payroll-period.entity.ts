import {
  Entity,
  Column,
  Index,
  OneToMany,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { Payslip } from './payslip.entity';
import { User } from './user.entity';
import { PayrollFrequency, PayrollRunStatus, Currency } from '@app/common/enums/status.enum';

@Entity('payroll_periods')
@Index(['tenantId', 'startDate', 'endDate'])
@Index(['tenantId', 'frequency', 'status'])
@Index(['tenantId', 'payDate'])
export class PayrollPeriod extends TenantAwareEntity {
  @Column({
    type: 'varchar',
    length: 100,
    comment: 'Payroll period name/title',
  })
  name: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Payroll period description',
  })
  description?: string;

  @Column({
    type: 'enum',
    enum: PayrollFrequency,
    comment: 'Payroll frequency',
  })
  frequency: PayrollFrequency;

  @Column({
    type: 'date',
    comment: 'Period start date',
  })
  @Index()
  startDate: Date;

  @Column({
    type: 'date',
    comment: 'Period end date',
  })
  @Index()
  endDate: Date;

  @Column({
    type: 'date',
    comment: 'Pay date',
  })
  @Index()
  payDate: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Cut-off date for timesheet submissions',
  })
  cutoffDate?: Date;

  @Column({
    type: 'enum',
    enum: PayrollRunStatus,
    default: PayrollRunStatus.DRAFT,
    comment: 'Payroll run status',
  })
  @Index()
  status: PayrollRunStatus;

  @Column({
    type: 'enum',
    enum: Currency,
    default: Currency.USD,
    comment: 'Primary currency for this payroll period',
  })
  currency: Currency;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Total gross pay for the period',
  })
  totalGrossPay: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Total net pay for the period',
  })
  totalNetPay: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Total taxes for the period',
  })
  totalTaxes: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Total deductions for the period',
  })
  totalDeductions: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Total benefits for the period',
  })
  totalBenefits: number;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Number of employees in this payroll run',
  })
  employeeCount: number;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Number of processed payslips',
  })
  processedCount: number;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Number of failed payslips',
  })
  failedCount: number;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When payroll calculation started',
  })
  calculationStartedAt?: Date;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When payroll calculation completed',
  })
  calculationCompletedAt?: Date;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When payroll was approved',
  })
  approvedAt?: Date;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who approved the payroll',
  })
  approvedBy?: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When payroll processing started',
  })
  processingStartedAt?: Date;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When payroll processing completed',
  })
  processingCompletedAt?: Date;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who processed the payroll',
  })
  processedBy?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Processing errors and warnings',
  })
  processingErrors?: {
    errors: Array<{
      employeeId: string;
      error: string;
      severity: 'warning' | 'error' | 'critical';
      timestamp: Date;
    }>;
    warnings: Array<{
      employeeId: string;
      warning: string;
      timestamp: Date;
    }>;
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Payroll configuration and settings',
  })
  configuration?: {
    includeInactive?: boolean;
    includeProbationary?: boolean;
    overtimeThreshold?: number;
    roundingRules?: {
      hours?: 'up' | 'down' | 'nearest';
      currency?: 'up' | 'down' | 'nearest';
      precision?: number;
    };
    taxSettings?: {
      federalTaxEnabled?: boolean;
      stateTaxEnabled?: boolean;
      localTaxEnabled?: boolean;
    };
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Payroll statistics and metrics',
  })
  statistics?: {
    averageGrossPay?: number;
    averageNetPay?: number;
    averageHours?: number;
    departmentBreakdown?: Array<{
      departmentId: string;
      departmentName: string;
      employeeCount: number;
      totalGrossPay: number;
      totalNetPay: number;
    }>;
    payrollItemBreakdown?: Array<{
      type: string;
      count: number;
      totalAmount: number;
    }>;
  };

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this payroll period is locked',
  })
  isLocked: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this is a correction run',
  })
  isCorrectionRun: boolean;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Original payroll period ID if this is a correction',
  })
  originalPayrollPeriodId?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Payroll period metadata',
  })
  metadata?: Record<string, any>;

  // Relationships
  @OneToMany(() => Payslip, payslip => payslip.payrollPeriod)
  payslips: Payslip[];

  @ManyToOne(() => User, { eager: false, nullable: true })
  @JoinColumn({ name: 'approved_by' })
  approver?: User;

  @ManyToOne(() => User, { eager: false, nullable: true })
  @JoinColumn({ name: 'processed_by' })
  processor?: User;

  @ManyToOne(() => PayrollPeriod, { nullable: true })
  @JoinColumn({ name: 'original_payroll_period_id' })
  originalPayrollPeriod?: PayrollPeriod;

  // Virtual properties
  get duration(): number {
    return Math.ceil(
      (this.endDate.getTime() - this.startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
  }

  get isActive(): boolean {
    const now = new Date();
    return this.startDate <= now && this.endDate >= now;
  }

  get isPast(): boolean {
    return this.endDate < new Date();
  }

  get isFuture(): boolean {
    return this.startDate > new Date();
  }

  get isProcessable(): boolean {
    return [
      PayrollRunStatus.DRAFT,
      PayrollRunStatus.CALCULATED,
      PayrollRunStatus.APPROVED,
    ].includes(this.status);
  }

  get isCompleted(): boolean {
    return [
      PayrollRunStatus.PROCESSED,
      PayrollRunStatus.COMPLETED,
    ].includes(this.status);
  }

  get completionPercentage(): number {
    if (this.employeeCount === 0) return 0;
    return Math.round((this.processedCount / this.employeeCount) * 100);
  }

  get averageProcessingTime(): number | null {
    if (!this.calculationStartedAt || !this.calculationCompletedAt) return null;
    return this.calculationCompletedAt.getTime() - this.calculationStartedAt.getTime();
  }

  // Methods
  canBeApproved(): boolean {
    return this.status === PayrollRunStatus.CALCULATED && this.failedCount === 0;
  }

  canBeProcessed(): boolean {
    return this.status === PayrollRunStatus.APPROVED;
  }

  canBeCancelled(): boolean {
    return ![
      PayrollRunStatus.PROCESSING,
      PayrollRunStatus.PROCESSED,
      PayrollRunStatus.COMPLETED,
    ].includes(this.status);
  }

  @BeforeInsert()
  @BeforeUpdate()
  validateDates(): void {
    if (this.startDate >= this.endDate) {
      throw new Error('Start date must be before end date');
    }
    if (this.payDate < this.endDate) {
      throw new Error('Pay date must be on or after end date');
    }
    if (this.cutoffDate && this.cutoffDate > this.endDate) {
      throw new Error('Cutoff date must be on or before end date');
    }
  }
}
