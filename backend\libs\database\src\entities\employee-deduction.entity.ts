import {
  Entity,
  Column,
  Index,
  ManyTo<PERSON>ne,
  Join<PERSON><PERSON>um<PERSON>,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { Employee } from './employee.entity';
import { Deduction } from './deduction.entity';
import { Currency } from '@app/common/enums/status.enum';

export enum DeductionStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

@Entity('employee_deductions')
@Index(['tenantId', 'employeeId', 'deductionId'], { unique: true })
@Index(['tenantId', 'status'])
@Index(['tenantId', 'startDate'])
export class EmployeeDeduction extends TenantAwareEntity {
  @Column({
    type: 'uuid',
    comment: 'Employee ID',
  })
  employeeId: string;

  @Column({
    type: 'uuid',
    comment: 'Deduction ID',
  })
  deductionId: string;

  @Column({
    type: 'enum',
    enum: DeductionStatus,
    default: DeductionStatus.ACTIVE,
    comment: 'Deduction status',
  })
  @Index()
  status: DeductionStatus;

  @Column({
    type: 'date',
    comment: 'Deduction start date',
  })
  @Index()
  startDate: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Deduction end date',
  })
  endDate?: Date;

  @Column({
    type: 'enum',
    enum: Currency,
    default: Currency.USD,
    comment: 'Currency for deduction amounts',
  })
  currency: Currency;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Custom deduction amount (overrides default)',
  })
  customAmount?: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 4,
    nullable: true,
    comment: 'Custom deduction percentage (overrides default)',
  })
  customPercentage?: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Current deduction amount per pay period',
  })
  currentAmount: number;

  @Column({
    type: 'varchar',
    length: 50,
    default: 'monthly',
    comment: 'Deduction frequency',
  })
  frequency: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Total amount to be deducted (for fixed-term deductions)',
  })
  totalAmount?: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Amount deducted to date',
  })
  deductedToDate: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Remaining amount to be deducted',
  })
  remainingAmount?: number;

  @Column({
    type: 'int',
    nullable: true,
    comment: 'Number of payments (for installment deductions)',
  })
  numberOfPayments?: number;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Number of payments made',
  })
  paymentsMade: number;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this is a pre-tax deduction',
  })
  isPreTax: boolean;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Priority order for deduction processing',
  })
  priority: number;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Year-to-date deduction totals',
  })
  ytdTotals?: {
    amount: number;
    count: number;
    lastUpdated: Date;
  };

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Reference number for external tracking',
  })
  referenceNumber?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Court case number (for garnishments)',
  })
  courtCaseNumber?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Attorney or agency contact',
  })
  agencyContact?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Garnishment details and compliance info',
  })
  garnishmentDetails?: {
    garnishmentType?: string;
    courtOrderDate?: Date;
    maxPercentage?: number;
    priorityLevel?: number;
    disposableIncomeBase?: string;
    exemptAmount?: number;
    requiredDocuments?: string[];
  };

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who authorized the deduction',
  })
  authorizedBy?: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When the deduction was authorized',
  })
  authorizedAt?: Date;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who approved the deduction',
  })
  approvedBy?: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When the deduction was approved',
  })
  approvedAt?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Reason for the deduction',
  })
  reason?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Deduction history and changes',
  })
  deductionHistory?: Array<{
    action: string;
    date: Date;
    previousAmount?: number;
    newAmount?: number;
    reason?: string;
    processedBy?: string;
  }>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Payment schedule for installment deductions',
  })
  paymentSchedule?: Array<{
    paymentNumber: number;
    dueDate: Date;
    amount: number;
    status: 'pending' | 'processed' | 'failed';
    processedDate?: Date;
  }>;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this deduction is suspended',
  })
  isSuspended: boolean;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Date when deduction was suspended',
  })
  suspendedDate?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Reason for suspension',
  })
  suspensionReason?: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Date when deduction was completed',
  })
  completedDate?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Additional notes or comments',
  })
  notes?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Employee deduction metadata',
  })
  metadata?: Record<string, any>;

  // Relationships
  @ManyToOne(() => Employee, employee => employee.employeeDeductions, { eager: false })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;

  @ManyToOne(() => Deduction, deduction => deduction.employeeDeductions, { eager: false })
  @JoinColumn({ name: 'deduction_id' })
  deduction: Deduction;

  // Virtual properties
  get isActive(): boolean {
    return this.status === DeductionStatus.ACTIVE && !this.isSuspended;
  }

  get isCompleted(): boolean {
    return this.status === DeductionStatus.COMPLETED;
  }

  get isCurrentlyEffective(): boolean {
    const now = new Date();
    const startOk = this.startDate <= now;
    const endOk = !this.endDate || this.endDate >= now;
    return startOk && endOk && this.isActive;
  }

  get progressPercentage(): number {
    if (!this.totalAmount || this.totalAmount === 0) return 0;
    return Math.min(100, (this.deductedToDate / this.totalAmount) * 100);
  }

  get isInstallmentDeduction(): boolean {
    return this.numberOfPayments !== null && this.numberOfPayments > 0;
  }

  get isGarnishment(): boolean {
    return this.garnishmentDetails !== null;
  }

  get daysUntilEnd(): number | null {
    if (!this.endDate) return null;
    const diff = this.endDate.getTime() - new Date().getTime();
    return Math.ceil(diff / (1000 * 60 * 60 * 24));
  }

  // Methods
  calculateCurrentAmount(salary: number): number {
    if (this.isSuspended || !this.isCurrentlyEffective) {
      return 0;
    }

    let amount = this.deduction.calculateDeduction(salary, this.customAmount, this.customPercentage);

    // For installment deductions, check if we've reached the limit
    if (this.isInstallmentDeduction && this.totalAmount) {
      const remaining = this.totalAmount - this.deductedToDate;
      amount = Math.min(amount, remaining);
    }

    // For garnishments, apply compliance limits
    if (this.isGarnishment && this.garnishmentDetails?.maxPercentage) {
      const maxAllowed = (salary * this.garnishmentDetails.maxPercentage) / 100;
      amount = Math.min(amount, maxAllowed);
    }

    return Math.max(0, amount);
  }

  processDeduction(amount: number): void {
    this.deductedToDate += amount;
    this.paymentsMade += 1;

    // Update remaining amount for installment deductions
    if (this.totalAmount) {
      this.remainingAmount = Math.max(0, this.totalAmount - this.deductedToDate);
    }

    // Update YTD totals
    this.updateYtdTotals(amount);

    // Check if deduction is completed
    if (this.isInstallmentDeduction) {
      if (this.numberOfPayments && this.paymentsMade >= this.numberOfPayments) {
        this.complete('Maximum payments reached');
      } else if (this.totalAmount && this.deductedToDate >= this.totalAmount) {
        this.complete('Total amount reached');
      }
    }

    // Add to history
    this.addToHistory('processed', {
      amount,
      deductedToDate: this.deductedToDate,
      paymentsMade: this.paymentsMade,
    });
  }

  suspend(reason: string, suspendedDate?: Date): void {
    this.isSuspended = true;
    this.status = DeductionStatus.SUSPENDED;
    this.suspendedDate = suspendedDate || new Date();
    this.suspensionReason = reason;

    this.addToHistory('suspended', {
      reason,
      suspendedDate: this.suspendedDate,
    });
  }

  resume(reason?: string): void {
    this.isSuspended = false;
    this.status = DeductionStatus.ACTIVE;
    this.suspendedDate = null;
    this.suspensionReason = null;

    this.addToHistory('resumed', {
      reason: reason || 'Deduction resumed',
    });
  }

  complete(reason?: string): void {
    this.status = DeductionStatus.COMPLETED;
    this.completedDate = new Date();
    this.endDate = this.completedDate;

    this.addToHistory('completed', {
      reason: reason || 'Deduction completed',
      completedDate: this.completedDate,
    });
  }

  cancel(reason: string): void {
    this.status = DeductionStatus.CANCELLED;
    this.endDate = new Date();

    this.addToHistory('cancelled', {
      reason,
      cancelledDate: this.endDate,
    });
  }

  updateAmount(newAmount: number, reason: string, updatedBy: string): void {
    const previousAmount = this.customAmount || this.currentAmount;
    this.customAmount = newAmount;
    this.currentAmount = newAmount;

    this.addToHistory('amount_updated', {
      previousAmount,
      newAmount,
      reason,
      updatedBy,
    });
  }

  private updateYtdTotals(amount: number): void {
    if (!this.ytdTotals) {
      this.ytdTotals = {
        amount: 0,
        count: 0,
        lastUpdated: new Date(),
      };
    }

    this.ytdTotals.amount += amount;
    this.ytdTotals.count += 1;
    this.ytdTotals.lastUpdated = new Date();
  }

  private addToHistory(action: string, details: Record<string, any>): void {
    if (!this.deductionHistory) {
      this.deductionHistory = [];
    }

    this.deductionHistory.push({
      action,
      date: new Date(),
      ...details,
    });
  }

  @BeforeInsert()
  @BeforeUpdate()
  validateEmployeeDeduction(): void {
    if (this.startDate && this.endDate) {
      if (this.startDate > this.endDate) {
        throw new Error('Start date must be before end date');
      }
    }

    if (this.totalAmount && this.deductedToDate > this.totalAmount) {
      throw new Error('Deducted amount cannot exceed total amount');
    }

    if (this.numberOfPayments && this.paymentsMade > this.numberOfPayments) {
      throw new Error('Payments made cannot exceed number of payments');
    }

    // Calculate remaining amount for installment deductions
    if (this.totalAmount) {
      this.remainingAmount = Math.max(0, this.totalAmount - this.deductedToDate);
    }

    // Validate garnishment compliance
    if (this.isGarnishment && this.garnishmentDetails?.maxPercentage) {
      if (this.garnishmentDetails.maxPercentage > 100) {
        throw new Error('Garnishment percentage cannot exceed 100%');
      }
    }
  }
}
