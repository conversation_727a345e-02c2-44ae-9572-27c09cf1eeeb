import {
  Entity,
  Column,
  Index,
  OneToMany,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { User } from './user.entity';
import { Employee } from './employee.entity';
import { Currency, Country, TimeZone } from '@app/common/enums/status.enum';

export enum TenantStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  TRIAL = 'trial',
  EXPIRED = 'expired',
}

export enum TenantPlan {
  STARTER = 'starter',
  PROFESSIONAL = 'professional',
  ENTERPRISE = 'enterprise',
  CUSTOM = 'custom',
}

@Entity('tenants')
@Index(['subdomain'], { unique: true })
@Index(['domain'], { unique: true, where: 'domain IS NOT NULL' })
export class Tenant extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Tenant name/company name',
  })
  name: string;

  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: 'Subdomain for tenant access',
  })
  @Index()
  subdomain: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Custom domain for tenant',
  })
  domain?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Tenant description',
  })
  description?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Company logo URL',
  })
  logoUrl?: string;

  @Column({
    type: 'enum',
    enum: TenantStatus,
    default: TenantStatus.TRIAL,
    comment: 'Tenant account status',
  })
  @Index()
  status: TenantStatus;

  @Column({
    type: 'enum',
    enum: TenantPlan,
    default: TenantPlan.STARTER,
    comment: 'Subscription plan',
  })
  plan: TenantPlan;

  @Column({
    type: 'enum',
    enum: Currency,
    default: Currency.USD,
    comment: 'Primary currency for the tenant',
  })
  primaryCurrency: Currency;

  @Column({
    type: 'enum',
    enum: Country,
    default: Country.US,
    comment: 'Primary country for the tenant',
  })
  primaryCountry: Country;

  @Column({
    type: 'enum',
    enum: TimeZone,
    default: TimeZone.UTC,
    comment: 'Default timezone for the tenant',
  })
  defaultTimezone: TimeZone;

  @Column({
    type: 'varchar',
    length: 10,
    default: 'en',
    comment: 'Default language',
  })
  defaultLanguage: string;

  @Column({
    type: 'int',
    default: 100,
    comment: 'Maximum number of employees allowed',
  })
  maxEmployees: number;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Current number of active employees',
  })
  currentEmployees: number;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Trial expiry date',
  })
  trialExpiresAt?: Date;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Subscription expiry date',
  })
  subscriptionExpiresAt?: Date;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Tenant-specific configuration settings',
  })
  settings?: Record<string, any>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Feature flags for the tenant',
  })
  features?: Record<string, boolean>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Billing information',
  })
  billingInfo?: {
    companyName?: string;
    address?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
    taxId?: string;
    billingEmail?: string;
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Contact information',
  })
  contactInfo?: {
    primaryContactName?: string;
    primaryContactEmail?: string;
    primaryContactPhone?: string;
    supportEmail?: string;
    supportPhone?: string;
  };

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether the tenant is active',
  })
  isActive: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether the tenant has been verified',
  })
  isVerified: boolean;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Database schema name for this tenant',
  })
  schemaName?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Encryption key for tenant data',
  })
  encryptionKey?: string;

  // Relationships
  @OneToMany(() => User, user => user.tenant)
  users: User[];

  @OneToMany(() => Employee, employee => employee.tenant)
  employees: Employee[];

  // Virtual properties
  get isTrialExpired(): boolean {
    return this.trialExpiresAt ? this.trialExpiresAt < new Date() : false;
  }

  get isSubscriptionExpired(): boolean {
    return this.subscriptionExpiresAt ? this.subscriptionExpiresAt < new Date() : false;
  }

  get canAddEmployees(): boolean {
    return this.currentEmployees < this.maxEmployees;
  }

  get employeeUsagePercentage(): number {
    return this.maxEmployees > 0 ? (this.currentEmployees / this.maxEmployees) * 100 : 0;
  }

  @BeforeInsert()
  @BeforeUpdate()
  normalizeSubdomain() {
    if (this.subdomain) {
      this.subdomain = this.subdomain.toLowerCase().trim();
    }
  }

  @BeforeInsert()
  setDefaults() {
    if (!this.schemaName) {
      this.schemaName = `tenant_${this.subdomain}`;
    }
  }
}
