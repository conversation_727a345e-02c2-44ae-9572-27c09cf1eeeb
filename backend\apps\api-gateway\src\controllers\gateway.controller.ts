import {
  <PERSON>,
  All,
  Req,
  Res,
  HttpException,
  HttpStatus,
  Logger,
  UseGuards,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@app/security';
import { Public } from '@app/common';
import { ApiGatewayService, RoutingRule } from '../services/api-gateway.service';
import { ServiceDiscoveryService } from '../services/service-discovery.service';
import { CircuitBreakerMiddleware } from '../middleware/circuit-breaker.middleware';
import { MonitoringService } from '../services/monitoring.service';
import { HealthCheckService } from '../services/health-check.service';

@ApiTags('Gateway')
@Controller()
export class GatewayController {
  private readonly logger = new Logger(GatewayController.name);

  constructor(
    private readonly gatewayService: ApiGatewayService,
    private readonly serviceDiscovery: ServiceDiscoveryService,
    private readonly circuitBreaker: CircuitBreakerMiddleware,
    private readonly monitoringService: MonitoringService,
    private readonly healthCheckService: HealthCheckService,
  ) {}

  /**
   * Proxy all requests to appropriate microservices
   */
  @All('*')
  @ApiOperation({ 
    summary: 'Proxy requests to microservices',
    description: 'Routes requests to appropriate microservices based on path patterns'
  })
  async proxyRequest(@Req() req: Request, @Res() res: Response): Promise<void> {
    try {
      const proxyRequest = {
        method: req.method,
        url: req.url,
        headers: req.headers as Record<string, string>,
        body: req.body,
        query: req.query as Record<string, string>,
        params: req.params,
      };

      const response = await this.gatewayService.proxyRequest(proxyRequest);

      // Set response headers
      Object.entries(response.headers).forEach(([key, value]) => {
        if (typeof value === 'string') {
          res.setHeader(key, value);
        }
      });

      // Add gateway-specific headers
      res.setHeader('X-Gateway-Duration', response.duration.toString());
      res.setHeader('X-Gateway-Version', '1.0.0');

      res.status(response.status).json(response.data);

    } catch (error) {
      this.logger.error(`Gateway proxy error: ${error.message}`, error.stack);

      if (error instanceof HttpException) {
        const status = error.getStatus();
        const response = error.getResponse();
        res.status(status).json(response);
      } else {
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          message: 'Internal gateway error',
          error: 'Gateway Error',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        });
      }
    }
  }

  /**
   * Get gateway status and health
   */
  @Public()
  @Get('_gateway/status')
  @ApiOperation({ 
    summary: 'Get gateway status',
    description: 'Returns the current status of the API gateway and connected services'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Gateway status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        version: { type: 'string', example: '1.0.0' },
        uptime: { type: 'number', example: 3600 },
        services: { type: 'object' },
        circuitBreakers: { type: 'object' },
        statistics: { type: 'object' }
      }
    }
  })
  async getGatewayStatus() {
    try {
      const systemHealth = await this.healthCheckService.performHealthCheck();
      const services = this.serviceDiscovery.getRegisteredServices();
      const circuitBreakerStatus = this.circuitBreaker.getStatus();

      return {
        ...systemHealth,
        gateway: {
          services: {
            total: services.length,
            healthy: services.filter(s => s.status === 'healthy').length,
            registered: services,
          },
          circuitBreakers: circuitBreakerStatus,
          statistics: this.gatewayService.getStatistics(),
        },
      };
    } catch (error) {
      this.logger.error('Failed to get gateway status:', error);
      return {
        status: 'unhealthy',
        version: '1.0.0',
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        services: this.serviceDiscovery.getMetrics(),
        circuitBreakers: this.circuitBreaker.getCircuitStatus(),
        statistics: this.gatewayService.getStatistics(),
      };
    }
  }

  /**
   * Get service discovery information
   */
  @Public()
  @Get('_gateway/services')
  @ApiOperation({ 
    summary: 'Get registered services',
    description: 'Returns information about all registered microservices'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Services information retrieved successfully'
  })
  getServices() {
    return {
      services: this.serviceDiscovery.getAllServices(),
      metrics: this.serviceDiscovery.getMetrics(),
    };
  }

  /**
   * Force health check for a specific service
   */
  @UseGuards(JwtAuthGuard)
  @Post('_gateway/services/:serviceName/health-check')
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Force health check',
    description: 'Triggers an immediate health check for the specified service'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Health check completed successfully'
  })
  async forceHealthCheck(@Param('serviceName') serviceName: string) {
    await this.serviceDiscovery.forceHealthCheck(serviceName);
    return {
      message: `Health check completed for service: ${serviceName}`,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get circuit breaker status
   */
  @Public()
  @Get('_gateway/circuit-breakers')
  @ApiOperation({ 
    summary: 'Get circuit breaker status',
    description: 'Returns the current status of all circuit breakers'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Circuit breaker status retrieved successfully'
  })
  getCircuitBreakers() {
    return {
      circuitBreakers: this.circuitBreaker.getCircuitStatus(),
      metrics: this.circuitBreaker.getMetrics(),
    };
  }

  /**
   * Reset circuit breaker for a specific service
   */
  @UseGuards(JwtAuthGuard)
  @Post('_gateway/circuit-breakers/:serviceName/reset')
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Reset circuit breaker',
    description: 'Resets the circuit breaker for the specified service'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Circuit breaker reset successfully'
  })
  resetCircuitBreaker(@Param('serviceName') serviceName: string) {
    this.circuitBreaker.resetCircuit(serviceName);
    return {
      message: `Circuit breaker reset for service: ${serviceName}`,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get routing configuration
   */
  @UseGuards(JwtAuthGuard)
  @Get('_gateway/routing')
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Get routing configuration',
    description: 'Returns the current routing rules configuration'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Routing configuration retrieved successfully'
  })
  getRoutingConfiguration() {
    return {
      rules: this.gatewayService.getRoutingConfiguration(),
      totalRules: this.gatewayService.getRoutingConfiguration().length,
    };
  }

  /**
   * Add new routing rule
   */
  @UseGuards(JwtAuthGuard)
  @Post('_gateway/routing')
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Add routing rule',
    description: 'Adds a new routing rule to the gateway configuration'
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Routing rule added successfully'
  })
  addRoutingRule(@Body() rule: RoutingRule) {
    this.gatewayService.addRoutingRule(rule);
    return {
      message: 'Routing rule added successfully',
      rule,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Update routing rule
   */
  @UseGuards(JwtAuthGuard)
  @Put('_gateway/routing/:path')
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Update routing rule',
    description: 'Updates an existing routing rule'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Routing rule updated successfully'
  })
  updateRoutingRule(
    @Param('path') path: string,
    @Body() updates: Partial<RoutingRule>
  ) {
    const decodedPath = decodeURIComponent(path);
    this.gatewayService.updateRoutingRule(decodedPath, updates);
    return {
      message: 'Routing rule updated successfully',
      path: decodedPath,
      updates,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Delete routing rule
   */
  @UseGuards(JwtAuthGuard)
  @Delete('_gateway/routing/:path')
  @ApiBearerAuth()
  @ApiOperation({ 
    summary: 'Delete routing rule',
    description: 'Removes a routing rule from the gateway configuration'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Routing rule deleted successfully'
  })
  deleteRoutingRule(@Param('path') path: string) {
    const decodedPath = decodeURIComponent(path);
    this.gatewayService.removeRoutingRule(decodedPath);
    return {
      message: 'Routing rule deleted successfully',
      path: decodedPath,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Get monitoring dashboard data
   */
  @Get('_gateway/dashboard')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get monitoring dashboard data' })
  @ApiResponse({ status: 200, description: 'Dashboard data retrieved successfully' })
  async getDashboardData(): Promise<any> {
    try {
      return this.monitoringService.getDashboardData();
    } catch (error) {
      this.logger.error('Failed to get dashboard data:', error);
      throw new HttpException(
        'Failed to get dashboard data',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get Prometheus metrics
   */
  @Get('_gateway/metrics/prometheus')
  @Public()
  @ApiOperation({ summary: 'Get Prometheus-formatted metrics' })
  @ApiResponse({ status: 200, description: 'Prometheus metrics', content: { 'text/plain': {} } })
  async getPrometheusMetrics(): Promise<string> {
    try {
      return this.monitoringService.getPrometheusMetrics();
    } catch (error) {
      this.logger.error('Failed to get Prometheus metrics:', error);
      throw new HttpException(
        'Failed to get metrics',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get active alerts
   */
  @Get('_gateway/alerts')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get active alerts' })
  @ApiResponse({ status: 200, description: 'Active alerts retrieved successfully' })
  async getActiveAlerts(): Promise<any> {
    try {
      return {
        alerts: this.monitoringService.getActiveAlerts(),
        rules: this.monitoringService.getAlertRules(),
      };
    } catch (error) {
      this.logger.error('Failed to get alerts:', error);
      throw new HttpException(
        'Failed to get alerts',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get gateway metrics for monitoring
   */
  @Public()
  @Get('_gateway/metrics')
  @ApiOperation({
    summary: 'Get gateway metrics',
    description: 'Returns metrics for monitoring and observability'
  })
  @ApiResponse({
    status: 200,
    description: 'Gateway metrics retrieved successfully'
  })
  getMetrics() {
    return {
      gateway: this.gatewayService.getStatistics(),
      services: this.serviceDiscovery.getMetrics(),
      circuitBreakers: this.circuitBreaker.getMetrics(),
      monitoring: this.monitoringService.getDashboardData(),
      timestamp: new Date().toISOString(),
    };
  }
}
