import { Controller, Get, UseGuards, Request } from '@nestjs/common';
import { Api<PERSON><PERSON><PERSON>, ApiBearerAuth } from '@nestjs/swagger';

import { JwtAuthGuard, RolesGuard } from '@app/security/guards';
import { Roles } from '@app/security/decorators/roles.decorator';
import { UserRole } from '@app/common/enums/status.enum';
import { RequestWithUser } from '@app/security/interfaces/request-with-user.interface';

import { DepartmentService } from '../services/department.service';

@ApiTags('Departments')
@Controller('departments')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class DepartmentController {
  constructor(private readonly departmentService: DepartmentService) {}

  @Get()
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.MANAGER, UserRole.EMPLOYEE, UserRole.SUPER_ADMIN)
  async findAll(@Request() req: RequestWithUser) {
    return this.departmentService.findAll(req.user.tenantId);
  }
}
