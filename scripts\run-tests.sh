#!/bin/bash

# PeopleNest HRMS - Comprehensive Test Runner
# This script runs all tests for the PeopleNest HRMS application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to run backend tests
run_backend_tests() {
    print_status "Running backend tests..."
    
    cd backend
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_status "Installing backend dependencies..."
        npm install
    fi
    
    # Run unit tests
    print_status "Running backend unit tests..."
    npm run test
    
    # Run integration tests
    print_status "Running backend integration tests..."
    npm run test:e2e
    
    # Generate coverage report
    print_status "Generating backend coverage report..."
    npm run test:cov
    
    cd ..
    print_success "Backend tests completed"
}

# Function to run frontend tests
run_frontend_tests() {
    print_status "Running frontend tests..."
    
    cd frontend
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_status "Installing frontend dependencies..."
        npm install
    fi
    
    # Run unit tests
    print_status "Running frontend unit tests..."
    npm run test
    
    # Generate coverage report
    print_status "Generating frontend coverage report..."
    npm run test:coverage
    
    cd ..
    print_success "Frontend tests completed"
}

# Function to run E2E tests
run_e2e_tests() {
    print_status "Running E2E tests..."
    
    cd frontend
    
    # Install Playwright if needed
    if [ ! -d "node_modules/@playwright" ]; then
        print_status "Installing Playwright..."
        npx playwright install
    fi
    
    # Run E2E tests
    print_status "Running Playwright E2E tests..."
    npm run test:e2e
    
    cd ..
    print_success "E2E tests completed"
}

# Function to run security tests
run_security_tests() {
    print_status "Running security tests..."
    
    # Backend security audit
    cd backend
    print_status "Running backend security audit..."
    npm audit --audit-level=moderate || print_warning "Backend security audit found issues"
    
    # Frontend security audit
    cd ../frontend
    print_status "Running frontend security audit..."
    npm audit --audit-level=moderate || print_warning "Frontend security audit found issues"
    
    cd ..
    print_success "Security tests completed"
}

# Function to run performance tests
run_performance_tests() {
    print_status "Running performance tests..."
    
    # Check if k6 is installed for load testing
    if command_exists k6; then
        print_status "Running load tests with k6..."
        k6 run tests/performance/load-test.js || print_warning "Load tests not found or failed"
    else
        print_warning "k6 not installed, skipping load tests"
    fi
    
    print_success "Performance tests completed"
}

# Function to generate test reports
generate_reports() {
    print_status "Generating test reports..."
    
    # Create reports directory
    mkdir -p reports
    
    # Combine coverage reports
    print_status "Combining coverage reports..."
    
    # Backend coverage
    if [ -f "backend/coverage/lcov.info" ]; then
        cp backend/coverage/lcov.info reports/backend-coverage.lcov
    fi
    
    # Frontend coverage
    if [ -f "frontend/coverage/lcov.info" ]; then
        cp frontend/coverage/lcov.info reports/frontend-coverage.lcov
    fi
    
    # E2E test results
    if [ -f "frontend/test-results/results.json" ]; then
        cp frontend/test-results/results.json reports/e2e-results.json
    fi
    
    print_success "Test reports generated in reports/ directory"
}

# Function to check coverage thresholds
check_coverage() {
    print_status "Checking coverage thresholds..."
    
    # Backend coverage check (85% threshold)
    if [ -f "backend/coverage/coverage-summary.json" ]; then
        backend_coverage=$(node -e "
            const coverage = require('./backend/coverage/coverage-summary.json');
            const total = coverage.total;
            console.log(Math.min(total.lines.pct, total.functions.pct, total.branches.pct, total.statements.pct));
        ")
        
        if (( $(echo "$backend_coverage >= 85" | bc -l) )); then
            print_success "Backend coverage: ${backend_coverage}% (meets 85% threshold)"
        else
            print_error "Backend coverage: ${backend_coverage}% (below 85% threshold)"
            exit 1
        fi
    fi
    
    # Frontend coverage check (80% threshold)
    if [ -f "frontend/coverage/coverage-summary.json" ]; then
        frontend_coverage=$(node -e "
            const coverage = require('./frontend/coverage/coverage-summary.json');
            const total = coverage.total;
            console.log(Math.min(total.lines.pct, total.functions.pct, total.branches.pct, total.statements.pct));
        ")
        
        if (( $(echo "$frontend_coverage >= 80" | bc -l) )); then
            print_success "Frontend coverage: ${frontend_coverage}% (meets 80% threshold)"
        else
            print_error "Frontend coverage: ${frontend_coverage}% (below 80% threshold)"
            exit 1
        fi
    fi
}

# Main execution
main() {
    print_status "Starting PeopleNest HRMS Test Suite"
    print_status "=================================="
    
    # Parse command line arguments
    SKIP_BACKEND=false
    SKIP_FRONTEND=false
    SKIP_E2E=false
    SKIP_SECURITY=false
    SKIP_PERFORMANCE=false
    COVERAGE_ONLY=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-backend)
                SKIP_BACKEND=true
                shift
                ;;
            --skip-frontend)
                SKIP_FRONTEND=true
                shift
                ;;
            --skip-e2e)
                SKIP_E2E=true
                shift
                ;;
            --skip-security)
                SKIP_SECURITY=true
                shift
                ;;
            --skip-performance)
                SKIP_PERFORMANCE=true
                shift
                ;;
            --coverage-only)
                COVERAGE_ONLY=true
                shift
                ;;
            --help)
                echo "Usage: $0 [options]"
                echo "Options:"
                echo "  --skip-backend      Skip backend tests"
                echo "  --skip-frontend     Skip frontend tests"
                echo "  --skip-e2e          Skip E2E tests"
                echo "  --skip-security     Skip security tests"
                echo "  --skip-performance  Skip performance tests"
                echo "  --coverage-only     Only check coverage, don't run tests"
                echo "  --help              Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Check if we're only checking coverage
    if [ "$COVERAGE_ONLY" = true ]; then
        check_coverage
        exit 0
    fi
    
    # Run tests based on flags
    if [ "$SKIP_BACKEND" = false ]; then
        run_backend_tests
    fi
    
    if [ "$SKIP_FRONTEND" = false ]; then
        run_frontend_tests
    fi
    
    if [ "$SKIP_E2E" = false ]; then
        run_e2e_tests
    fi
    
    if [ "$SKIP_SECURITY" = false ]; then
        run_security_tests
    fi
    
    if [ "$SKIP_PERFORMANCE" = false ]; then
        run_performance_tests
    fi
    
    # Generate reports and check coverage
    generate_reports
    check_coverage
    
    print_success "All tests completed successfully!"
    print_status "Test reports available in reports/ directory"
}

# Run main function with all arguments
main "$@"
