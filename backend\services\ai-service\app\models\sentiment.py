"""
Pydantic models for sentiment analysis results.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field


class EmotionAnalysis(BaseModel):
    """Emotion analysis results."""
    dominant_emotion: str = Field(..., description="The dominant emotion detected")
    emotion_scores: Dict[str, float] = Field(default_factory=dict, description="Scores for all emotions")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score for emotion detection")


class TopicSentiment(BaseModel):
    """Sentiment analysis for specific topics."""
    topic: str = Field(..., description="The topic name")
    sentiment: str = Field(..., description="Sentiment label for the topic")
    score: float = Field(..., ge=0.0, le=1.0, description="Sentiment score for the topic")
    mentions: int = Field(..., ge=0, description="Number of mentions of this topic")
    sample_text: str = Field(default="", description="Sample text mentioning this topic")


class SentimentResult(BaseModel):
    """Complete sentiment analysis result."""
    text: str = Field(..., description="Original text analyzed")
    cleaned_text: Optional[str] = Field(None, description="Preprocessed text")
    sentiment: str = Field(..., description="Overall sentiment label")
    sentiment_score: float = Field(..., ge=0.0, le=1.0, description="Overall sentiment score")
    sentiment_distribution: Optional[Dict[str, float]] = Field(None, description="Distribution of sentiment scores")
    emotions: Optional[EmotionAnalysis] = Field(None, description="Emotion analysis results")
    topics: Optional[List[TopicSentiment]] = Field(None, description="Topic-based sentiment analysis")
    key_phrases: Optional[List[str]] = Field(None, description="Key phrases extracted from text")
    entities: Optional[List[Dict[str, str]]] = Field(None, description="Named entities found in text")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Overall confidence in the analysis")
    analyzed_at: datetime = Field(default_factory=datetime.utcnow, description="Timestamp of analysis")
    model_version: Optional[str] = Field(None, description="Version of the model used")
    error: Optional[str] = Field(None, description="Error message if analysis failed")


class BatchSentimentResult(BaseModel):
    """Results from batch sentiment analysis."""
    results: List[SentimentResult] = Field(..., description="Individual sentiment analysis results")
    summary: Dict[str, Any] = Field(..., description="Summary statistics")
    processed_count: int = Field(..., description="Number of texts processed")
    error_count: int = Field(..., description="Number of texts that failed processing")
    processing_time: float = Field(..., description="Total processing time in seconds")


class FeedbackInsights(BaseModel):
    """Insights from employee feedback analysis."""
    total_feedback: int = Field(..., description="Total number of feedback items analyzed")
    sentiment_distribution: Dict[str, int] = Field(..., description="Count of each sentiment type")
    average_sentiment_score: float = Field(..., description="Average sentiment score")
    department_sentiment: Dict[str, Dict[str, int]] = Field(..., description="Sentiment by department")
    common_themes: List[tuple] = Field(..., description="Most common themes and their frequencies")
    analysis_summary: str = Field(..., description="Human-readable summary of the analysis")
    recommendations: Optional[List[str]] = Field(None, description="Recommended actions based on analysis")


class WorkplaceIssues(BaseModel):
    """Detected workplace issues from sentiment analysis."""
    risk_score: float = Field(..., ge=0.0, le=100.0, description="Overall workplace risk score")
    negative_sentiment_trend: Dict[str, Any] = Field(..., description="Negative sentiment trend analysis")
    stress_indicators: Dict[str, Any] = Field(..., description="Stress indicator analysis")
    conflict_indicators: Dict[str, Any] = Field(..., description="Conflict indicator analysis")
    burnout_indicators: Dict[str, Any] = Field(..., description="Burnout indicator analysis")
    engagement_issues: Dict[str, Any] = Field(..., description="Engagement issue analysis")
    recommendations: List[str] = Field(..., description="Recommended actions")
    analyzed_communications: int = Field(..., description="Number of communications analyzed")
    analysis_date: datetime = Field(default_factory=datetime.utcnow, description="Date of analysis")


class SentimentTrend(BaseModel):
    """Sentiment trend over time."""
    date: datetime = Field(..., description="Date of the sentiment data point")
    sentiment_score: float = Field(..., description="Average sentiment score for the date")
    sentiment_distribution: Dict[str, float] = Field(..., description="Sentiment distribution for the date")
    sample_size: int = Field(..., description="Number of texts analyzed for this date")


class SentimentAnalysisRequest(BaseModel):
    """Request model for sentiment analysis."""
    text: str = Field(..., min_length=1, description="Text to analyze")
    include_emotions: bool = Field(default=True, description="Whether to include emotion analysis")
    include_topics: bool = Field(default=True, description="Whether to include topic-based sentiment")
    include_entities: bool = Field(default=True, description="Whether to extract named entities")
    language: Optional[str] = Field(default="en", description="Language of the text")


class BatchSentimentAnalysisRequest(BaseModel):
    """Request model for batch sentiment analysis."""
    texts: List[str] = Field(..., min_items=1, description="List of texts to analyze")
    batch_size: int = Field(default=32, ge=1, le=100, description="Batch size for processing")
    include_emotions: bool = Field(default=True, description="Whether to include emotion analysis")
    include_topics: bool = Field(default=True, description="Whether to include topic-based sentiment")
    include_entities: bool = Field(default=True, description="Whether to extract named entities")
    language: Optional[str] = Field(default="en", description="Language of the texts")


class FeedbackAnalysisRequest(BaseModel):
    """Request model for employee feedback analysis."""
    feedback_data: List[Dict[str, Any]] = Field(..., min_items=1, description="Employee feedback data")
    include_department_analysis: bool = Field(default=True, description="Whether to include department-wise analysis")
    include_trend_analysis: bool = Field(default=False, description="Whether to include trend analysis")
    date_range: Optional[Dict[str, datetime]] = Field(None, description="Date range for analysis")


class PerformanceReviewAnalysisRequest(BaseModel):
    """Request model for performance review analysis."""
    reviews: List[Dict[str, Any]] = Field(..., min_items=1, description="Performance review data")
    include_reviewer_analysis: bool = Field(default=True, description="Whether to include reviewer-wise analysis")
    include_rating_correlation: bool = Field(default=True, description="Whether to correlate with numerical ratings")
    period: Optional[str] = Field(None, description="Review period identifier")


class WorkplaceIssueDetectionRequest(BaseModel):
    """Request model for workplace issue detection."""
    communications: List[str] = Field(..., min_items=1, description="Employee communications to analyze")
    detection_sensitivity: float = Field(default=0.5, ge=0.0, le=1.0, description="Sensitivity for issue detection")
    include_recommendations: bool = Field(default=True, description="Whether to include recommendations")
    employee_metadata: Optional[List[Dict[str, Any]]] = Field(None, description="Additional employee metadata")
