{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "tsconfig.build.json"}, "monorepo": true, "root": "apps/api-gateway", "projects": {"api-gateway": {"type": "application", "root": "apps/api-gateway", "entryFile": "main", "sourceRoot": "apps/api-gateway/src", "compilerOptions": {"tsConfigPath": "apps/api-gateway/tsconfig.app.json"}}, "auth-service": {"type": "application", "root": "apps/auth-service", "entryFile": "main", "sourceRoot": "apps/auth-service/src", "compilerOptions": {"tsConfigPath": "apps/auth-service/tsconfig.app.json"}}, "employee-service": {"type": "application", "root": "apps/employee-service", "entryFile": "main", "sourceRoot": "apps/employee-service/src", "compilerOptions": {"tsConfigPath": "apps/employee-service/tsconfig.app.json"}}, "payroll-service": {"type": "application", "root": "apps/payroll-service", "entryFile": "main", "sourceRoot": "apps/payroll-service/src", "compilerOptions": {"tsConfigPath": "apps/payroll-service/tsconfig.app.json"}}, "notification-service": {"type": "application", "root": "apps/notification-service", "entryFile": "main", "sourceRoot": "apps/notification-service/src", "compilerOptions": {"tsConfigPath": "apps/notification-service/tsconfig.app.json"}}, "common": {"type": "library", "root": "libs/common", "entryFile": "index", "sourceRoot": "libs/common/src", "compilerOptions": {"tsConfigPath": "libs/common/tsconfig.lib.json"}}, "database": {"type": "library", "root": "libs/database", "entryFile": "index", "sourceRoot": "libs/database/src", "compilerOptions": {"tsConfigPath": "libs/database/tsconfig.lib.json"}}, "security": {"type": "library", "root": "libs/security", "entryFile": "index", "sourceRoot": "libs/security/src", "compilerOptions": {"tsConfigPath": "libs/security/tsconfig.lib.json"}}}}