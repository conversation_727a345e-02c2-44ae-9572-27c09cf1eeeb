import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { AuditLog } from '@app/database/entities/audit-log.entity';
import { User } from '@app/database/entities/user.entity';
import { Session } from '@app/database/entities/session.entity';
import { AuditService } from './audit.service';
import { SOC2ComplianceService } from './soc2-compliance.service';

export interface SecurityAlert {
  id: string;
  type: 'authentication_failure' | 'suspicious_activity' | 'data_access_anomaly' | 'system_intrusion' | 'policy_violation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  source: string;
  timestamp: Date;
  userId?: string;
  ip?: string;
  userAgent?: string;
  metadata: Record<string, any>;
  status: 'open' | 'investigating' | 'resolved' | 'false_positive';
  assignedTo?: string;
  resolvedAt?: Date;
  resolution?: string;
}

export interface ThreatIndicator {
  type: 'ip' | 'user_agent' | 'email' | 'domain' | 'hash';
  value: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  description: string;
  firstSeen: Date;
  lastSeen: Date;
  occurrences: number;
  blocked: boolean;
}

export interface SecurityMetrics {
  alertsLast24h: number;
  alertsLast7d: number;
  alertsLast30d: number;
  alertsByType: Record<string, number>;
  alertsBySeverity: Record<string, number>;
  failedLogins: number;
  suspiciousActivities: number;
  blockedThreats: number;
  averageResponseTime: number;
  topThreats: ThreatIndicator[];
}

@Injectable()
export class SecurityMonitoringService {
  private readonly logger = new Logger(SecurityMonitoringService.name);
  private readonly alerts = new Map<string, SecurityAlert>();
  private readonly threatIndicators = new Map<string, ThreatIndicator>();
  private readonly suspiciousPatterns = new Map<string, number>();

  // Configurable thresholds
  private readonly failedLoginThreshold: number;
  private readonly suspiciousActivityThreshold: number;
  private readonly dataAccessThreshold: number;
  private readonly sessionAnomalyThreshold: number;

  constructor(
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Session)
    private readonly sessionRepository: Repository<Session>,
    private readonly auditService: AuditService,
    private readonly soc2Service: SOC2ComplianceService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.failedLoginThreshold = this.configService.get<number>('SECURITY_FAILED_LOGIN_THRESHOLD', 5);
    this.suspiciousActivityThreshold = this.configService.get<number>('SECURITY_SUSPICIOUS_ACTIVITY_THRESHOLD', 10);
    this.dataAccessThreshold = this.configService.get<number>('SECURITY_DATA_ACCESS_THRESHOLD', 100);
    this.sessionAnomalyThreshold = this.configService.get<number>('SECURITY_SESSION_ANOMALY_THRESHOLD', 3);
  }

  /**
   * Monitor authentication events for suspicious activity
   */
  async monitorAuthenticationEvents(): Promise<void> {
    const last24Hours = new Date();
    last24Hours.setHours(last24Hours.getHours() - 24);

    // Check for failed login attempts
    const failedLogins = await this.auditLogRepository
      .createQueryBuilder('audit')
      .where('audit.action = :action', { action: 'login_failed' })
      .andWhere('audit.createdAt >= :since', { since: last24Hours })
      .groupBy('audit.ip')
      .select(['audit.ip', 'COUNT(*) as count'])
      .getRawMany();

    for (const login of failedLogins) {
      if (login.count >= this.failedLoginThreshold) {
        await this.createSecurityAlert({
          type: 'authentication_failure',
          severity: login.count >= this.failedLoginThreshold * 2 ? 'high' : 'medium',
          title: 'Multiple Failed Login Attempts',
          description: `${login.count} failed login attempts from IP ${login.ip}`,
          source: 'authentication_monitor',
          ip: login.ip,
          metadata: { failedAttempts: login.count, timeWindow: '24h' },
        });

        // Add IP to threat indicators
        await this.addThreatIndicator({
          type: 'ip',
          value: login.ip,
          severity: 'medium',
          source: 'failed_login_monitor',
          description: `IP with ${login.count} failed login attempts`,
        });
      }
    }

    // Check for unusual login patterns
    await this.detectUnusualLoginPatterns();
  }

  /**
   * Monitor data access patterns for anomalies
   */
  async monitorDataAccessPatterns(): Promise<void> {
    const last24Hours = new Date();
    last24Hours.setHours(last24Hours.getHours() - 24);

    // Check for excessive data access
    const dataAccess = await this.auditLogRepository
      .createQueryBuilder('audit')
      .where('audit.action IN (:...actions)', { 
        actions: ['data_read', 'data_export', 'employee_view', 'payroll_access'] 
      })
      .andWhere('audit.createdAt >= :since', { since: last24Hours })
      .groupBy('audit.userId')
      .select(['audit.userId', 'COUNT(*) as count'])
      .getRawMany();

    for (const access of dataAccess) {
      if (access.count >= this.dataAccessThreshold) {
        const user = await this.userRepository.findOne({
          where: { id: access.userId },
          select: ['email', 'firstName', 'lastName'],
        });

        await this.createSecurityAlert({
          type: 'data_access_anomaly',
          severity: 'high',
          title: 'Excessive Data Access',
          description: `User ${user?.email} accessed ${access.count} records in 24 hours`,
          source: 'data_access_monitor',
          userId: access.userId,
          metadata: { 
            accessCount: access.count, 
            userEmail: user?.email,
            timeWindow: '24h' 
          },
        });
      }
    }

    // Check for unusual data access patterns
    await this.detectUnusualDataAccessPatterns();
  }

  /**
   * Monitor system activities for intrusion attempts
   */
  async monitorSystemIntrusion(): Promise<void> {
    const last1Hour = new Date();
    last1Hour.setHours(last1Hour.getHours() - 1);

    // Check for suspicious API calls
    const suspiciousActivities = await this.auditLogRepository
      .createQueryBuilder('audit')
      .where('audit.level = :level', { level: 'ERROR' })
      .andWhere('audit.createdAt >= :since', { since: last1Hour })
      .andWhere('audit.action LIKE :pattern', { pattern: '%unauthorized%' })
      .getMany();

    for (const activity of suspiciousActivities) {
      await this.createSecurityAlert({
        type: 'system_intrusion',
        severity: 'high',
        title: 'Potential System Intrusion',
        description: `Unauthorized access attempt: ${activity.description}`,
        source: 'intrusion_monitor',
        ip: activity.ip,
        userAgent: activity.userAgent,
        metadata: { 
          auditLogId: activity.id,
          action: activity.action,
          error: activity.error 
        },
      });
    }

    // Check for SQL injection attempts
    await this.detectSQLInjectionAttempts();

    // Check for XSS attempts
    await this.detectXSSAttempts();
  }

  /**
   * Monitor session activities for anomalies
   */
  async monitorSessionAnomalies(): Promise<void> {
    const activeSessions = await this.sessionRepository
      .createQueryBuilder('session')
      .where('session.isActive = :active', { active: true })
      .getMany();

    for (const session of activeSessions) {
      // Check for session hijacking indicators
      const sessionLogs = await this.auditLogRepository
        .createQueryBuilder('audit')
        .where('audit.sessionId = :sessionId', { sessionId: session.id })
        .orderBy('audit.createdAt', 'DESC')
        .limit(10)
        .getMany();

      // Detect IP changes within session
      const uniqueIPs = new Set(sessionLogs.map(log => log.ip).filter(Boolean));
      if (uniqueIPs.size > this.sessionAnomalyThreshold) {
        await this.createSecurityAlert({
          type: 'suspicious_activity',
          severity: 'high',
          title: 'Session Anomaly Detected',
          description: `Session ${session.id} accessed from ${uniqueIPs.size} different IPs`,
          source: 'session_monitor',
          userId: session.userId,
          metadata: { 
            sessionId: session.id,
            uniqueIPs: Array.from(uniqueIPs),
            logCount: sessionLogs.length 
          },
        });
      }

      // Detect unusual user agent changes
      const uniqueUserAgents = new Set(sessionLogs.map(log => log.userAgent).filter(Boolean));
      if (uniqueUserAgents.size > 2) {
        await this.createSecurityAlert({
          type: 'suspicious_activity',
          severity: 'medium',
          title: 'Session User Agent Anomaly',
          description: `Session ${session.id} used ${uniqueUserAgents.size} different user agents`,
          source: 'session_monitor',
          userId: session.userId,
          metadata: { 
            sessionId: session.id,
            uniqueUserAgents: Array.from(uniqueUserAgents) 
          },
        });
      }
    }
  }

  /**
   * Create a security alert
   */
  async createSecurityAlert(alert: Omit<SecurityAlert, 'id' | 'timestamp' | 'status'>): Promise<string> {
    const alertId = `ALERT-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const securityAlert: SecurityAlert = {
      id: alertId,
      timestamp: new Date(),
      status: 'open',
      ...alert,
    };

    this.alerts.set(alertId, securityAlert);

    // Audit the alert creation
    await this.auditService.logUserAction({
      userId: alert.userId || 'system',
      action: 'security_alert_created',
      resource: 'security_alert',
      resourceId: alertId,
      success: true,
      metadata: {
        alertType: alert.type,
        severity: alert.severity,
        title: alert.title,
        source: alert.source,
      },
    });

    // Emit event for high/critical alerts
    if (alert.severity === 'high' || alert.severity === 'critical') {
      this.eventEmitter.emit('security.alert.critical', {
        alertId,
        alert: securityAlert,
      });

      // Auto-create SOC 2 incident for critical alerts
      if (alert.severity === 'critical') {
        await this.soc2Service.reportIncident({
          title: alert.title,
          description: alert.description,
          severity: 'high',
          category: 'unauthorized_access',
          reportedBy: 'security_monitoring_system',
          affectedSystems: [alert.source],
          affectedUsers: alert.userId ? 1 : 0,
          containmentActions: ['Alert created', 'Investigation initiated'],
        });
      }
    }

    this.logger.warn(`Security alert created: ${alertId} - ${alert.title}`);
    return alertId;
  }

  /**
   * Add threat indicator
   */
  async addThreatIndicator(indicator: Omit<ThreatIndicator, 'firstSeen' | 'lastSeen' | 'occurrences' | 'blocked'>): Promise<void> {
    const key = `${indicator.type}:${indicator.value}`;
    const existing = this.threatIndicators.get(key);

    if (existing) {
      existing.lastSeen = new Date();
      existing.occurrences++;
      existing.severity = this.escalateSeverity(existing.severity, indicator.severity);
    } else {
      const threatIndicator: ThreatIndicator = {
        ...indicator,
        firstSeen: new Date(),
        lastSeen: new Date(),
        occurrences: 1,
        blocked: false,
      };
      this.threatIndicators.set(key, threatIndicator);
    }

    // Auto-block high-severity threats
    const currentIndicator = this.threatIndicators.get(key)!;
    if (currentIndicator.severity === 'high' || currentIndicator.severity === 'critical') {
      await this.blockThreat(key);
    }
  }

  /**
   * Block a threat indicator
   */
  async blockThreat(indicatorKey: string): Promise<void> {
    const indicator = this.threatIndicators.get(indicatorKey);
    if (!indicator) {
      throw new Error(`Threat indicator ${indicatorKey} not found`);
    }

    indicator.blocked = true;

    // Audit the threat blocking
    await this.auditService.logUserAction({
      userId: 'system',
      action: 'threat_blocked',
      resource: 'threat_indicator',
      resourceId: indicatorKey,
      success: true,
      metadata: {
        type: indicator.type,
        value: indicator.value,
        severity: indicator.severity,
        occurrences: indicator.occurrences,
      },
    });

    this.logger.warn(`Threat blocked: ${indicatorKey}`);
  }

  /**
   * Get security metrics
   */
  getSecurityMetrics(): SecurityMetrics {
    const now = new Date();
    const last24h = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const last7d = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const last30d = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    const alerts = Array.from(this.alerts.values());
    
    const alertsLast24h = alerts.filter(a => a.timestamp >= last24h).length;
    const alertsLast7d = alerts.filter(a => a.timestamp >= last7d).length;
    const alertsLast30d = alerts.filter(a => a.timestamp >= last30d).length;

    const alertsByType = alerts.reduce((acc, alert) => {
      acc[alert.type] = (acc[alert.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const alertsBySeverity = alerts.reduce((acc, alert) => {
      acc[alert.severity] = (acc[alert.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const failedLogins = alerts.filter(a => a.type === 'authentication_failure').length;
    const suspiciousActivities = alerts.filter(a => a.type === 'suspicious_activity').length;
    const blockedThreats = Array.from(this.threatIndicators.values()).filter(t => t.blocked).length;

    // Calculate average response time
    const resolvedAlerts = alerts.filter(a => a.status === 'resolved' && a.resolvedAt);
    const averageResponseTime = resolvedAlerts.length > 0
      ? resolvedAlerts.reduce((sum, alert) => {
          const responseTime = alert.resolvedAt!.getTime() - alert.timestamp.getTime();
          return sum + responseTime;
        }, 0) / resolvedAlerts.length / (1000 * 60 * 60) // Convert to hours
      : 0;

    // Get top threats
    const topThreats = Array.from(this.threatIndicators.values())
      .sort((a, b) => b.occurrences - a.occurrences)
      .slice(0, 10);

    return {
      alertsLast24h,
      alertsLast7d,
      alertsLast30d,
      alertsByType,
      alertsBySeverity,
      failedLogins,
      suspiciousActivities,
      blockedThreats,
      averageResponseTime,
      topThreats,
    };
  }

  /**
   * Automated security monitoring
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async performSecurityMonitoring(): Promise<void> {
    try {
      await this.monitorAuthenticationEvents();
      await this.monitorSessionAnomalies();
    } catch (error) {
      this.logger.error('Security monitoring failed:', error);
    }
  }

  @Cron(CronExpression.EVERY_15_MINUTES)
  async performDataAccessMonitoring(): Promise<void> {
    try {
      await this.monitorDataAccessPatterns();
    } catch (error) {
      this.logger.error('Data access monitoring failed:', error);
    }
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async performIntrusionMonitoring(): Promise<void> {
    try {
      await this.monitorSystemIntrusion();
    } catch (error) {
      this.logger.error('Intrusion monitoring failed:', error);
    }
  }

  // Private helper methods
  private async detectUnusualLoginPatterns(): Promise<void> {
    // Implementation for detecting unusual login patterns
    // This would analyze login times, locations, devices, etc.
  }

  private async detectUnusualDataAccessPatterns(): Promise<void> {
    // Implementation for detecting unusual data access patterns
    // This would analyze access frequency, data types, user roles, etc.
  }

  private async detectSQLInjectionAttempts(): Promise<void> {
    // Implementation for detecting SQL injection attempts
    // This would analyze request parameters for SQL injection patterns
  }

  private async detectXSSAttempts(): Promise<void> {
    // Implementation for detecting XSS attempts
    // This would analyze request parameters for XSS patterns
  }

  private escalateSeverity(current: string, new_: string): 'low' | 'medium' | 'high' | 'critical' {
    const severityLevels = { low: 1, medium: 2, high: 3, critical: 4 };
    const currentLevel = severityLevels[current as keyof typeof severityLevels];
    const newLevel = severityLevels[new_ as keyof typeof severityLevels];
    
    const maxLevel = Math.max(currentLevel, newLevel);
    return Object.keys(severityLevels)[maxLevel - 1] as 'low' | 'medium' | 'high' | 'critical';
  }

  /**
   * Get all alerts
   */
  getAllAlerts(): SecurityAlert[] {
    return Array.from(this.alerts.values());
  }

  /**
   * Get alert by ID
   */
  getAlert(alertId: string): SecurityAlert | undefined {
    return this.alerts.get(alertId);
  }

  /**
   * Update alert status
   */
  async updateAlert(alertId: string, updates: Partial<SecurityAlert>): Promise<void> {
    const alert = this.alerts.get(alertId);
    if (!alert) {
      throw new Error(`Alert ${alertId} not found`);
    }

    Object.assign(alert, updates);

    if (updates.status === 'resolved') {
      alert.resolvedAt = new Date();
    }

    await this.auditService.logUserAction({
      userId: updates.assignedTo || 'system',
      action: 'security_alert_updated',
      resource: 'security_alert',
      resourceId: alertId,
      success: true,
      metadata: { updates },
    });
  }

  /**
   * Get all threat indicators
   */
  getAllThreatIndicators(): ThreatIndicator[] {
    return Array.from(this.threatIndicators.values());
  }

  /**
   * Check if value is blocked
   */
  isBlocked(type: string, value: string): boolean {
    const key = `${type}:${value}`;
    const indicator = this.threatIndicators.get(key);
    return indicator?.blocked || false;
  }
}
