import {
  Entity,
  Column,
  Index,
  OneToMany,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { EmployeeDeduction } from './employee-deduction.entity';
import { DeductionType, Currency } from '@app/common/enums/status.enum';

@Entity('deductions')
@Index(['tenantId', 'code'], { unique: true })
@Index(['tenantId', 'deductionType'])
@Index(['tenantId', 'isActive'])
export class Deduction extends TenantAwareEntity {
  @Column({
    type: 'varchar',
    length: 50,
    comment: 'Deduction code',
  })
  @Index()
  code: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Deduction name',
  })
  name: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Deduction description',
  })
  description?: string;

  @Column({
    type: 'enum',
    enum: DeductionType,
    comment: 'Type of deduction',
  })
  @Index()
  deductionType: DeductionType;

  @Column({
    type: 'varchar',
    length: 50,
    default: 'post_tax',
    comment: 'Tax treatment: pre_tax, post_tax, both',
  })
  taxTreatment: string;

  @Column({
    type: 'enum',
    enum: Currency,
    default: Currency.USD,
    comment: 'Currency for deduction amounts',
  })
  currency: Currency;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Default deduction amount',
  })
  defaultAmount?: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 4,
    nullable: true,
    comment: 'Default deduction percentage',
  })
  defaultPercentage?: number;

  @Column({
    type: 'varchar',
    length: 50,
    default: 'fixed',
    comment: 'Calculation method: fixed, percentage, tiered, formula',
  })
  calculationMethod: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Calculation formula or expression',
  })
  formula?: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Minimum deduction amount',
  })
  minimumAmount?: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Maximum deduction amount',
  })
  maximumAmount?: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Annual deduction limit',
  })
  annualLimit?: number;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Tiered calculation structure',
  })
  tiers?: Array<{
    minAmount: number;
    maxAmount: number;
    rate: number;
    flatAmount?: number;
  }>;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this deduction is mandatory',
  })
  isMandatory: boolean;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether employees can opt out',
  })
  allowOptOut: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this deduction requires approval',
  })
  requiresApproval: boolean;

  @Column({
    type: 'varchar',
    length: 50,
    default: 'monthly',
    comment: 'Deduction frequency',
  })
  frequency: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Eligibility criteria',
  })
  eligibilityCriteria?: {
    minimumSalary?: number;
    maximumSalary?: number;
    employmentTypes?: string[];
    departments?: string[];
    positions?: string[];
    minimumTenure?: number; // in months
    customRules?: Array<{
      field: string;
      operator: string;
      value: any;
    }>;
  };

  @Column({
    type: 'date',
    comment: 'Effective start date',
  })
  @Index()
  effectiveStartDate: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Effective end date',
  })
  @Index()
  effectiveEndDate?: Date;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this deduction is active',
  })
  @Index()
  isActive: boolean;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Priority order for deduction calculations',
  })
  priority: number;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'GL account code for accounting integration',
  })
  glAccountCode?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Vendor or payee information',
  })
  vendor?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Vendor account number',
  })
  vendorAccountNumber?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Legal and compliance requirements',
  })
  complianceRequirements?: {
    courtOrder?: boolean;
    garnishmentType?: string;
    maxPercentageOfDisposableIncome?: number;
    priorityLevel?: number;
    requiredDocuments?: string[];
    reportingRequired?: boolean;
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Integration settings for external systems',
  })
  integrationSettings?: {
    provider?: string;
    apiEndpoint?: string;
    credentials?: Record<string, any>;
    mappings?: Record<string, string>;
  };

  @Column({
    type: 'int',
    default: 0,
    comment: 'Display order',
  })
  sortOrder: number;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Additional notes or instructions',
  })
  notes?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Deduction metadata',
  })
  metadata?: Record<string, any>;

  // Relationships
  @OneToMany(() => EmployeeDeduction, employeeDeduction => employeeDeduction.deduction)
  employeeDeductions: EmployeeDeduction[];

  // Virtual properties
  get isCurrentlyEffective(): boolean {
    const now = new Date();
    const startOk = this.effectiveStartDate <= now;
    const endOk = !this.effectiveEndDate || this.effectiveEndDate >= now;
    return startOk && endOk && this.isActive;
  }

  get isExpired(): boolean {
    return this.effectiveEndDate ? this.effectiveEndDate < new Date() : false;
  }

  get isFuture(): boolean {
    return this.effectiveStartDate > new Date();
  }

  get isPreTax(): boolean {
    return this.taxTreatment === 'pre_tax' || this.taxTreatment === 'both';
  }

  get isPostTax(): boolean {
    return this.taxTreatment === 'post_tax' || this.taxTreatment === 'both';
  }

  get hasTiers(): boolean {
    return this.calculationMethod === 'tiered' && this.tiers && this.tiers.length > 0;
  }

  get isGarnishment(): boolean {
    return this.complianceRequirements?.garnishmentType !== undefined;
  }

  // Methods
  calculateDeduction(salary: number, customAmount?: number, customPercentage?: number): number {
    let deduction = 0;

    if (customAmount !== undefined) {
      deduction = customAmount;
    } else if (customPercentage !== undefined) {
      deduction = (salary * customPercentage) / 100;
    } else {
      switch (this.calculationMethod) {
        case 'fixed':
          deduction = this.defaultAmount || 0;
          break;
        
        case 'percentage':
          deduction = this.defaultPercentage ? (salary * this.defaultPercentage) / 100 : 0;
          break;
        
        case 'tiered':
          deduction = this.calculateTieredDeduction(salary);
          break;
        
        case 'formula':
          // Custom formula calculation would be implemented here
          deduction = this.defaultAmount || 0;
          break;
        
        default:
          deduction = this.defaultAmount || 0;
      }
    }

    // Apply minimum and maximum limits
    if (this.minimumAmount && deduction < this.minimumAmount) {
      deduction = this.minimumAmount;
    }
    
    if (this.maximumAmount && deduction > this.maximumAmount) {
      deduction = this.maximumAmount;
    }

    return Math.max(0, deduction);
  }

  private calculateTieredDeduction(salary: number): number {
    if (!this.tiers) return 0;

    let deduction = 0;
    let remainingSalary = salary;

    for (const tier of this.tiers) {
      if (remainingSalary <= 0) break;

      const tierMin = tier.minAmount;
      const tierMax = tier.maxAmount || Infinity;
      const tierRange = Math.min(tierMax - tierMin, remainingSalary);

      if (salary > tierMin) {
        const applicableAmount = Math.min(tierRange, salary - tierMin);
        deduction += (applicableAmount * tier.rate) / 100;
        
        if (tier.flatAmount) {
          deduction += tier.flatAmount;
        }
        
        remainingSalary -= applicableAmount;
      }
    }

    return deduction;
  }

  isEmployeeEligible(employee: {
    salary?: number;
    employmentType?: string;
    departmentId?: string;
    positionId?: string;
    tenureMonths?: number;
  }): boolean {
    if (!this.eligibilityCriteria) return true;

    const criteria = this.eligibilityCriteria;

    // Check salary range
    if (employee.salary) {
      if (criteria.minimumSalary && employee.salary < criteria.minimumSalary) {
        return false;
      }
      if (criteria.maximumSalary && employee.salary > criteria.maximumSalary) {
        return false;
      }
    }

    // Check employment types
    if (criteria.employmentTypes && employee.employmentType && !criteria.employmentTypes.includes(employee.employmentType)) {
      return false;
    }

    // Check departments
    if (criteria.departments && employee.departmentId && !criteria.departments.includes(employee.departmentId)) {
      return false;
    }

    // Check positions
    if (criteria.positions && employee.positionId && !criteria.positions.includes(employee.positionId)) {
      return false;
    }

    // Check minimum tenure
    if (criteria.minimumTenure && (!employee.tenureMonths || employee.tenureMonths < criteria.minimumTenure)) {
      return false;
    }

    return true;
  }

  validateGarnishmentLimits(disposableIncome: number, proposedDeduction: number): boolean {
    if (!this.isGarnishment || !this.complianceRequirements?.maxPercentageOfDisposableIncome) {
      return true;
    }

    const maxAllowed = (disposableIncome * this.complianceRequirements.maxPercentageOfDisposableIncome) / 100;
    return proposedDeduction <= maxAllowed;
  }

  @BeforeInsert()
  @BeforeUpdate()
  validateDeduction(): void {
    if (this.effectiveStartDate && this.effectiveEndDate) {
      if (this.effectiveStartDate > this.effectiveEndDate) {
        throw new Error('Effective start date must be before end date');
      }
    }

    if (this.minimumAmount && this.maximumAmount) {
      if (this.minimumAmount > this.maximumAmount) {
        throw new Error('Minimum amount must be less than maximum amount');
      }
    }

    if (this.calculationMethod === 'percentage' && !this.defaultPercentage) {
      throw new Error('Percentage calculation method requires default percentage');
    }

    if (this.calculationMethod === 'tiered' && (!this.tiers || this.tiers.length === 0)) {
      throw new Error('Tiered calculation method requires tier configuration');
    }

    if (this.calculationMethod === 'fixed' && !this.defaultAmount) {
      throw new Error('Fixed calculation method requires default amount');
    }
  }
}
