import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

import { Employee } from '@app/database';

@Injectable()
export class EmployeeEventListener {
  private readonly logger = new Logger(EmployeeEventListener.name);

  @OnEvent('employee.created')
  async handleEmployeeCreated(payload: {
    employee: Employee;
    tenantId: string;
    createdBy: string;
  }): Promise<void> {
    this.logger.log(`Employee created: ${payload.employee.employeeId}`);
    // TODO: Implement employee creation event handling
  }

  @OnEvent('employee.updated')
  async handleEmployeeUpdated(payload: {
    employee: Employee;
    originalValues: Partial<Employee>;
    tenantId: string;
    updatedBy: string;
  }): Promise<void> {
    this.logger.log(`Employee updated: ${payload.employee.employeeId}`);
    // TODO: Implement employee update event handling
  }

  @OnEvent('employee.deleted')
  async handleEmployeeDeleted(payload: {
    employee: Employee;
    tenantId: string;
    deletedBy: string;
    reason?: string;
  }): Promise<void> {
    this.logger.log(`Employee deleted: ${payload.employee.employeeId}`);
    // TODO: Implement employee deletion event handling
  }
}
