import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { AuthService } from '../services/auth.service';
import { TokenService } from '../services/token.service';
import { JwtPayload } from '../interfaces/jwt-payload.interface';

@Injectable()
export class RefreshTokenStrategy extends PassportStrategy(
  Strategy,
  'jwt-refresh',
) {
  constructor(
    private configService: ConfigService,
    private authService: AuthService,
    private tokenService: TokenService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_REFRESH_SECRET'),
      issuer: 'peoplenest-hrms',
      audience: 'peoplenest-users',
      passReqToCallback: true,
    });
  }

  async validate(request: any, payload: JwtPayload) {
    const { sub: userId, email, sessionId, tokenType, jti } = payload;

    // Validate token type
    if (tokenType !== 'refresh') {
      throw new UnauthorizedException('Invalid token type');
    }

    // Extract refresh token from header
    const refreshToken = ExtractJwt.fromAuthHeaderAsBearerToken()(request);
    
    if (!refreshToken) {
      throw new UnauthorizedException('Refresh token not provided');
    }

    // Validate refresh token in database
    const isValidRefreshToken = await this.tokenService.validateRefreshToken(
      userId,
      jti,
      refreshToken,
    );

    if (!isValidRefreshToken) {
      throw new UnauthorizedException('Invalid or expired refresh token');
    }

    // Get user from database
    const user = await this.authService.validateUserById(userId);
    
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Check if user is active
    if (user.status !== 'active') {
      throw new UnauthorizedException(`User account is ${user.status}`);
    }

    // Verify email matches
    if (user.email !== email) {
      throw new UnauthorizedException('Token email mismatch');
    }

    // Return user object with refresh token info
    return {
      id: user.id,
      email: user.email,
      role: user.role,
      status: user.status,
      tenantId: user.tenantId,
      sessionId,
      refreshTokenId: jti,
      permissions: user.permissions || [],
    };
  }
}
