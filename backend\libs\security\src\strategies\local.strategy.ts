import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';

import { AuthService } from '../services/auth.service';
import { AuditService } from '../services/audit.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(
    private authService: AuthService,
    private auditService: AuditService,
  ) {
    super({
      usernameField: 'email',
      passwordField: 'password',
      passReqToCallback: true,
    });
  }

  async validate(
    request: any,
    email: string,
    password: string,
  ): Promise<any> {
    const ip = request.ip;
    const userAgent = request.get('User-Agent');

    try {
      // Validate user credentials
      const user = await this.authService.validateUser(email, password);
      
      if (!user) {
        // Log failed login attempt
        await this.auditService.logAuthAttempt({
          email,
          ip,
          userAgent,
          success: false,
          error: 'Invalid credentials',
          timestamp: new Date(),
        });
        
        throw new UnauthorizedException('Invalid credentials');
      }

      // Check account status
      if (user.status !== 'active') {
        await this.auditService.logAuthAttempt({
          userId: user.id,
          email,
          ip,
          userAgent,
          success: false,
          error: `Account is ${user.status}`,
          timestamp: new Date(),
        });
        
        throw new UnauthorizedException(`Account is ${user.status}`);
      }

      // Check if account is locked
      const isLocked = await this.authService.isAccountLocked(user.id);
      if (isLocked) {
        await this.auditService.logAuthAttempt({
          userId: user.id,
          email,
          ip,
          userAgent,
          success: false,
          error: 'Account is locked',
          timestamp: new Date(),
        });
        
        throw new UnauthorizedException('Account is temporarily locked');
      }

      // Reset failed login attempts on successful validation
      await this.authService.resetFailedLoginAttempts(user.id);

      // Log successful login attempt
      await this.auditService.logAuthAttempt({
        userId: user.id,
        email,
        ip,
        userAgent,
        success: true,
        timestamp: new Date(),
      });

      return user;
    } catch (error) {
      // Increment failed login attempts
      const user = await this.authService.findUserByEmail(email);
      if (user) {
        await this.authService.incrementFailedLoginAttempts(user.id);
      }
      
      throw error;
    }
  }
}
