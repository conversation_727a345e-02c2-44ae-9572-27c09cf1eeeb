{"version": 3, "sources": ["../../../../node_modules/@apollo/src/link/retry/delayFunction.ts", "../../../../node_modules/@apollo/src/link/retry/retryFunction.ts", "../../../../node_modules/@apollo/src/link/retry/retryLink.ts"], "sourcesContent": ["import type { Operation } from \"../core/index.js\";\n\n/**\n * Advanced mode: a function that implements the strategy for calculating delays\n * for particular responses.\n */\nexport interface DelayFunction {\n  (count: number, operation: Operation, error: any): number;\n}\n\nexport interface DelayFunctionOptions {\n  /**\n   * The number of milliseconds to wait before attempting the first retry.\n   *\n   * Delays will increase exponentially for each attempt.  E.g. if this is\n   * set to 100, subsequent retries will be delayed by 200, 400, 800, etc,\n   * until they reach maxDelay.\n   *\n   * Note that if jittering is enabled, this is the _average_ delay.\n   *\n   * Defaults to 300.\n   */\n  initial?: number;\n\n  /**\n   * The maximum number of milliseconds that the link should wait for any\n   * retry.\n   *\n   * Defaults to Infinity.\n   */\n  max?: number;\n\n  /**\n   * Whether delays between attempts should be randomized.\n   *\n   * This helps avoid thundering herd type situations by better distributing\n   * load during major outages.\n   *\n   * Defaults to true.\n   */\n  jitter?: boolean;\n}\n\nexport function buildDelayFunction(\n  delayOptions?: DelayFunctionOptions\n): DelayFunction {\n  const { initial = 300, jitter = true, max = Infinity } = delayOptions || {};\n  // If we're jittering, baseDelay is half of the maximum delay for that\n  // attempt (and is, on average, the delay we will encounter).\n  // If we're not jittering, adjust baseDelay so that the first attempt\n  // lines up with initialDelay, for everyone's sanity.\n  const baseDelay = jitter ? initial : initial / 2;\n\n  return function delayFunction(count: number) {\n    let delay = Math.min(max, baseDelay * 2 ** count);\n    if (jitter) {\n      // We opt for a full jitter approach for a mostly uniform distribution,\n      // but bound it within initialDelay and delay for everyone's sanity.\n      delay = Math.random() * delay;\n    }\n\n    return delay;\n  };\n}\n", "import type { Operation } from \"../core/index.js\";\n\n/**\n * Advanced mode: a function that determines both whether a particular\n * response should be retried.\n */\nexport interface RetryFunction {\n  (count: number, operation: Operation, error: any): boolean | Promise<boolean>;\n}\n\nexport interface RetryFunctionOptions {\n  /**\n   * The max number of times to try a single operation before giving up.\n   *\n   * Note that this INCLUDES the initial request as part of the count.\n   * E.g. maxTries of 1 indicates no retrying should occur.\n   *\n   * Defaults to 5.  Pass Infinity for infinite retries.\n   */\n  max?: number;\n\n  /**\n   * Predicate function that determines whether a particular error should\n   * trigger a retry.\n   *\n   * For example, you may want to not retry 4xx class HTTP errors.\n   *\n   * By default, all errors are retried.\n   */\n  retryIf?: (error: any, operation: Operation) => boolean | Promise<boolean>;\n}\n\nexport function buildRetryFunction(\n  retryOptions?: RetryFunctionOptions\n): RetryFunction {\n  const { retryIf, max = 5 } = retryOptions || ({} as RetryFunctionOptions);\n  return function retryFunction(count, operation, error) {\n    if (count >= max) return false;\n    return retryIf ? retryIf(error, operation) : !!error;\n  };\n}\n", "import type { Operation, FetchResult, NextLink } from \"../core/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport type { ObservableSubscription } from \"../../utilities/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport type { DelayFunction, DelayFunctionOptions } from \"./delayFunction.js\";\nimport { buildDelayFunction } from \"./delayFunction.js\";\nimport type { RetryFunction, RetryFunctionOptions } from \"./retryFunction.js\";\nimport { buildRetryFunction } from \"./retryFunction.js\";\nimport type { SubscriptionObserver } from \"zen-observable-ts\";\nimport {\n  ApolloError,\n  graphQLResultHasProtocolErrors,\n  PROTOCOL_ERRORS_SYMBOL,\n} from \"../../errors/index.js\";\n\nexport namespace RetryLink {\n  export interface Options {\n    /**\n     * Configuration for the delay strategy to use, or a custom delay strategy.\n     */\n    delay?: DelayFunctionOptions | DelayFunction;\n\n    /**\n     * Configuration for the retry strategy to use, or a custom retry strategy.\n     */\n    attempts?: RetryFunctionOptions | RetryFunction;\n  }\n}\n\n/**\n * Tracking and management of operations that may be (or currently are) retried.\n */\nclass RetryableOperation {\n  private retryCount: number = 0;\n  private currentSubscription: ObservableSubscription | null = null;\n  private timerId: number | undefined;\n\n  constructor(\n    private observer: SubscriptionObserver<FetchResult>,\n    private operation: Operation,\n    private forward: NextLink,\n    private delayFor: DelayFunction,\n    private retryIf: RetryFunction\n  ) {\n    this.try();\n  }\n\n  /**\n   * Stop retrying for the operation, and cancel any in-progress requests.\n   */\n  public cancel() {\n    if (this.currentSubscription) {\n      this.currentSubscription.unsubscribe();\n    }\n    clearTimeout(this.timerId);\n    this.timerId = undefined;\n    this.currentSubscription = null;\n  }\n\n  private try() {\n    this.currentSubscription = this.forward(this.operation).subscribe({\n      next: (result) => {\n        if (graphQLResultHasProtocolErrors(result)) {\n          this.onError(\n            new ApolloError({\n              protocolErrors: result.extensions[PROTOCOL_ERRORS_SYMBOL],\n            })\n          );\n          // Unsubscribe from the current subscription to prevent the `complete`\n          // handler to be called as a result of the stream closing.\n          this.currentSubscription?.unsubscribe();\n          return;\n        }\n\n        this.observer.next(result);\n      },\n      error: this.onError,\n      complete: this.observer.complete.bind(this.observer),\n    });\n  }\n\n  private onError = async (error: any) => {\n    this.retryCount += 1;\n\n    // Should we retry?\n    const shouldRetry = await this.retryIf(\n      this.retryCount,\n      this.operation,\n      error\n    );\n    if (shouldRetry) {\n      this.scheduleRetry(this.delayFor(this.retryCount, this.operation, error));\n      return;\n    }\n\n    this.observer.error(error);\n  };\n\n  private scheduleRetry(delay: number) {\n    if (this.timerId) {\n      throw new Error(`RetryLink BUG! Encountered overlapping retries`);\n    }\n\n    this.timerId = setTimeout(() => {\n      this.timerId = undefined;\n      this.try();\n    }, delay) as any as number;\n  }\n}\n\nexport class RetryLink extends ApolloLink {\n  private delayFor: DelayFunction;\n  private retryIf: RetryFunction;\n\n  constructor(options?: RetryLink.Options) {\n    super();\n    const { attempts, delay } = options || ({} as RetryLink.Options);\n    this.delayFor =\n      typeof delay === \"function\" ? delay : buildDelayFunction(delay);\n    this.retryIf =\n      typeof attempts === \"function\" ? attempts : buildRetryFunction(attempts);\n  }\n\n  public request(\n    operation: Operation,\n    nextLink: NextLink\n  ): Observable<FetchResult> {\n    return new Observable((observer) => {\n      const retryable = new RetryableOperation(\n        observer,\n        operation,\n        nextLink,\n        this.delayFor,\n        this.retryIf\n      );\n      return () => {\n        retryable.cancel();\n      };\n    });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AA2CM,SAAU,mBACd,cAAmC;AAE7B,MAAA,KAAmD,gBAAgB,CAAA,GAAjE,KAAA,GAAA,SAAA,UAAO,OAAA,SAAG,MAAG,IAAE,KAAA,GAAA,QAAA,SAAM,OAAA,SAAG,OAAI,IAAE,KAAA,GAAA,KAAA,MAAG,OAAA,SAAG,WAAQ;AAKpD,MAAM,YAAY,SAAS,UAAU,UAAU;AAE/C,SAAO,SAAS,cAAc,OAAa;AACzC,QAAI,QAAQ,KAAK,IAAI,KAAK,YAAY,KAAA,IAAA,GAAK,KAAK,CAAA;AAChD,QAAI,QAAQ;AAGV,cAAQ,KAAK,OAAM,IAAK;IAC1B;AAEA,WAAO;EACT;AACF;;;AC/BM,SAAU,mBACd,cAAmC;AAE7B,MAAA,KAAuB,gBAAiB,CAAA,GAAtC,UAAO,GAAA,SAAE,KAAA,GAAA,KAAA,MAAG,OAAA,SAAG,IAAC;AACxB,SAAO,SAAS,cAAc,OAAO,WAAW,OAAK;AACnD,QAAI,SAAS;AAAK,aAAO;AACzB,WAAO,UAAU,QAAQ,OAAO,SAAS,IAAI,CAAC,CAAC;EACjD;AACF;;;ACRA,IAAA;;EAAA,WAAA;AAKE,aAAAA,oBACU,UACA,WACA,SACA,UACA,SAAsB;AALhC,UAAA,QAAA;AACU,WAAA,WAAA;AACA,WAAA,YAAA;AACA,WAAA,UAAA;AACA,WAAA,WAAA;AACA,WAAA,UAAA;AATF,WAAA,aAAqB;AACrB,WAAA,sBAAqD;AA+CrD,WAAA,UAAU,SAAO,OAAU;AAAA,eAAA,UAAA,OAAA,QAAA,QAAA,WAAA;;;;;AACjC,qBAAK,cAAc;AAGC,uBAAA,CAAA,GAAM,KAAK,QAC7B,KAAK,YACL,KAAK,WACL,KAAK,CACN;;AAJK,8BAAc,GAAA,KAAA;AAKpB,oBAAI,aAAa;AACf,uBAAK,cAAc,KAAK,SAAS,KAAK,YAAY,KAAK,WAAW,KAAK,CAAC;AACxE,yBAAA;oBAAA;;kBAAA;gBACF;AAEA,qBAAK,SAAS,MAAM,KAAK;;;;;;;;;AAnDzB,WAAK,IAAG;IACV;AAKO,IAAAA,oBAAA,UAAA,SAAP,WAAA;AACE,UAAI,KAAK,qBAAqB;AAC5B,aAAK,oBAAoB,YAAW;MACtC;AACA,mBAAa,KAAK,OAAO;AACzB,WAAK,UAAU;AACf,WAAK,sBAAsB;IAC7B;AAEQ,IAAAA,oBAAA,UAAA,MAAR,WAAA;AAAA,UAAA,QAAA;AACE,WAAK,sBAAsB,KAAK,QAAQ,KAAK,SAAS,EAAE,UAAU;QAChE,MAAM,SAAC,QAAM;;AACX,cAAI,+BAA+B,MAAM,GAAG;AAC1C,kBAAK,QACH,IAAI,YAAY;cACd,gBAAgB,OAAO,WAAW,sBAAsB;aACzD,CAAC;AAIJ,aAAA,KAAA,MAAK,yBAAmB,QAAA,OAAA,SAAA,SAAA,GAAE,YAAW;AACrC;UACF;AAEA,gBAAK,SAAS,KAAK,MAAM;QAC3B;QACA,OAAO,KAAK;QACZ,UAAU,KAAK,SAAS,SAAS,KAAK,KAAK,QAAQ;OACpD;IACH;AAmBQ,IAAAA,oBAAA,UAAA,gBAAR,SAAsB,OAAa;AAAnC,UAAA,QAAA;AACE,UAAI,KAAK,SAAS;AAChB,cAAM,IAAI,MAAM,gDAAgD;MAClE;AAEA,WAAK,UAAU,WAAW,WAAA;AACxB,cAAK,UAAU;AACf,cAAK,IAAG;MACV,GAAG,KAAK;IACV;AACF,WAAAA;EAAA,EA5EA;;AA8EA,IAAA;;EAAA,SAAA,QAAA;AAA+B,cAAAC,YAAA,MAAA;AAI7B,aAAAA,WAAY,SAA2B;AACrC,UAAA,QAAA,OAAK,KAAA,IAAA,KAAE;AACD,UAAA,KAAsB,WAAY,CAAA,GAAhC,WAAQ,GAAA,UAAE,QAAK,GAAA;AACvB,YAAK,WACH,OAAO,UAAU,aAAa,QAAQ,mBAAmB,KAAK;AAChE,YAAK,UACH,OAAO,aAAa,aAAa,WAAW,mBAAmB,QAAQ;;IAC3E;AAEO,IAAAA,WAAA,UAAA,UAAP,SACE,WACA,UAAkB;AAFpB,UAAA,QAAA;AAIE,aAAO,IAAI,WAAW,SAAC,UAAQ;AAC7B,YAAM,YAAY,IAAI,mBACpB,UACA,WACA,UACA,MAAK,UACL,MAAK,OAAO;AAEd,eAAO,WAAA;AACL,oBAAU,OAAM;QAClB;MACF,CAAC;IACH;AACF,WAAAA;EAAA,EA9B+B,UAAU;;", "names": ["RetryableOperation", "RetryLink"]}