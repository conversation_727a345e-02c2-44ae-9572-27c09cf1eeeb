import {
  Entity,
  Column,
  Index,
  ManyToOne,
  Join<PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { EncryptedEntity } from './base.entity';
import { Employee } from './employee.entity';

export enum AddressType {
  HOME = 'home',
  WORK = 'work',
  MAILING = 'mailing',
  EMERGENCY = 'emergency',
}

@Entity('employee_addresses')
@Index(['employeeId', 'type'])
export class EmployeeAddress extends EncryptedEntity {
  @Column({
    type: 'uuid',
    comment: 'Employee ID',
  })
  employeeId: string;

  @Column({
    type: 'enum',
    enum: AddressType,
    comment: 'Type of address',
  })
  type: AddressType;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Street address line 1 (encrypted)',
  })
  addressLine1: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Street address line 2 (encrypted)',
  })
  addressLine2?: string;

  @Column({
    type: 'varchar',
    length: 100,
    comment: 'City (encrypted)',
  })
  city: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'State/Province (encrypted)',
  })
  state?: string;

  @Column({
    type: 'varchar',
    length: 20,
    comment: 'Postal/ZIP code (encrypted)',
  })
  postalCode: string;

  @Column({
    type: 'varchar',
    length: 100,
    comment: 'Country',
  })
  country: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this is the primary address',
  })
  isPrimary: boolean;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this address is active',
  })
  isActive: boolean;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 8,
    nullable: true,
    comment: 'Latitude coordinate',
  })
  latitude?: number;

  @Column({
    type: 'decimal',
    precision: 11,
    scale: 8,
    nullable: true,
    comment: 'Longitude coordinate',
  })
  longitude?: number;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Display order',
  })
  sortOrder: number;

  // Relationships
  @ManyToOne(() => Employee, employee => employee.addresses, { eager: false })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;

  // Virtual properties
  get fullAddress(): string {
    const parts = [
      this.addressLine1,
      this.addressLine2,
      this.city,
      this.state,
      this.postalCode,
      this.country,
    ].filter(Boolean);
    return parts.join(', ');
  }
}
