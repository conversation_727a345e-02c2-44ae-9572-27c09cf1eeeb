import {
  Entity,
  Column,
  Index,
  ManyToOne,
  Join<PERSON><PERSON>umn,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { Employee } from './employee.entity';
import { BenefitPlan } from './benefit-plan.entity';
import { Currency } from '@app/common/enums/status.enum';

export enum BenefitStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
  TERMINATED = 'terminated',
}

export enum EnrollmentStatus {
  ENROLLED = 'enrolled',
  DECLINED = 'declined',
  PENDING_APPROVAL = 'pending_approval',
  WAITING_PERIOD = 'waiting_period',
  TERMINATED = 'terminated',
}

@Entity('employee_benefits')
@Index(['tenantId', 'employeeId', 'benefitPlanId'], { unique: true })
@Index(['tenantId', 'status'])
@Index(['tenantId', 'enrollmentDate'])
export class EmployeeBenefit extends TenantAwareEntity {
  @Column({
    type: 'uuid',
    comment: 'Employee ID',
  })
  employeeId: string;

  @Column({
    type: 'uuid',
    comment: 'Benefit plan ID',
  })
  benefitPlanId: string;

  @Column({
    type: 'enum',
    enum: BenefitStatus,
    default: BenefitStatus.ACTIVE,
    comment: 'Benefit status',
  })
  @Index()
  status: BenefitStatus;

  @Column({
    type: 'enum',
    enum: EnrollmentStatus,
    default: EnrollmentStatus.ENROLLED,
    comment: 'Enrollment status',
  })
  @Index()
  enrollmentStatus: EnrollmentStatus;

  @Column({
    type: 'date',
    comment: 'Enrollment date',
  })
  @Index()
  enrollmentDate: Date;

  @Column({
    type: 'date',
    comment: 'Coverage start date',
  })
  coverageStartDate: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Coverage end date',
  })
  coverageEndDate?: Date;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Coverage tier (individual, family, etc.)',
  })
  coverageTier?: string;

  @Column({
    type: 'enum',
    enum: Currency,
    default: Currency.USD,
    comment: 'Currency for benefit amounts',
  })
  currency: Currency;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Employee contribution amount',
  })
  employeeContribution: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Employer contribution amount',
  })
  employerContribution: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Total contribution amount',
  })
  totalContribution: number;

  @Column({
    type: 'varchar',
    length: 50,
    default: 'monthly',
    comment: 'Contribution frequency',
  })
  contributionFrequency: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether employee contribution is pre-tax',
  })
  isPreTax: boolean;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Covered dependents information',
  })
  dependents?: Array<{
    id: string;
    name: string;
    relationship: string;
    dateOfBirth: Date;
    ssn?: string;
    isActive: boolean;
    enrollmentDate: Date;
    terminationDate?: Date;
  }>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Beneficiary information',
  })
  beneficiaries?: Array<{
    id: string;
    name: string;
    relationship: string;
    percentage: number;
    isPrimary: boolean;
    contactInfo?: {
      address?: string;
      phone?: string;
      email?: string;
    };
  }>;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 4,
    default: 100,
    comment: 'Vested percentage for employer contributions',
  })
  vestedPercentage: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Vested employer contribution amount',
  })
  vestedAmount: number;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Year-to-date contribution totals',
  })
  ytdContributions?: {
    employee: number;
    employer: number;
    total: number;
    lastUpdated: Date;
  };

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'External benefit provider member ID',
  })
  providerMemberId?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'External benefit provider group ID',
  })
  providerGroupId?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Election details and choices',
  })
  electionDetails?: {
    contributionAmount?: number;
    contributionPercentage?: number;
    coverageLevel?: string;
    deductible?: number;
    coinsurance?: number;
    outOfPocketMax?: number;
    customOptions?: Record<string, any>;
  };

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Enrollment method (online, paper, phone, etc.)',
  })
  enrollmentMethod?: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who processed the enrollment',
  })
  enrolledBy?: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Date when benefit was terminated',
  })
  terminationDate?: Date;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Reason for termination',
  })
  terminationReason?: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who terminated the benefit',
  })
  terminatedBy?: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether COBRA continuation is available',
  })
  cobraEligible: boolean;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'COBRA election deadline',
  })
  cobraElectionDeadline?: Date;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether COBRA was elected',
  })
  cobraElected: boolean;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'COBRA coverage start date',
  })
  cobraStartDate?: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'COBRA coverage end date',
  })
  cobraEndDate?: Date;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Enrollment history and changes',
  })
  enrollmentHistory?: Array<{
    action: string;
    date: Date;
    previousValues?: Record<string, any>;
    newValues?: Record<string, any>;
    reason?: string;
    processedBy?: string;
  }>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Compliance and audit information',
  })
  complianceInfo?: {
    lastAuditDate?: Date;
    auditStatus?: string;
    requiredDocuments?: string[];
    submittedDocuments?: Array<{
      type: string;
      submittedDate: Date;
      status: string;
    }>;
  };

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Additional notes or comments',
  })
  notes?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Employee benefit metadata',
  })
  metadata?: Record<string, any>;

  // Relationships
  @ManyToOne(() => Employee, employee => employee.employeeBenefits, { eager: false })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;

  @ManyToOne(() => BenefitPlan, plan => plan.employeeBenefits, { eager: false })
  @JoinColumn({ name: 'benefit_plan_id' })
  benefitPlan: BenefitPlan;

  // Virtual properties
  get isActive(): boolean {
    return this.status === BenefitStatus.ACTIVE && this.enrollmentStatus === EnrollmentStatus.ENROLLED;
  }

  get isTerminated(): boolean {
    return this.status === BenefitStatus.TERMINATED || this.enrollmentStatus === EnrollmentStatus.TERMINATED;
  }

  get isCoverageActive(): boolean {
    const now = new Date();
    const startOk = this.coverageStartDate <= now;
    const endOk = !this.coverageEndDate || this.coverageEndDate >= now;
    return startOk && endOk && this.isActive;
  }

  get daysUntilCoverageEnd(): number | null {
    if (!this.coverageEndDate) return null;
    const diff = this.coverageEndDate.getTime() - new Date().getTime();
    return Math.ceil(diff / (1000 * 60 * 60 * 24));
  }

  get totalYtdContributions(): number {
    return this.ytdContributions ? this.ytdContributions.total : 0;
  }

  get hasActiveDependents(): boolean {
    return this.dependents ? this.dependents.some(dep => dep.isActive) : false;
  }

  get activeDependentsCount(): number {
    return this.dependents ? this.dependents.filter(dep => dep.isActive).length : 0;
  }

  // Methods
  calculateVestedAmount(yearsOfService: number): number {
    if (!this.benefitPlan?.hasVesting) {
      return this.employerContribution;
    }

    const vestedPercentage = this.benefitPlan.getVestedPercentage(yearsOfService);
    return (this.employerContribution * vestedPercentage) / 100;
  }

  addDependent(dependent: {
    name: string;
    relationship: string;
    dateOfBirth: Date;
    ssn?: string;
  }): void {
    if (!this.dependents) {
      this.dependents = [];
    }

    this.dependents.push({
      id: `dep_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: dependent.name,
      relationship: dependent.relationship,
      dateOfBirth: dependent.dateOfBirth,
      ssn: dependent.ssn,
      isActive: true,
      enrollmentDate: new Date(),
    });
  }

  removeDependent(dependentId: string, terminationDate?: Date): void {
    if (!this.dependents) return;

    const dependent = this.dependents.find(dep => dep.id === dependentId);
    if (dependent) {
      dependent.isActive = false;
      dependent.terminationDate = terminationDate || new Date();
    }
  }

  addBeneficiary(beneficiary: {
    name: string;
    relationship: string;
    percentage: number;
    isPrimary: boolean;
    contactInfo?: any;
  }): void {
    if (!this.beneficiaries) {
      this.beneficiaries = [];
    }

    // Validate percentage doesn't exceed 100%
    const currentTotal = this.beneficiaries.reduce((sum, b) => sum + b.percentage, 0);
    if (currentTotal + beneficiary.percentage > 100) {
      throw new Error('Total beneficiary percentage cannot exceed 100%');
    }

    this.beneficiaries.push({
      id: `ben_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...beneficiary,
    });
  }

  updateYtdContributions(employeeAmount: number, employerAmount: number): void {
    if (!this.ytdContributions) {
      this.ytdContributions = {
        employee: 0,
        employer: 0,
        total: 0,
        lastUpdated: new Date(),
      };
    }

    this.ytdContributions.employee += employeeAmount;
    this.ytdContributions.employer += employerAmount;
    this.ytdContributions.total = this.ytdContributions.employee + this.ytdContributions.employer;
    this.ytdContributions.lastUpdated = new Date();
  }

  terminate(reason: string, terminationDate?: Date, terminatedBy?: string): void {
    this.status = BenefitStatus.TERMINATED;
    this.enrollmentStatus = EnrollmentStatus.TERMINATED;
    this.terminationDate = terminationDate || new Date();
    this.terminationReason = reason;
    this.terminatedBy = terminatedBy;
    this.coverageEndDate = this.terminationDate;

    // Add to enrollment history
    this.addToHistory('terminated', {
      reason,
      terminationDate: this.terminationDate,
      terminatedBy,
    });
  }

  electCobra(startDate: Date, endDate: Date): void {
    if (!this.cobraEligible) {
      throw new Error('Employee is not eligible for COBRA');
    }

    this.cobraElected = true;
    this.cobraStartDate = startDate;
    this.cobraEndDate = endDate;
    this.status = BenefitStatus.ACTIVE;
    this.enrollmentStatus = EnrollmentStatus.ENROLLED;

    this.addToHistory('cobra_elected', {
      startDate,
      endDate,
    });
  }

  private addToHistory(action: string, details: Record<string, any>): void {
    if (!this.enrollmentHistory) {
      this.enrollmentHistory = [];
    }

    this.enrollmentHistory.push({
      action,
      date: new Date(),
      newValues: details,
    });
  }

  @BeforeInsert()
  @BeforeUpdate()
  validateBenefit(): void {
    if (this.coverageStartDate && this.coverageEndDate) {
      if (this.coverageStartDate > this.coverageEndDate) {
        throw new Error('Coverage start date must be before end date');
      }
    }

    if (this.enrollmentDate > this.coverageStartDate) {
      throw new Error('Enrollment date cannot be after coverage start date');
    }

    // Calculate total contribution
    this.totalContribution = this.employeeContribution + this.employerContribution;

    // Validate beneficiary percentages
    if (this.beneficiaries) {
      const totalPercentage = this.beneficiaries.reduce((sum, b) => sum + b.percentage, 0);
      if (totalPercentage > 100) {
        throw new Error('Total beneficiary percentage cannot exceed 100%');
      }
    }
  }
}
