"""
API routes for AI/ML services.
"""

import logging
from typing import Dict, List, Any
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form
from fastapi.responses import JSONResponse
import asyncio

from app.services.resume_parser import ResumeParser
from app.services.sentiment_analyzer import SentimentAnalyzer
from app.services.predictive_analytics import PredictiveAnalytics
from app.services.nlp_query import NLPQueryService
from app.services.anomaly_detection import AnomalyDetectionService

from app.models.resume import (
    ResumeParsingRequest, ResumeData, BatchResumeParsingRequest,
    JobMatchingRequest, ResumeAnalysisResult
)
from app.models.sentiment import (
    SentimentAnalysisRequest, SentimentResult, BatchSentimentRequest,
    FeedbackAnalysisRequest, PerformanceReviewAnalysisRequest
)
from app.models.predictive import (
    AttritionPredictionRequest, AttritionPrediction,
    PerformanceForecastRequest, PerformanceForecast,
    TrendAnalysisRequest, TrendAnalysis,
    RiskProfileRequest, EmployeeRiskProfile
)
from app.models.nlp import (
    NLPQueryRequest, NLPQueryResult, QuerySuggestion
)
from app.models.anomaly import (
    AnomalyDetectionRequest, AnomalyDetectionResult
)

from app.core.exceptions import ValidationError, DataProcessingError
from app.core.auth import get_current_user, require_permissions

logger = logging.getLogger(__name__)

# Initialize services
resume_parser = ResumeParser()
sentiment_analyzer = SentimentAnalyzer()
predictive_analytics = PredictiveAnalytics()
nlp_query_service = NLPQueryService()
anomaly_detection = AnomalyDetectionService()

# Create router
router = APIRouter(prefix="/api/v1", tags=["AI Services"])


# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "ai-ml-service"}


# Resume parsing endpoints
@router.post("/resume/parse", response_model=ResumeData)
async def parse_resume(
    file: UploadFile = File(...),
    extract_skills: bool = Form(True),
    extract_experience: bool = Form(True),
    extract_education: bool = Form(True),
    job_description: str = Form(None),
    current_user: Dict = Depends(get_current_user)
):
    """Parse a resume file and extract structured data."""
    try:
        # Validate file
        if not file.filename:
            raise ValidationError("No file provided")
        
        # Read file content
        content = await file.read()
        
        # Create request
        request = ResumeParsingRequest(
            file_content=content,
            filename=file.filename,
            content_type=file.content_type,
            extract_skills=extract_skills,
            extract_experience=extract_experience,
            extract_education=extract_education,
            job_description=job_description
        )
        
        # Parse resume
        result = await resume_parser.parse_resume(request)
        return result
        
    except Exception as e:
        logger.error(f"Resume parsing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/resume/batch-parse")
async def batch_parse_resumes(
    request: BatchResumeParsingRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Parse multiple resumes in batch."""
    try:
        results = await resume_parser.batch_parse_resumes(request)
        return results
        
    except Exception as e:
        logger.error(f"Batch resume parsing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/resume/analyze", response_model=ResumeAnalysisResult)
async def analyze_resume(
    request: JobMatchingRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Analyze resume against job requirements."""
    try:
        result = await resume_parser.analyze_job_match(request)
        return result
        
    except Exception as e:
        logger.error(f"Resume analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Sentiment analysis endpoints
@router.post("/sentiment/analyze", response_model=SentimentResult)
async def analyze_sentiment(
    request: SentimentAnalysisRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Analyze sentiment of text."""
    try:
        result = await sentiment_analyzer.analyze_sentiment(request.text, request.context)
        return result
        
    except Exception as e:
        logger.error(f"Sentiment analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sentiment/batch-analyze")
async def batch_analyze_sentiment(
    request: BatchSentimentRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Analyze sentiment of multiple texts."""
    try:
        results = await sentiment_analyzer.batch_analyze_sentiment(request.texts, request.context)
        return results
        
    except Exception as e:
        logger.error(f"Batch sentiment analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sentiment/feedback")
async def analyze_feedback(
    request: FeedbackAnalysisRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Analyze employee feedback sentiment."""
    try:
        result = await sentiment_analyzer.analyze_feedback(request.feedback_data, request.context)
        return result
        
    except Exception as e:
        logger.error(f"Feedback analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sentiment/performance-review")
async def analyze_performance_review(
    request: PerformanceReviewAnalysisRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Analyze performance review sentiment."""
    try:
        result = await sentiment_analyzer.analyze_performance_review(request.review_data, request.context)
        return result
        
    except Exception as e:
        logger.error(f"Performance review analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Predictive analytics endpoints
@router.post("/predict/attrition", response_model=AttritionPrediction)
async def predict_attrition(
    request: AttritionPredictionRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Predict employee attrition probability."""
    try:
        result = await predictive_analytics.predict_attrition(request.employee_data)
        return result
        
    except Exception as e:
        logger.error(f"Attrition prediction failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/predict/performance", response_model=PerformanceForecast)
async def forecast_performance(
    request: PerformanceForecastRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Forecast employee performance."""
    try:
        result = await predictive_analytics.forecast_performance(
            request.employee_data, 
            request.forecast_periods
        )
        return result
        
    except Exception as e:
        logger.error(f"Performance forecasting failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/predict/trends", response_model=TrendAnalysis)
async def analyze_trends(
    request: TrendAnalysisRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Analyze trends in HR data."""
    try:
        result = await predictive_analytics.analyze_trends(request.data, request.trend_type)
        return result
        
    except Exception as e:
        logger.error(f"Trend analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/predict/risk-profile", response_model=EmployeeRiskProfile)
async def generate_risk_profile(
    request: RiskProfileRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Generate employee risk profile."""
    try:
        result = await predictive_analytics.generate_employee_risk_profile(request.employee_data)
        return result
        
    except Exception as e:
        logger.error(f"Risk profile generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Natural language query endpoints
@router.post("/query/nlp", response_model=NLPQueryResult)
async def process_nlp_query(
    request: NLPQueryRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Process natural language query."""
    try:
        result = await nlp_query_service.process_query(request.query, request.user_context)
        return result
        
    except Exception as e:
        logger.error(f"NLP query processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/query/suggestions")
async def get_query_suggestions(
    partial_query: str = "",
    limit: int = 5,
    current_user: Dict = Depends(get_current_user)
):
    """Get query suggestions."""
    try:
        suggestions = await nlp_query_service.get_query_suggestions(partial_query, limit)
        return {"suggestions": suggestions}
        
    except Exception as e:
        logger.error(f"Query suggestions failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/query/explain")
async def explain_query(
    query: str,
    current_user: Dict = Depends(get_current_user)
):
    """Explain how a query will be processed."""
    try:
        explanation = await nlp_query_service.explain_query(query)
        return explanation
        
    except Exception as e:
        logger.error(f"Query explanation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Anomaly detection endpoints
@router.post("/anomaly/detect", response_model=AnomalyDetectionResult)
async def detect_anomalies(
    request: AnomalyDetectionRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Detect anomalies in payroll data."""
    try:
        result = await anomaly_detection.detect_payroll_anomalies(request.data)
        return result
        
    except Exception as e:
        logger.error(f"Anomaly detection failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Model management endpoints
@router.get("/models/status")
async def get_models_status(
    current_user: Dict = Depends(get_current_user)
):
    """Get status of all AI models."""
    try:
        status = {
            "resume_parser": {
                "initialized": resume_parser.initialized,
                "models_loaded": len(resume_parser.models) if hasattr(resume_parser, 'models') else 0
            },
            "sentiment_analyzer": {
                "initialized": sentiment_analyzer.initialized,
                "models_loaded": len(sentiment_analyzer.models) if hasattr(sentiment_analyzer, 'models') else 0
            },
            "predictive_analytics": {
                "initialized": predictive_analytics.initialized,
                "models_loaded": 4  # attrition, performance, salary, engagement
            },
            "nlp_query_service": {
                "initialized": nlp_query_service.initialized,
                "models_loaded": 2  # intent classifier, entity extractor
            },
            "anomaly_detection": {
                "initialized": anomaly_detection.initialized,
                "models_loaded": 2  # isolation forest, dbscan
            }
        }
        return status
        
    except Exception as e:
        logger.error(f"Model status check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/models/initialize")
async def initialize_models(
    current_user: Dict = Depends(get_current_user)
):
    """Initialize all AI models."""
    try:
        # Initialize all services
        await asyncio.gather(
            resume_parser.initialize(),
            sentiment_analyzer.initialize(),
            predictive_analytics.initialize(),
            nlp_query_service.initialize(),
            anomaly_detection.initialize()
        )
        
        return {"message": "All models initialized successfully"}
        
    except Exception as e:
        logger.error(f"Model initialization failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Analytics endpoints
@router.get("/analytics/usage")
async def get_usage_analytics(
    start_date: str = None,
    end_date: str = None,
    current_user: Dict = Depends(get_current_user)
):
    """Get AI service usage analytics."""
    try:
        # This would typically query a database for usage metrics
        analytics = {
            "total_requests": 1000,
            "successful_requests": 950,
            "failed_requests": 50,
            "average_response_time": 1.2,
            "most_used_services": [
                {"service": "resume_parsing", "count": 400},
                {"service": "sentiment_analysis", "count": 300},
                {"service": "predictive_analytics", "count": 200},
                {"service": "nlp_query", "count": 100}
            ]
        }
        return analytics
        
    except Exception as e:
        logger.error(f"Analytics retrieval failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Error handlers
@router.exception_handler(ValidationError)
async def validation_error_handler(request, exc):
    return JSONResponse(
        status_code=400,
        content={"error": "Validation Error", "detail": str(exc)}
    )


@router.exception_handler(DataProcessingError)
async def data_processing_error_handler(request, exc):
    return JSONResponse(
        status_code=422,
        content={"error": "Data Processing Error", "detail": str(exc)}
    )
