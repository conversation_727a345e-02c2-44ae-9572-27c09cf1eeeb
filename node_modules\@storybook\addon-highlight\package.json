{"name": "@storybook/addon-highlight", "version": "8.6.14", "description": "Highlight DOM nodes within your stories", "keywords": ["storybook-addons", "essentials", "style", "appearance"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/highlight", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/highlight"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./preview": {"types": "./dist/preview.d.ts", "import": "./dist/preview.mjs", "require": "./dist/preview.js"}, "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/index.d.ts"], "preview": ["dist/preview.d.ts"]}}, "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/addon-bundle.ts"}, "dependencies": {"@storybook/global": "^5.0.0"}, "devDependencies": {"@types/webpack-env": "^1.16.0", "typescript": "^5.7.3"}, "peerDependencies": {"storybook": "^8.6.14"}, "publishConfig": {"access": "public"}, "bundler": {"exportEntries": ["./src/index.ts"], "previewEntries": ["./src/preview.ts"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae16", "sbmodern": "dist/modern/index.js", "storybook": {"displayName": "Highlight", "unsupportedFrameworks": ["react-native"], "icon": "https://user-images.githubusercontent.com/42671/162045505-9d06fe2e-8fcb-4c41-9486-e0553bce10cc.png"}}