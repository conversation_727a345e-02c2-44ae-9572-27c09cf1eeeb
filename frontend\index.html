<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="PeopleNest HRMS - AI-Enabled Enterprise Human Resource Management System" />
    <meta name="keywords" content="HRMS, HR, Human Resources, AI, Enterprise, Management" />
    <meta name="author" content="PeopleNest Team" />
    <meta name="theme-color" content="#3b82f6" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://app.peoplenest.com/" />
    <meta property="og:title" content="PeopleNest HRMS" />
    <meta property="og:description" content="AI-Enabled Enterprise Human Resource Management System" />
    <meta property="og:image" content="/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://app.peoplenest.com/" />
    <meta property="twitter:title" content="PeopleNest HRMS" />
    <meta property="twitter:description" content="AI-Enabled Enterprise Human Resource Management System" />
    <meta property="twitter:image" content="/twitter-image.png" />

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet" />
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    
    <title>PeopleNest HRMS</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
