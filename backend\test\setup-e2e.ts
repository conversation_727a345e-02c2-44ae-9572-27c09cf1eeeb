import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ValidationPipe } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import * as request from 'supertest';

// Global test configuration
export interface TestContext {
  app: INestApplication;
  moduleRef: TestingModule;
  httpServer: any;
}

// Test database configuration
export const getTestDatabaseConfig = () => ({
  type: 'postgres' as const,
  host: process.env.TEST_DATABASE_HOST || 'localhost',
  port: parseInt(process.env.TEST_DATABASE_PORT || '5432'),
  username: process.env.TEST_DATABASE_USERNAME || 'postgres',
  password: process.env.TEST_DATABASE_PASSWORD || 'postgres123',
  database: process.env.TEST_DATABASE_NAME || 'peoplenest_test',
  entities: [__dirname + '/../**/*.entity{.ts,.js}'],
  synchronize: true,
  dropSchema: true,
  logging: false,
});

// JWT test configuration
export const getTestJwtConfig = () => ({
  secret: 'test-jwt-secret-key-for-testing-only',
  signOptions: {
    expiresIn: '1h',
    issuer: 'peoplenest-test',
    audience: 'peoplenest-test-users',
  },
});

// Create test application
export const createTestApp = async (moduleMetadata: any): Promise<TestContext> => {
  const moduleRef: TestingModule = await Test.createTestingModule({
    imports: [
      ConfigModule.forRoot({
        isGlobal: true,
        envFilePath: '.env.test',
      }),
      TypeOrmModule.forRoot(getTestDatabaseConfig()),
      JwtModule.register(getTestJwtConfig()),
      ...moduleMetadata.imports,
    ],
    controllers: moduleMetadata.controllers || [],
    providers: moduleMetadata.providers || [],
  }).compile();

  const app: INestApplication = moduleRef.createNestApplication();
  
  // Global pipes and middleware
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  await app.init();
  const httpServer = app.getHttpServer();

  return { app, moduleRef, httpServer };
};

// Cleanup test application
export const cleanupTestApp = async (context: TestContext) => {
  if (context.app) {
    await context.app.close();
  }
  if (context.moduleRef) {
    await context.moduleRef.close();
  }
};

// Test user factory
export const createTestUser = () => ({
  email: '<EMAIL>',
  password: 'TestPassword123!',
  firstName: 'Test',
  lastName: 'User',
  role: 'EMPLOYEE',
});

// Test tenant factory
export const createTestTenant = () => ({
  name: 'Test Company',
  domain: 'test.com',
  settings: {
    timezone: 'UTC',
    currency: 'USD',
    dateFormat: 'YYYY-MM-DD',
  },
});

// Authentication helper
export const authenticateUser = async (
  httpServer: any,
  userCredentials = { email: '<EMAIL>', password: 'TestPassword123!' }
): Promise<string> => {
  const response = await request(httpServer)
    .post('/auth/login')
    .send(userCredentials)
    .expect(200);

  return response.body.accessToken;
};

// Database cleanup helper
export const cleanupDatabase = async (app: INestApplication) => {
  const dataSource = app.get('DataSource');
  const entities = dataSource.entityMetadatas;

  for (const entity of entities) {
    const repository = dataSource.getRepository(entity.name);
    await repository.clear();
  }
};

// Mock data generators
export const generateMockEmployee = (overrides = {}) => ({
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  employeeId: 'EMP001',
  department: 'Engineering',
  position: 'Software Developer',
  hireDate: new Date('2023-01-01'),
  salary: 75000,
  status: 'ACTIVE',
  ...overrides,
});

export const generateMockPayroll = (overrides = {}) => ({
  period: '2024-01',
  grossPay: 6250,
  netPay: 4500,
  deductions: 1750,
  taxes: 1250,
  status: 'PROCESSED',
  ...overrides,
});

// Test utilities
export const expectValidationError = (response: any, field: string) => {
  expect(response.status).toBe(400);
  expect(response.body.message).toContain(field);
};

export const expectUnauthorized = (response: any) => {
  expect(response.status).toBe(401);
};

export const expectForbidden = (response: any) => {
  expect(response.status).toBe(403);
};

export const expectNotFound = (response: any) => {
  expect(response.status).toBe(404);
};

// Performance testing helpers
export const measureExecutionTime = async (fn: () => Promise<any>): Promise<{ result: any; duration: number }> => {
  const start = Date.now();
  const result = await fn();
  const duration = Date.now() - start;
  return { result, duration };
};

// Mock external services
export const mockEmailService = {
  sendEmail: jest.fn().mockResolvedValue(true),
  sendBulkEmail: jest.fn().mockResolvedValue(true),
};

export const mockFileStorageService = {
  uploadFile: jest.fn().mockResolvedValue({ url: 'https://example.com/file.pdf', key: 'file-key' }),
  deleteFile: jest.fn().mockResolvedValue(true),
  getFileUrl: jest.fn().mockResolvedValue('https://example.com/file.pdf'),
};

export const mockAIService = {
  parseResume: jest.fn().mockResolvedValue({
    personalInfo: { name: 'John Doe', email: '<EMAIL>' },
    skills: ['JavaScript', 'TypeScript', 'Node.js'],
    experience: [{ company: 'Tech Corp', position: 'Developer', duration: '2 years' }],
  }),
  analyzeSentiment: jest.fn().mockResolvedValue({
    sentiment: 'positive',
    confidence: 0.85,
    emotions: { joy: 0.7, trust: 0.6 },
  }),
};

// Global test setup
beforeAll(async () => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
  process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters';
});

afterAll(async () => {
  // Global cleanup
});

// Jest configuration
jest.setTimeout(30000);

// Suppress console logs during tests unless explicitly needed
if (process.env.TEST_VERBOSE !== 'true') {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
}
