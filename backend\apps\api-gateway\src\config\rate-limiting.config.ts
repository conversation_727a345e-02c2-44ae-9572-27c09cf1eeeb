import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ThrottlerModuleOptions, ThrottlerOptionsFactory } from '@nestjs/throttler';
import { Request } from 'express';

export interface RateLimitRule {
  id: string;
  name: string;
  path: string;
  method?: string;
  limit: number;
  ttl: number; // Time window in seconds
  skipIf?: (req: Request) => boolean;
  keyGenerator?: (req: Request) => string;
  message?: string;
  headers?: boolean;
}

@Injectable()
export class RateLimitingConfigService implements ThrottlerOptionsFactory {
  private readonly logger = new Logger(RateLimitingConfigService.name);
  private readonly customRules = new Map<string, RateLimitRule>();

  constructor(private readonly configService: ConfigService) {
    this.initializeDefaultRules();
  }

  createThrottlerOptions(): ThrottlerModuleOptions {
    const defaultTtl = this.configService.get<number>('RATE_LIMIT_WINDOW_MS', 900000); // 15 minutes
    const defaultLimit = this.configService.get<number>('RATE_LIMIT_MAX_REQUESTS', 100);

    return {
      ttl: defaultTtl,
      limit: defaultLimit,
      skipIf: (req: Request) => this.shouldSkipRateLimit(req),
      keyGenerator: (req: Request) => this.generateRateLimitKey(req),
      getTracker: (req: Request) => this.getCustomTracker(req),
      errorMessage: 'Too many requests, please try again later.',
      headers: true,
    };
  }

  /**
   * Add custom rate limiting rule
   */
  addRule(rule: RateLimitRule): void {
    this.customRules.set(rule.id, rule);
    this.logger.log(`Added rate limiting rule: ${rule.name}`);
  }

  /**
   * Remove rate limiting rule
   */
  removeRule(ruleId: string): void {
    this.customRules.delete(ruleId);
    this.logger.log(`Removed rate limiting rule: ${ruleId}`);
  }

  /**
   * Get all rate limiting rules
   */
  getRules(): RateLimitRule[] {
    return Array.from(this.customRules.values());
  }

  /**
   * Check if request should skip rate limiting
   */
  private shouldSkipRateLimit(req: Request): boolean {
    // Skip for health checks
    if (req.path.includes('/_health') || req.path.includes('/_gateway/status')) {
      return true;
    }

    // Skip for internal service-to-service communication
    if (req.headers['x-internal-service'] === 'true') {
      return true;
    }

    // Skip for admin users (if configured)
    if (this.configService.get<boolean>('RATE_LIMIT_SKIP_ADMIN', false)) {
      const user = (req as any).user;
      if (user && user.role === 'admin') {
        return true;
      }
    }

    // Check custom rules
    for (const rule of this.customRules.values()) {
      if (rule.skipIf && rule.skipIf(req)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Generate rate limiting key
   */
  private generateRateLimitKey(req: Request): string {
    // Check for custom key generators
    const matchingRule = this.findMatchingRule(req);
    if (matchingRule && matchingRule.keyGenerator) {
      return matchingRule.keyGenerator(req);
    }

    // Default key generation strategy
    const user = (req as any).user;
    const tenantId = req.headers['x-tenant-id'] as string;
    const ip = req.ip || req.connection.remoteAddress;

    // Authenticated users: use user ID + tenant
    if (user) {
      return `user:${user.id}:tenant:${tenantId || 'default'}`;
    }

    // Anonymous users: use IP + tenant
    return `ip:${ip}:tenant:${tenantId || 'default'}`;
  }

  /**
   * Get custom tracker for specific rules
   */
  private getCustomTracker(req: Request): { limit: number; ttl: number } | undefined {
    const matchingRule = this.findMatchingRule(req);
    
    if (matchingRule) {
      return {
        limit: matchingRule.limit,
        ttl: matchingRule.ttl * 1000, // Convert to milliseconds
      };
    }

    return undefined;
  }

  /**
   * Find matching rate limiting rule for request
   */
  private findMatchingRule(req: Request): RateLimitRule | undefined {
    for (const rule of this.customRules.values()) {
      // Check path match
      const pathMatches = this.pathMatches(req.path, rule.path);
      
      // Check method match (if specified)
      const methodMatches = !rule.method || req.method.toLowerCase() === rule.method.toLowerCase();
      
      if (pathMatches && methodMatches) {
        return rule;
      }
    }

    return undefined;
  }

  /**
   * Check if path matches rule pattern
   */
  private pathMatches(requestPath: string, rulePath: string): boolean {
    // Exact match
    if (requestPath === rulePath) {
      return true;
    }

    // Wildcard match
    if (rulePath.includes('*')) {
      const pattern = rulePath.replace(/\*/g, '.*');
      const regex = new RegExp(`^${pattern}$`);
      return regex.test(requestPath);
    }

    // Prefix match
    if (rulePath.endsWith('/')) {
      return requestPath.startsWith(rulePath);
    }

    return false;
  }

  /**
   * Initialize default rate limiting rules
   */
  private initializeDefaultRules(): void {
    const defaultRules: RateLimitRule[] = [
      {
        id: 'auth-login',
        name: 'Authentication Login',
        path: '/api/v1/auth/login',
        method: 'POST',
        limit: 5, // 5 attempts
        ttl: 900, // 15 minutes
        message: 'Too many login attempts, please try again later.',
      },
      {
        id: 'auth-register',
        name: 'User Registration',
        path: '/api/v1/auth/register',
        method: 'POST',
        limit: 3, // 3 attempts
        ttl: 3600, // 1 hour
        message: 'Too many registration attempts, please try again later.',
      },
      {
        id: 'password-reset',
        name: 'Password Reset',
        path: '/api/v1/auth/password/reset',
        method: 'POST',
        limit: 3, // 3 attempts
        ttl: 3600, // 1 hour
        message: 'Too many password reset attempts, please try again later.',
      },
      {
        id: 'ai-services',
        name: 'AI Services',
        path: '/api/v1/ai/*',
        limit: 20, // 20 requests
        ttl: 60, // 1 minute
        message: 'AI service rate limit exceeded, please try again later.',
      },
      {
        id: 'payroll-processing',
        name: 'Payroll Processing',
        path: '/api/v1/payroll/process',
        method: 'POST',
        limit: 10, // 10 requests
        ttl: 300, // 5 minutes
        message: 'Payroll processing rate limit exceeded.',
      },
      {
        id: 'file-upload',
        name: 'File Upload',
        path: '/api/v1/*/upload',
        limit: 10, // 10 uploads
        ttl: 300, // 5 minutes
        message: 'File upload rate limit exceeded.',
      },
      {
        id: 'search-api',
        name: 'Search API',
        path: '/api/v1/*/search',
        limit: 100, // 100 searches
        ttl: 60, // 1 minute
        message: 'Search rate limit exceeded.',
      },
      {
        id: 'reports-generation',
        name: 'Reports Generation',
        path: '/api/v1/reports/generate',
        method: 'POST',
        limit: 5, // 5 reports
        ttl: 300, // 5 minutes
        message: 'Report generation rate limit exceeded.',
      },
      {
        id: 'bulk-operations',
        name: 'Bulk Operations',
        path: '/api/v1/*/bulk',
        limit: 3, // 3 bulk operations
        ttl: 600, // 10 minutes
        message: 'Bulk operation rate limit exceeded.',
      },
      {
        id: 'graphql-queries',
        name: 'GraphQL Queries',
        path: '/graphql',
        limit: 200, // 200 queries
        ttl: 60, // 1 minute
        keyGenerator: (req: Request) => {
          // Custom key for GraphQL based on query complexity
          const user = (req as any).user;
          const complexity = this.estimateQueryComplexity(req.body?.query || '');
          
          if (complexity > 10) {
            return `graphql:complex:${user?.id || req.ip}`;
          }
          
          return `graphql:simple:${user?.id || req.ip}`;
        },
      },
    ];

    defaultRules.forEach(rule => this.addRule(rule));
  }

  /**
   * Estimate GraphQL query complexity (simplified)
   */
  private estimateQueryComplexity(query: string): number {
    if (!query) return 1;
    
    // Simple heuristic: count nested levels and field selections
    const depth = (query.match(/{/g) || []).length;
    const fields = (query.match(/\w+\s*{/g) || []).length;
    
    return depth + fields;
  }

  /**
   * Get rate limiting statistics
   */
  getRateLimitingStats(): {
    totalRules: number;
    activeRules: number;
    requestsBlocked: number;
    requestsAllowed: number;
  } {
    // This would be implemented with proper metrics collection
    // For now, return mock data
    return {
      totalRules: this.customRules.size,
      activeRules: Array.from(this.customRules.values()).filter(rule => !rule.skipIf).length,
      requestsBlocked: 0,
      requestsAllowed: 0,
    };
  }

  /**
   * Update rate limiting rule
   */
  updateRule(ruleId: string, updates: Partial<RateLimitRule>): void {
    const existingRule = this.customRules.get(ruleId);
    if (existingRule) {
      const updatedRule = { ...existingRule, ...updates };
      this.customRules.set(ruleId, updatedRule);
      this.logger.log(`Updated rate limiting rule: ${ruleId}`);
    }
  }

  /**
   * Test rate limiting rule
   */
  testRule(ruleId: string, mockRequest: Partial<Request>): {
    matches: boolean;
    limit: number;
    ttl: number;
    key: string;
  } {
    const rule = this.customRules.get(ruleId);
    if (!rule) {
      throw new Error(`Rule not found: ${ruleId}`);
    }

    const req = mockRequest as Request;
    const matches = this.findMatchingRule(req) === rule;
    
    return {
      matches,
      limit: rule.limit,
      ttl: rule.ttl,
      key: matches ? this.generateRateLimitKey(req) : '',
    };
  }
}
