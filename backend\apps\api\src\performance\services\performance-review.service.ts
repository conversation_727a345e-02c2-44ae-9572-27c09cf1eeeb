import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, Between } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { PerformanceReview, ReviewType, ReviewStatus, OverallRating } from '@app/database/entities/performance-review.entity';
import { Employee } from '@app/database/entities/employee.entity';
import { TenantService } from '@app/common/tenant/tenant.service';

export interface CreatePerformanceReviewDto {
  employeeId: string;
  reviewerId?: string;
  hrReviewerId?: string;
  reviewType: ReviewType;
  reviewPeriodStart: Date;
  reviewPeriodEnd: Date;
  dueDate?: Date;
  metadata?: any;
}

export interface UpdatePerformanceReviewDto {
  status?: ReviewStatus;
  selfAssessment?: string;
  selfRatings?: Record<string, { rating: number; comments?: string }>;
  managerAssessment?: string;
  managerRatings?: Record<string, { rating: number; comments?: string }>;
  hrComments?: string;
  overallRating?: OverallRating;
  overallScore?: number;
  developmentPlan?: string;
  strengths?: string[];
  improvementAreas?: string[];
  careerAspirations?: string[];
  promotionRecommended?: boolean;
  promotionJustification?: string;
  salaryIncreaseRecommended?: boolean;
  salaryIncreasePercentage?: number;
  compensationJustification?: string;
  aiInsights?: any;
  metadata?: any;
}

export interface PerformanceReviewFilters {
  employeeId?: string;
  reviewerId?: string;
  hrReviewerId?: string;
  reviewType?: ReviewType;
  status?: ReviewStatus;
  reviewPeriodStart?: Date;
  reviewPeriodEnd?: Date;
  dueDate?: Date;
  overallRating?: OverallRating;
}

@Injectable()
export class PerformanceReviewService {
  constructor(
    @InjectRepository(PerformanceReview)
    private performanceReviewRepository: Repository<PerformanceReview>,
    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,
    private tenantService: TenantService,
    private eventEmitter: EventEmitter2,
  ) {}

  async create(
    createDto: CreatePerformanceReviewDto,
    userId: string,
  ): Promise<PerformanceReview> {
    const tenantId = this.tenantService.getCurrentTenantId();

    // Validate employee exists
    const employee = await this.employeeRepository.findOne({
      where: { id: createDto.employeeId, tenantId },
    });

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    // Validate reviewer if provided
    if (createDto.reviewerId) {
      const reviewer = await this.employeeRepository.findOne({
        where: { id: createDto.reviewerId, tenantId },
      });
      if (!reviewer) {
        throw new NotFoundException('Reviewer not found');
      }
    }

    // Validate HR reviewer if provided
    if (createDto.hrReviewerId) {
      const hrReviewer = await this.employeeRepository.findOne({
        where: { id: createDto.hrReviewerId, tenantId },
      });
      if (!hrReviewer) {
        throw new NotFoundException('HR reviewer not found');
      }
    }

    // Check for overlapping reviews
    const existingReview = await this.performanceReviewRepository.findOne({
      where: {
        tenantId,
        employeeId: createDto.employeeId,
        reviewType: createDto.reviewType,
        reviewPeriodStart: Between(createDto.reviewPeriodStart, createDto.reviewPeriodEnd),
        status: ReviewStatus.COMPLETED,
      },
    });

    if (existingReview) {
      throw new BadRequestException('Employee already has a completed review for this period');
    }

    const performanceReview = this.performanceReviewRepository.create({
      ...createDto,
      tenantId,
      createdBy: userId,
      status: ReviewStatus.DRAFT,
    });

    const savedReview = await this.performanceReviewRepository.save(performanceReview);

    // Emit event
    this.eventEmitter.emit('performance-review.created', {
      performanceReview: savedReview,
      tenantId,
      userId,
    });

    return savedReview;
  }

  async findAll(
    filters: PerformanceReviewFilters = {},
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: PerformanceReview[]; total: number; page: number; limit: number }> {
    const tenantId = this.tenantService.getCurrentTenantId();

    const where: FindOptionsWhere<PerformanceReview> = {
      tenantId,
      ...filters,
    };

    const [data, total] = await this.performanceReviewRepository.findAndCount({
      where,
      relations: ['employee', 'reviewer', 'hrReviewer', 'feedback', 'goals'],
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return { data, total, page, limit };
  }

  async findOne(id: string): Promise<PerformanceReview> {
    const tenantId = this.tenantService.getCurrentTenantId();

    const performanceReview = await this.performanceReviewRepository.findOne({
      where: { id, tenantId },
      relations: ['employee', 'reviewer', 'hrReviewer', 'feedback', 'goals'],
    });

    if (!performanceReview) {
      throw new NotFoundException('Performance review not found');
    }

    return performanceReview;
  }

  async update(
    id: string,
    updateDto: UpdatePerformanceReviewDto,
    userId: string,
  ): Promise<PerformanceReview> {
    const tenantId = this.tenantService.getCurrentTenantId();

    const performanceReview = await this.findOne(id);

    // Validate status transitions
    if (updateDto.status) {
      this.validateStatusTransition(performanceReview.status, updateDto.status);
    }

    // Update timestamps based on status changes
    if (updateDto.status === ReviewStatus.PENDING_MANAGER_REVIEW && updateDto.selfAssessment) {
      updateDto['selfAssessmentCompletedAt'] = new Date();
    }

    if (updateDto.status === ReviewStatus.PENDING_HR_REVIEW && updateDto.managerAssessment) {
      updateDto['managerReviewCompletedAt'] = new Date();
    }

    if (updateDto.status === ReviewStatus.COMPLETED && updateDto.hrComments) {
      updateDto['hrReviewCompletedAt'] = new Date();
      updateDto['completedDate'] = new Date();
    }

    Object.assign(performanceReview, updateDto);
    performanceReview.updatedBy = userId;

    const savedReview = await this.performanceReviewRepository.save(performanceReview);

    // Emit event
    this.eventEmitter.emit('performance-review.updated', {
      performanceReview: savedReview,
      tenantId,
      userId,
      changes: updateDto,
    });

    return savedReview;
  }

  async delete(id: string, userId: string): Promise<void> {
    const tenantId = this.tenantService.getCurrentTenantId();

    const performanceReview = await this.findOne(id);

    if (performanceReview.status === ReviewStatus.COMPLETED) {
      throw new BadRequestException('Cannot delete completed performance review');
    }

    await this.performanceReviewRepository.remove(performanceReview);

    // Emit event
    this.eventEmitter.emit('performance-review.deleted', {
      performanceReviewId: id,
      tenantId,
      userId,
    });
  }

  async getEmployeeReviews(
    employeeId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: PerformanceReview[]; total: number; page: number; limit: number }> {
    return this.findAll({ employeeId }, page, limit);
  }

  async getReviewsByManager(
    managerId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: PerformanceReview[]; total: number; page: number; limit: number }> {
    return this.findAll({ reviewerId: managerId }, page, limit);
  }

  async getDueReviews(
    dueDate?: Date,
  ): Promise<PerformanceReview[]> {
    const tenantId = this.tenantService.getCurrentTenantId();
    const targetDate = dueDate || new Date();

    return this.performanceReviewRepository.find({
      where: {
        tenantId,
        dueDate: Between(new Date(), targetDate),
        status: ReviewStatus.PENDING_SELF_ASSESSMENT,
      },
      relations: ['employee', 'reviewer'],
    });
  }

  private validateStatusTransition(currentStatus: ReviewStatus, newStatus: ReviewStatus): void {
    const validTransitions: Record<ReviewStatus, ReviewStatus[]> = {
      [ReviewStatus.DRAFT]: [ReviewStatus.PENDING_SELF_ASSESSMENT, ReviewStatus.CANCELLED],
      [ReviewStatus.PENDING_SELF_ASSESSMENT]: [ReviewStatus.PENDING_MANAGER_REVIEW, ReviewStatus.CANCELLED],
      [ReviewStatus.PENDING_MANAGER_REVIEW]: [ReviewStatus.PENDING_HR_REVIEW, ReviewStatus.PENDING_CALIBRATION, ReviewStatus.COMPLETED],
      [ReviewStatus.PENDING_HR_REVIEW]: [ReviewStatus.PENDING_CALIBRATION, ReviewStatus.COMPLETED],
      [ReviewStatus.PENDING_CALIBRATION]: [ReviewStatus.COMPLETED],
      [ReviewStatus.COMPLETED]: [],
      [ReviewStatus.CANCELLED]: [],
    };

    if (!validTransitions[currentStatus]?.includes(newStatus)) {
      throw new BadRequestException(`Invalid status transition from ${currentStatus} to ${newStatus}`);
    }
  }
}
