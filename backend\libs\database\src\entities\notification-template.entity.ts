import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  <PERSON>To<PERSON><PERSON>,
  Join<PERSON><PERSON>um<PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';

export enum TemplateType {
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  IN_APP = 'in_app',
}

export enum TriggerEvent {
  EMPLOYEE_ONBOARDING = 'employee_onboarding',
  PERFORMANCE_REVIEW_DUE = 'performance_review_due',
  LEAVE_REQUEST_SUBMITTED = 'leave_request_submitted',
  LEAVE_REQUEST_APPROVED = 'leave_request_approved',
  LEAVE_REQUEST_REJECTED = 'leave_request_rejected',
  PAYROLL_PROCESSED = 'payroll_processed',
  BIRTHDAY_REMINDER = 'birthday_reminder',
  WORK_ANNIVERSARY = 'work_anniversary',
  TRAINING_DUE = 'training_due',
  DOCUMENT_EXPIRY = 'document_expiry',
  GOAL_DEADLINE_APPROACHING = 'goal_deadline_approaching',
  INTERVIEW_SCHEDULED = 'interview_scheduled',
  OFFER_EXTENDED = 'offer_extended',
  SYSTEM_MAINTENANCE = 'system_maintenance',
}

@Entity('notification_templates')
@Index(['tenantId', 'triggerEvent'])
@Index(['tenantId', 'isActive'])
export class NotificationTemplate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: TemplateType,
    name: 'template_type',
  })
  templateType: TemplateType;

  @Column({
    type: 'enum',
    enum: TriggerEvent,
    name: 'trigger_event',
  })
  triggerEvent: TriggerEvent;

  @Column({ type: 'boolean', default: true, name: 'is_active' })
  isActive: boolean;

  @Column({ length: 255, name: 'subject_template' })
  subjectTemplate: string;

  @Column({ type: 'text', name: 'body_template' })
  bodyTemplate: string;

  @Column({ type: 'json', nullable: true, name: 'template_variables' })
  templateVariables: Array<{
    name: string;
    description: string;
    type: 'string' | 'number' | 'date' | 'boolean';
    required: boolean;
    defaultValue?: any;
  }>;

  @Column({ type: 'json', nullable: true, name: 'delivery_settings' })
  deliverySettings: {
    channels: Array<'email' | 'sms' | 'push' | 'in_app'>;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    retryAttempts?: number;
    retryInterval?: number; // in minutes
    batchSize?: number;
  };

  @Column({ type: 'json', nullable: true, name: 'trigger_conditions' })
  triggerConditions: {
    filters?: Array<{
      field: string;
      operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
      value: any;
    }>;
    timing?: {
      delay?: number; // in minutes
      schedule?: string; // cron expression
      timezone?: string;
    };
  };

  @Column({ type: 'json', nullable: true, name: 'personalization_rules' })
  personalizationRules: Array<{
    condition: string;
    subjectOverride?: string;
    bodyOverride?: string;
    channelOverride?: string[];
  }>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater: User;
}
