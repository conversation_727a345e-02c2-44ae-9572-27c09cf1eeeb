#!/bin/bash

# PeopleNest HRMS - Production Deployment Script
# This script deploys the PeopleNest HRMS application to Kubernetes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
NAMESPACE="peoplenest"
ENVIRONMENT="production"
REGISTRY="ghcr.io"
IMAGE_TAG="latest"

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed. Please install kubectl first."
        exit 1
    fi
    
    # Check if helm is installed
    if ! command -v helm &> /dev/null; then
        print_error "helm is not installed. Please install helm first."
        exit 1
    fi
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        print_error "docker is not installed. Please install docker first."
        exit 1
    fi
    
    # Check kubectl connection
    if ! kubectl cluster-info &> /dev/null; then
        print_error "Cannot connect to Kubernetes cluster. Please check your kubeconfig."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to create namespace
create_namespace() {
    print_status "Creating namespace..."
    
    if kubectl get namespace $NAMESPACE &> /dev/null; then
        print_warning "Namespace $NAMESPACE already exists"
    else
        kubectl apply -f k8s/namespace.yaml
        print_success "Namespace $NAMESPACE created"
    fi
}

# Function to apply secrets
apply_secrets() {
    print_status "Applying secrets..."
    
    # Check if secrets file exists
    if [ ! -f "k8s/secrets.yaml" ]; then
        print_error "Secrets file not found. Please create k8s/secrets.yaml"
        exit 1
    fi
    
    # Warn about default secrets
    print_warning "Please ensure all secrets in k8s/secrets.yaml are updated for production!"
    read -p "Have you updated all secrets for production? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_error "Please update secrets before deploying to production"
        exit 1
    fi
    
    kubectl apply -f k8s/secrets.yaml
    print_success "Secrets applied"
}

# Function to apply configmaps
apply_configmaps() {
    print_status "Applying configmaps..."
    kubectl apply -f k8s/configmap.yaml
    print_success "Configmaps applied"
}

# Function to deploy databases
deploy_databases() {
    print_status "Deploying databases..."
    
    # Deploy PostgreSQL
    if [ -f "k8s/postgres-deployment.yaml" ]; then
        kubectl apply -f k8s/postgres-deployment.yaml
        print_status "Waiting for PostgreSQL to be ready..."
        kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=postgres -n $NAMESPACE --timeout=300s
    fi
    
    # Deploy MongoDB
    if [ -f "k8s/mongodb-deployment.yaml" ]; then
        kubectl apply -f k8s/mongodb-deployment.yaml
        print_status "Waiting for MongoDB to be ready..."
        kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=mongodb -n $NAMESPACE --timeout=300s
    fi
    
    # Deploy Redis
    if [ -f "k8s/redis-deployment.yaml" ]; then
        kubectl apply -f k8s/redis-deployment.yaml
        print_status "Waiting for Redis to be ready..."
        kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=redis -n $NAMESPACE --timeout=300s
    fi
    
    print_success "Databases deployed"
}

# Function to deploy applications
deploy_applications() {
    print_status "Deploying applications..."
    
    # Deploy backend
    kubectl apply -f k8s/backend-deployment.yaml
    print_status "Waiting for backend to be ready..."
    kubectl rollout status deployment/backend -n $NAMESPACE --timeout=600s
    
    # Deploy AI service
    if [ -f "k8s/ai-service-deployment.yaml" ]; then
        kubectl apply -f k8s/ai-service-deployment.yaml
        print_status "Waiting for AI service to be ready..."
        kubectl rollout status deployment/ai-service -n $NAMESPACE --timeout=600s
    fi
    
    # Deploy frontend
    kubectl apply -f k8s/frontend-deployment.yaml
    print_status "Waiting for frontend to be ready..."
    kubectl rollout status deployment/frontend -n $NAMESPACE --timeout=600s
    
    print_success "Applications deployed"
}

# Function to deploy monitoring
deploy_monitoring() {
    print_status "Deploying monitoring stack..."
    
    if [ -f "k8s/monitoring.yaml" ]; then
        kubectl apply -f k8s/monitoring.yaml
        print_status "Waiting for monitoring stack to be ready..."
        kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=prometheus -n $NAMESPACE --timeout=300s
        kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=grafana -n $NAMESPACE --timeout=300s
    fi
    
    print_success "Monitoring stack deployed"
}

# Function to deploy ingress
deploy_ingress() {
    print_status "Deploying ingress..."
    
    if [ -f "k8s/ingress.yaml" ]; then
        kubectl apply -f k8s/ingress.yaml
        print_success "Ingress deployed"
    else
        print_warning "Ingress configuration not found"
    fi
}

# Function to run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    # Get backend pod name
    BACKEND_POD=$(kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=backend -o jsonpath='{.items[0].metadata.name}')
    
    if [ -n "$BACKEND_POD" ]; then
        kubectl exec -n $NAMESPACE $BACKEND_POD -- npm run migration:run
        print_success "Database migrations completed"
    else
        print_error "Backend pod not found"
        exit 1
    fi
}

# Function to create admin user
create_admin_user() {
    print_status "Creating admin user..."
    
    # Get backend pod name
    BACKEND_POD=$(kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=backend -o jsonpath='{.items[0].metadata.name}')
    
    if [ -n "$BACKEND_POD" ]; then
        kubectl exec -n $NAMESPACE $BACKEND_POD -- npm run admin:create
        print_success "Admin user created"
    else
        print_error "Backend pod not found"
        exit 1
    fi
}

# Function to run health checks
run_health_checks() {
    print_status "Running health checks..."
    
    # Check backend health
    BACKEND_SERVICE=$(kubectl get service backend-service -n $NAMESPACE -o jsonpath='{.spec.clusterIP}')
    if kubectl run health-check --rm -i --restart=Never --image=curlimages/curl -- curl -f http://$BACKEND_SERVICE:3001/health; then
        print_success "Backend health check passed"
    else
        print_error "Backend health check failed"
        exit 1
    fi
    
    # Check frontend health
    FRONTEND_SERVICE=$(kubectl get service frontend-service -n $NAMESPACE -o jsonpath='{.spec.clusterIP}')
    if kubectl run health-check --rm -i --restart=Never --image=curlimages/curl -- curl -f http://$FRONTEND_SERVICE:80/health; then
        print_success "Frontend health check passed"
    else
        print_error "Frontend health check failed"
        exit 1
    fi
    
    print_success "All health checks passed"
}

# Function to display deployment info
display_deployment_info() {
    print_status "Deployment Information"
    echo "======================="
    
    echo "Namespace: $NAMESPACE"
    echo "Environment: $ENVIRONMENT"
    echo "Image Tag: $IMAGE_TAG"
    echo ""
    
    print_status "Services:"
    kubectl get services -n $NAMESPACE
    echo ""
    
    print_status "Pods:"
    kubectl get pods -n $NAMESPACE
    echo ""
    
    print_status "Ingress:"
    kubectl get ingress -n $NAMESPACE
    echo ""
    
    # Get external URLs if available
    INGRESS_IP=$(kubectl get ingress -n $NAMESPACE -o jsonpath='{.items[0].status.loadBalancer.ingress[0].ip}')
    if [ -n "$INGRESS_IP" ]; then
        echo "Application URL: https://$INGRESS_IP"
        echo "API URL: https://$INGRESS_IP/api"
        echo "GraphQL URL: https://$INGRESS_IP/graphql"
    fi
}

# Function to cleanup on failure
cleanup_on_failure() {
    print_error "Deployment failed. Cleaning up..."
    
    # Rollback deployments
    kubectl rollout undo deployment/backend -n $NAMESPACE || true
    kubectl rollout undo deployment/frontend -n $NAMESPACE || true
    kubectl rollout undo deployment/ai-service -n $NAMESPACE || true
    
    print_warning "Rollback completed. Please check the logs for more information."
}

# Main deployment function
main() {
    print_status "Starting PeopleNest HRMS Deployment"
    print_status "==================================="
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            --image-tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            --namespace)
                NAMESPACE="$2"
                shift 2
                ;;
            --skip-migrations)
                SKIP_MIGRATIONS=true
                shift
                ;;
            --skip-health-checks)
                SKIP_HEALTH_CHECKS=true
                shift
                ;;
            --help)
                echo "Usage: $0 [options]"
                echo "Options:"
                echo "  --environment ENV     Set deployment environment (default: production)"
                echo "  --image-tag TAG       Set Docker image tag (default: latest)"
                echo "  --namespace NS        Set Kubernetes namespace (default: peoplenest)"
                echo "  --skip-migrations     Skip database migrations"
                echo "  --skip-health-checks  Skip health checks"
                echo "  --help                Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Set trap for cleanup on failure
    trap cleanup_on_failure ERR
    
    # Run deployment steps
    check_prerequisites
    create_namespace
    apply_secrets
    apply_configmaps
    deploy_databases
    deploy_applications
    deploy_monitoring
    deploy_ingress
    
    if [ "$SKIP_MIGRATIONS" != true ]; then
        run_migrations
        create_admin_user
    fi
    
    if [ "$SKIP_HEALTH_CHECKS" != true ]; then
        run_health_checks
    fi
    
    display_deployment_info
    
    print_success "PeopleNest HRMS deployment completed successfully!"
    print_status "Please update your DNS records to point to the ingress IP address."
    print_status "Default admin credentials: awadhesh / awadhesh123"
    print_warning "Remember to change default passwords and update secrets for production!"
}

# Run main function with all arguments
main "$@"
