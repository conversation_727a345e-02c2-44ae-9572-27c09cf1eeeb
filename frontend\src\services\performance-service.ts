import { apiClient } from './api-client';

export interface PerformanceReview {
  id: string;
  employee: {
    id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
    position: {
      title: string;
      department: {
        name: string;
      };
    };
  };
  reviewer: {
    id: string;
    firstName: string;
    lastName: string;
  };
  reviewPeriod: {
    startDate: string;
    endDate: string;
  };
  status: 'draft' | 'in_progress' | 'completed' | 'approved';
  type: 'annual' | 'quarterly' | 'probationary' | 'project_based';
  overallRating?: number;
  dueDate: string;
  createdAt: string;
  updatedAt: string;
  sections?: Array<{
    id: string;
    title: string;
    rating?: number;
    comments?: string;
    goals?: string[];
  }>;
}

export interface Goal {
  id: string;
  title: string;
  description: string;
  category: 'performance' | 'development' | 'behavioral';
  priority: 'low' | 'medium' | 'high';
  status: 'not_started' | 'in_progress' | 'completed' | 'overdue';
  progress: number;
  targetDate: string;
  createdAt: string;
  updatedAt: string;
  employee: {
    id: string;
    firstName: string;
    lastName: string;
  };
  assignedBy: {
    id: string;
    firstName: string;
    lastName: string;
  };
  milestones?: Array<{
    id: string;
    title: string;
    completed: boolean;
    dueDate: string;
  }>;
  metrics?: Array<{
    id: string;
    name: string;
    targetValue: number;
    currentValue: number;
    unit: string;
  }>;
}

export interface FeedbackRequest {
  id: string;
  employee: {
    id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
  };
  requester: {
    id: string;
    firstName: string;
    lastName: string;
  };
  reviewer: {
    id: string;
    firstName: string;
    lastName: string;
  };
  type: 'self' | 'manager' | 'peer' | 'subordinate';
  status: 'pending' | 'completed';
  dueDate: string;
  createdAt: string;
  completedAt?: string;
  feedback?: {
    rating: number;
    strengths: string;
    improvements: string;
    comments: string;
    competencies: Array<{
      name: string;
      rating: number;
    }>;
  };
}

export interface PerformanceOverview {
  activeReviews: number;
  pendingReviews: number;
  completedReviews: number;
  averageRating: number;
  totalGoals: number;
  completedGoals: number;
  inProgressGoals: number;
  overdueGoals: number;
  pendingFeedback: number;
  completedFeedback: number;
}

class PerformanceService {
  /**
   * Get performance overview/dashboard data
   */
  async getOverview(): Promise<PerformanceOverview> {
    const response = await apiClient.get('/performance/overview');
    return response.data;
  }

  /**
   * Get performance reviews
   */
  async getReviews(params: {
    status?: string;
    employeeId?: string;
    reviewerId?: string;
    page?: number;
    limit?: number;
  } = {}): Promise<PerformanceReview[]> {
    const response = await apiClient.get('/performance/reviews', { params });
    return response.data;
  }

  /**
   * Get performance review by ID
   */
  async getReview(id: string): Promise<PerformanceReview> {
    const response = await apiClient.get(`/performance/reviews/${id}`);
    return response.data;
  }

  /**
   * Create performance review
   */
  async createReview(data: {
    employeeId: string;
    reviewerId: string;
    type: string;
    reviewPeriod: {
      startDate: string;
      endDate: string;
    };
    dueDate: string;
    template?: string;
  }): Promise<PerformanceReview> {
    const response = await apiClient.post('/performance/reviews', data);
    return response.data;
  }

  /**
   * Update performance review
   */
  async updateReview(id: string, data: Partial<PerformanceReview>): Promise<PerformanceReview> {
    const response = await apiClient.patch(`/performance/reviews/${id}`, data);
    return response.data;
  }

  /**
   * Submit performance review
   */
  async submitReview(id: string): Promise<PerformanceReview> {
    const response = await apiClient.post(`/performance/reviews/${id}/submit`);
    return response.data;
  }

  /**
   * Approve performance review
   */
  async approveReview(id: string, comments?: string): Promise<PerformanceReview> {
    const response = await apiClient.post(`/performance/reviews/${id}/approve`, {
      comments,
    });
    return response.data;
  }

  /**
   * Get goals
   */
  async getGoals(params: {
    status?: string;
    employeeId?: string;
    category?: string;
    priority?: string;
    page?: number;
    limit?: number;
  } = {}): Promise<Goal[]> {
    const response = await apiClient.get('/performance/goals', { params });
    return response.data;
  }

  /**
   * Get goal by ID
   */
  async getGoal(id: string): Promise<Goal> {
    const response = await apiClient.get(`/performance/goals/${id}`);
    return response.data;
  }

  /**
   * Create goal
   */
  async createGoal(data: {
    title: string;
    description: string;
    category: string;
    priority: string;
    targetDate: string;
    employeeId: string;
    milestones?: Array<{
      title: string;
      dueDate: string;
    }>;
    metrics?: Array<{
      name: string;
      targetValue: number;
      unit: string;
    }>;
  }): Promise<Goal> {
    const response = await apiClient.post('/performance/goals', data);
    return response.data;
  }

  /**
   * Update goal
   */
  async updateGoal(id: string, data: Partial<Goal>): Promise<Goal> {
    const response = await apiClient.patch(`/performance/goals/${id}`, data);
    return response.data;
  }

  /**
   * Update goal progress
   */
  async updateGoalProgress(id: string, progress: number): Promise<Goal> {
    const response = await apiClient.patch(`/performance/goals/${id}/progress`, {
      progress,
    });
    return response.data;
  }

  /**
   * Complete goal
   */
  async completeGoal(id: string): Promise<Goal> {
    const response = await apiClient.post(`/performance/goals/${id}/complete`);
    return response.data;
  }

  /**
   * Get feedback requests
   */
  async getFeedbackRequests(params: {
    status?: string;
    type?: string;
    employeeId?: string;
    reviewerId?: string;
    page?: number;
    limit?: number;
  } = {}): Promise<FeedbackRequest[]> {
    const response = await apiClient.get('/performance/feedback', { params });
    return response.data;
  }

  /**
   * Get feedback request by ID
   */
  async getFeedbackRequest(id: string): Promise<FeedbackRequest> {
    const response = await apiClient.get(`/performance/feedback/${id}`);
    return response.data;
  }

  /**
   * Create feedback request
   */
  async createFeedbackRequest(data: {
    employeeId: string;
    reviewerId: string;
    type: string;
    dueDate: string;
    message?: string;
  }): Promise<FeedbackRequest> {
    const response = await apiClient.post('/performance/feedback', data);
    return response.data;
  }

  /**
   * Submit feedback
   */
  async submitFeedback(id: string, feedback: {
    rating: number;
    strengths: string;
    improvements: string;
    comments: string;
    competencies: Array<{
      name: string;
      rating: number;
    }>;
  }): Promise<FeedbackRequest> {
    const response = await apiClient.post(`/performance/feedback/${id}/submit`, feedback);
    return response.data;
  }

  /**
   * Get performance analytics
   */
  async getAnalytics(params: {
    timeRange?: string;
    departmentId?: string;
    employeeId?: string;
  } = {}): Promise<{
    performanceTrends: Array<{
      month: string;
      averageRating: number;
      completedReviews: number;
    }>;
    goalCompletionRate: Array<{
      month: string;
      completed: number;
      total: number;
      rate: number;
    }>;
    feedbackMetrics: Array<{
      type: string;
      count: number;
      averageRating: number;
    }>;
    departmentComparison: Array<{
      department: string;
      averageRating: number;
      goalCompletionRate: number;
      employeeCount: number;
    }>;
  }> {
    const response = await apiClient.get('/performance/analytics', { params });
    return response.data;
  }

  /**
   * Get performance insights using AI
   */
  async getInsights(employeeId?: string): Promise<{
    insights: Array<{
      type: 'strength' | 'improvement' | 'risk' | 'opportunity';
      title: string;
      description: string;
      confidence: number;
      recommendations: string[];
    }>;
    predictions: Array<{
      metric: string;
      currentValue: number;
      predictedValue: number;
      confidence: number;
      timeframe: string;
    }>;
  }> {
    const response = await apiClient.get('/performance/insights', {
      params: employeeId ? { employeeId } : {},
    });
    return response.data;
  }

  /**
   * Export performance data
   */
  async exportData(
    type: 'reviews' | 'goals' | 'feedback',
    format: 'csv' | 'xlsx' | 'pdf' = 'xlsx',
    filters?: any
  ): Promise<Blob> {
    const response = await apiClient.get(`/performance/export/${type}`, {
      params: { format, ...filters },
      responseType: 'blob',
    });
    return response.data;
  }

  /**
   * Get performance templates
   */
  async getTemplates(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    type: 'review' | 'goal' | 'feedback';
    sections: Array<{
      title: string;
      questions: string[];
      ratingScale?: {
        min: number;
        max: number;
        labels: string[];
      };
    }>;
  }>> {
    const response = await apiClient.get('/performance/templates');
    return response.data;
  }

  /**
   * Get competency framework
   */
  async getCompetencies(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    category: string;
    levels: Array<{
      level: number;
      title: string;
      description: string;
      behaviors: string[];
    }>;
  }>> {
    const response = await apiClient.get('/performance/competencies');
    return response.data;
  }

  /**
   * Get performance calendar/schedule
   */
  async getPerformanceCalendar(year: number): Promise<Array<{
    id: string;
    title: string;
    type: 'review' | 'goal_setting' | 'feedback' | 'calibration';
    startDate: string;
    endDate: string;
    participants: string[];
    status: 'upcoming' | 'active' | 'completed';
  }>> {
    const response = await apiClient.get('/performance/calendar', {
      params: { year },
    });
    return response.data;
  }
}

export const performanceService = new PerformanceService();
