import { IsString, IsOptional, IsEnum, IsDateString, IsNumber, IsBoolean, ValidateNested, IsObject } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PayrollFrequency, Currency } from '@app/common/enums/status.enum';

export class CreatePayrollPeriodDto {
  @ApiProperty({
    description: 'Payroll period name/title',
    example: 'January 2024 Payroll',
  })
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'Payroll period description',
    example: 'Monthly payroll for January 2024',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Payroll frequency',
    enum: PayrollFrequency,
    example: PayrollFrequency.MONTHLY,
  })
  @IsEnum(PayrollFrequency)
  frequency: PayrollFrequency;

  @ApiProperty({
    description: 'Period start date',
    example: '2024-01-01',
  })
  @IsDateString()
  startDate: string;

  @ApiProperty({
    description: 'Period end date',
    example: '2024-01-31',
  })
  @IsDateString()
  endDate: string;

  @ApiProperty({
    description: 'Pay date',
    example: '2024-02-05',
  })
  @IsDateString()
  payDate: string;

  @ApiPropertyOptional({
    description: 'Cut-off date for timesheet submissions',
    example: '2024-01-30',
  })
  @IsOptional()
  @IsDateString()
  cutoffDate?: string;

  @ApiProperty({
    description: 'Primary currency for this payroll period',
    enum: Currency,
    example: Currency.USD,
  })
  @IsEnum(Currency)
  currency: Currency;

  @ApiPropertyOptional({
    description: 'Whether to include inactive employees',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  includeInactive?: boolean;

  @ApiPropertyOptional({
    description: 'Whether to include probationary employees',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  includeProbationary?: boolean;

  @ApiPropertyOptional({
    description: 'Overtime threshold in hours',
    example: 40,
  })
  @IsOptional()
  @IsNumber()
  overtimeThreshold?: number;

  @ApiPropertyOptional({
    description: 'Payroll configuration settings',
  })
  @IsOptional()
  @IsObject()
  configuration?: {
    includeInactive?: boolean;
    includeProbationary?: boolean;
    overtimeThreshold?: number;
    roundingRules?: {
      hours?: 'up' | 'down' | 'nearest';
      currency?: 'up' | 'down' | 'nearest';
      precision?: number;
    };
    taxSettings?: {
      federalTaxEnabled?: boolean;
      stateTaxEnabled?: boolean;
      localTaxEnabled?: boolean;
    };
  };
}

export class UpdatePayrollPeriodDto {
  @ApiPropertyOptional({
    description: 'Payroll period name/title',
    example: 'January 2024 Payroll - Updated',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Payroll period description',
    example: 'Updated monthly payroll for January 2024',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Period start date',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Period end date',
    example: '2024-01-31',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Pay date',
    example: '2024-02-05',
  })
  @IsOptional()
  @IsDateString()
  payDate?: string;

  @ApiPropertyOptional({
    description: 'Cut-off date for timesheet submissions',
    example: '2024-01-30',
  })
  @IsOptional()
  @IsDateString()
  cutoffDate?: string;

  @ApiPropertyOptional({
    description: 'Primary currency for this payroll period',
    enum: Currency,
    example: Currency.USD,
  })
  @IsOptional()
  @IsEnum(Currency)
  currency?: Currency;

  @ApiPropertyOptional({
    description: 'Payroll configuration settings',
  })
  @IsOptional()
  @IsObject()
  configuration?: {
    includeInactive?: boolean;
    includeProbationary?: boolean;
    overtimeThreshold?: number;
    roundingRules?: {
      hours?: 'up' | 'down' | 'nearest';
      currency?: 'up' | 'down' | 'nearest';
      precision?: number;
    };
    taxSettings?: {
      federalTaxEnabled?: boolean;
      stateTaxEnabled?: boolean;
      localTaxEnabled?: boolean;
    };
  };
}

export class PayrollPeriodQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by payroll frequency',
    enum: PayrollFrequency,
  })
  @IsOptional()
  @IsEnum(PayrollFrequency)
  frequency?: PayrollFrequency;

  @ApiPropertyOptional({
    description: 'Filter by currency',
    enum: Currency,
  })
  @IsOptional()
  @IsEnum(Currency)
  currency?: Currency;

  @ApiPropertyOptional({
    description: 'Filter by start date (from)',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  startDateFrom?: string;

  @ApiPropertyOptional({
    description: 'Filter by start date (to)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  startDateTo?: string;

  @ApiPropertyOptional({
    description: 'Filter by pay date (from)',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  payDateFrom?: string;

  @ApiPropertyOptional({
    description: 'Filter by pay date (to)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  payDateTo?: string;

  @ApiPropertyOptional({
    description: 'Include only active periods',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  activeOnly?: boolean;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Sort field',
    example: 'startDate',
  })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiPropertyOptional({
    description: 'Sort order',
    example: 'DESC',
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC';
}
