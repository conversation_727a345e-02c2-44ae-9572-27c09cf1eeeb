{"name": "peoplenest-hrms", "version": "1.0.0", "description": "AI-Enabled Enterprise Human Resource Management System", "private": true, "workspaces": ["backend", "frontend", "ai-services"], "scripts": {"dev": "concurrently \"npm run backend:dev\" \"npm run frontend:dev\" \"npm run ai:dev\"", "start": "powershell -ExecutionPolicy Bypass -File scripts/start-dev.ps1", "start:backend": "powershell -ExecutionPolicy Bypass -File scripts/start-dev.ps1 -BackendOnly", "start:frontend": "powershell -ExecutionPolicy Bypass -File scripts/start-dev.ps1 -FrontendOnly", "start:ai": "powershell -ExecutionPolicy Bypass -File scripts/start-dev.ps1 -AIOnly", "stop": "powershell -ExecutionPolicy Bypass -File scripts/stop-dev.ps1", "stop:force": "powershell -ExecutionPolicy Bypass -File scripts/stop-dev.ps1 -Force", "stop:infra": "powershell -ExecutionPolicy Bypass -File scripts/stop-dev.ps1 -InfrastructureOnly", "stop:apps": "powershell -ExecutionPolicy Bypass -File scripts/stop-dev.ps1 -ApplicationOnly", "status": "powershell -ExecutionPolicy Bypass -File scripts/status.ps1", "status:detailed": "powershell -ExecutionPolicy Bypass -File scripts/status.ps1 -Detailed", "status:watch": "powershell -ExecutionPolicy Bypass -File scripts/status.ps1 -Watch", "status:json": "powershell -ExecutionPolicy Bypass -File scripts/status.ps1 -Json", "build": "npm run backend:build && npm run frontend:build", "test": "npm run backend:test && npm run frontend:test", "lint": "npm run backend:lint && npm run frontend:lint", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "backend:dev": "cd backend && npm run start:dev", "backend:build": "cd backend && npm run build", "backend:test": "cd backend && npm run test", "backend:test:e2e": "cd backend && npm run test:e2e", "backend:lint": "cd backend && npm run lint", "frontend:dev": "cd frontend && npm run dev", "frontend:build": "cd frontend && npm run build", "frontend:test": "cd frontend && npm run test", "frontend:lint": "cd frontend && npm run lint", "ai:dev": "cd ai-services && python -m uvicorn main:app --reload --port 8000", "ai:train": "cd ai-services && python scripts/train_models.py", "ai:deploy": "cd ai-services && python scripts/deploy_models.py", "db:migrate": "cd backend && npm run migration:run", "db:seed": "cd backend && npm run seed:run", "db:reset": "cd backend && npm run schema:drop && npm run migration:run && npm run seed:run", "db:init": "psql -h localhost -U postgres -d postgres -c \"CREATE DATABASE peoplenest_dev;\" && psql -h localhost -U postgres -d peoplenest_dev -f scripts/init-database.sql", "db:create-admin": "psql -h localhost -U postgres -d peoplenest_dev -f scripts/create-admin-user.sql", "setup": "npm run docker:up && npm run db:init && npm run db:create-admin", "infra:up": "docker-compose up -d", "infra:down": "docker-compose down", "infra:logs": "docker-compose logs -f", "infra:clean": "docker-compose down -v --remove-orphans", "security:audit": "npm audit --audit-level moderate", "security:scan": "snyk test", "admin:create": "cd backend && npm run admin:create", "admin:list": "cd backend && npm run admin:list", "admin:password": "cd backend && npm run admin:password", "admin:activate": "cd backend && npm run admin:activate", "admin:deactivate": "cd backend && npm run admin:deactivate"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "concurrently": "^8.2.2", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.29.0", "husky": "^8.0.3", "lint-staged": "^15.1.0", "prettier": "^3.1.0", "snyk": "^1.1248.0", "typescript": "^5.3.0"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/peoplenest-hrms.git"}, "keywords": ["hrms", "human-resources", "ai", "machine-learning", "enterprise", "<PERSON><PERSON><PERSON>", "react", "typescript", "microservices"], "author": "PeopleNest Team", "license": "MIT", "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test"}}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}