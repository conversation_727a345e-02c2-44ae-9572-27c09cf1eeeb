{"name": "@storybook/icons", "version": "1.4.0", "description": "Icon library from the Storybook team", "author": {"name": "c<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/storybookjs/icons.git"}, "scripts": {"dev": "concurrently  -n \"Build,SB\" \"pnpm build --watch\" \"pnpm storybook\" ", "build": "tsup --onSuccess \"cp -a public/. dist/public\"", "type-check": "tsc", "lint": "eslint --ignore-path .gitignore \"src/**/*.+(ts|js|tsx)\"", "lint:fix": "pnpm lint --fix && prettier --write .", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "release": "pnpm build && auto shipit", "chromatic": "npx chromatic --project-token=chpt_d0c5927e55681dd", "generate-icons": "ts-node ./src/generate.ts"}, "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "exports": {".": {"require": "./dist/index.js", "import": "./dist/index.mjs", "types": "./dist/index.d.ts"}}, "types": "./dist/index.d.ts", "files": ["dist"], "lint-staged": {"./src/**/*.{ts,js,jsx,tsx}": ["eslint --ignore-path .gitignore --fix"], "*": "prettier --write"}, "engines": {"node": ">=14.0.0"}, "devDependencies": {"@chromatic-com/storybook": "^1", "@storybook/addon-a11y": "^8.1.11", "@storybook/addon-essentials": "^8.1.11", "@storybook/addon-interactions": "^8.1.11", "@storybook/components": "^8.1.11", "@storybook/blocks": "^8.1.11", "@storybook/eslint-config-storybook": "^4.0.0", "@storybook/react": "^8.1.11", "@storybook/react-vite": "^8.1.11", "@storybook/test": "^8.1.11", "@storybook/theming": "^8.1.11", "@svgr/core": "5.5.0", "@svgr/plugin-prettier": "5.5.0", "@svgr/plugin-svgo": "5.5.0", "@types/fs-extra": "11.0.1", "@types/node": "20.4.1", "@types/react": "18.2.14", "@types/react-dom": "18.0.11", "@typescript-eslint/eslint-plugin": "5.55.0", "@typescript-eslint/parser": "5.55.0", "@vitejs/plugin-react": "3.1.0", "auto": "11.1.1", "axios": "1.4.0", "chalk": "4.1.2", "chromatic": "11.0.4", "concurrently": "8.0.1", "dotenv": "16.3.1", "eslint": "8.36.0", "eslint-plugin-jest": "27.2.1", "eslint-plugin-storybook": "^0.8.0", "figma-api-exporter": "0.0.2", "figma-js": "1.16.0", "figma-transformer": "2.1.0", "fs-extra": "11.1.1", "lint-staged": "13.2.0", "prettier": "2.8.4", "react": "18.2.0", "react-dom": "18.2.0", "storybook": "^8.1.11", "ts-node": "10.9.1", "tsup": "6.6.3", "typescript": "5.0.2", "vite": "4.2.1"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0-beta", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0-beta"}, "bugs": {"url": "https://github.com/storybookjs/icons/issues"}, "homepage": "https://github.com/storybookjs/icons#readme", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "auto": {"plugins": ["npm"]}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}