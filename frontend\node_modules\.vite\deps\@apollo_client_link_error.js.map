{"version": 3, "sources": ["../../../../node_modules/@apollo/src/link/error/index.ts"], "sourcesContent": ["import type { FormattedExecutionResult, GraphQLFormattedError } from \"graphql\";\n\nimport {\n  graphQLResultHasProtocolErrors,\n  PROTOCOL_ERRORS_SYMBOL,\n} from \"../../errors/index.js\";\nimport type { NetworkError } from \"../../errors/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport type { Operation, FetchResult, NextLink } from \"../core/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\n\nexport interface ErrorResponse {\n  /**\n   * Errors returned in the `errors` property of the GraphQL response.\n   */\n  graphQLErrors?: ReadonlyArray<GraphQLFormattedError>;\n  /**\n   * Errors thrown during a network request. This is usually an error thrown\n   * during a `fetch` call or an error while parsing the response from the\n   * network.\n   */\n  networkError?: NetworkError;\n  /**\n   * Fatal transport-level errors from multipart subscriptions.\n   * See the [multipart subscription protocol](https://www.apollographql.com/docs/graphos/routing/operations/subscriptions/multipart-protocol#message-and-error-format) for more information.\n   */\n  protocolErrors?: ReadonlyArray<GraphQLFormattedError>;\n  response?: FormattedExecutionResult;\n  operation: Operation;\n  forward: NextLink;\n}\n\nexport namespace ErrorLink {\n  /**\n   * Callback to be triggered when an error occurs within the link stack.\n   */\n  export interface ErrorHandler {\n    (error: ErrorResponse): Observable<FetchResult> | void;\n  }\n}\n\n// For backwards compatibility.\nexport import ErrorHandler = ErrorLink.ErrorHandler;\n\nexport function onError(errorHandler: ErrorHandler): ApolloLink {\n  return new ApolloLink((operation, forward) => {\n    return new Observable((observer) => {\n      let sub: any;\n      let retriedSub: any;\n      let retriedResult: any;\n\n      try {\n        sub = forward(operation).subscribe({\n          next: (result) => {\n            if (result.errors) {\n              retriedResult = errorHandler({\n                graphQLErrors: result.errors,\n                response: result,\n                operation,\n                forward,\n              });\n            } else if (graphQLResultHasProtocolErrors(result)) {\n              retriedResult = errorHandler({\n                protocolErrors: result.extensions[PROTOCOL_ERRORS_SYMBOL],\n                response: result,\n                operation,\n                forward,\n              });\n            }\n\n            if (retriedResult) {\n              retriedSub = retriedResult.subscribe({\n                next: observer.next.bind(observer),\n                error: observer.error.bind(observer),\n                complete: observer.complete.bind(observer),\n              });\n              return;\n            }\n\n            observer.next(result);\n          },\n          error: (networkError) => {\n            retriedResult = errorHandler({\n              operation,\n              networkError,\n              //Network errors can return GraphQL errors on for example a 403\n              graphQLErrors:\n                (networkError &&\n                  networkError.result &&\n                  networkError.result.errors) ||\n                void 0,\n              forward,\n            });\n            if (retriedResult) {\n              retriedSub = retriedResult.subscribe({\n                next: observer.next.bind(observer),\n                error: observer.error.bind(observer),\n                complete: observer.complete.bind(observer),\n              });\n              return;\n            }\n            observer.error(networkError);\n          },\n          complete: () => {\n            // disable the previous sub from calling complete on observable\n            // if retry is in flight.\n            if (!retriedResult) {\n              observer.complete.bind(observer)();\n            }\n          },\n        });\n      } catch (e) {\n        errorHandler({ networkError: e as Error, operation, forward });\n        observer.error(e);\n      }\n\n      return () => {\n        if (sub) sub.unsubscribe();\n        if (retriedSub) sub.unsubscribe();\n      };\n    });\n  });\n}\n\nexport class ErrorLink extends ApolloLink {\n  private link: ApolloLink;\n  constructor(errorHandler: ErrorLink.ErrorHandler) {\n    super();\n    this.link = onError(errorHandler);\n  }\n\n  public request(\n    operation: Operation,\n    forward: NextLink\n  ): Observable<FetchResult> | null {\n    return this.link.request(operation, forward);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;AA4CM,SAAU,QAAQ,cAA0B;AAChD,SAAO,IAAI,WAAW,SAAC,WAAW,SAAO;AACvC,WAAO,IAAI,WAAW,SAAC,UAAQ;AAC7B,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI;AACF,cAAM,QAAQ,SAAS,EAAE,UAAU;UACjC,MAAM,SAAC,QAAM;AACX,gBAAI,OAAO,QAAQ;AACjB,8BAAgB,aAAa;gBAC3B,eAAe,OAAO;gBACtB,UAAU;gBACV;gBACA;eACD;YACH,WAAW,+BAA+B,MAAM,GAAG;AACjD,8BAAgB,aAAa;gBAC3B,gBAAgB,OAAO,WAAW,sBAAsB;gBACxD,UAAU;gBACV;gBACA;eACD;YACH;AAEA,gBAAI,eAAe;AACjB,2BAAa,cAAc,UAAU;gBACnC,MAAM,SAAS,KAAK,KAAK,QAAQ;gBACjC,OAAO,SAAS,MAAM,KAAK,QAAQ;gBACnC,UAAU,SAAS,SAAS,KAAK,QAAQ;eAC1C;AACD;YACF;AAEA,qBAAS,KAAK,MAAM;UACtB;UACA,OAAO,SAAC,cAAY;AAClB,4BAAgB,aAAa;cAC3B;cACA;;cAEA,eACG,gBACC,aAAa,UACb,aAAa,OAAO,UACtB;cACF;aACD;AACD,gBAAI,eAAe;AACjB,2BAAa,cAAc,UAAU;gBACnC,MAAM,SAAS,KAAK,KAAK,QAAQ;gBACjC,OAAO,SAAS,MAAM,KAAK,QAAQ;gBACnC,UAAU,SAAS,SAAS,KAAK,QAAQ;eAC1C;AACD;YACF;AACA,qBAAS,MAAM,YAAY;UAC7B;UACA,UAAU,WAAA;AAGR,gBAAI,CAAC,eAAe;AAClB,uBAAS,SAAS,KAAK,QAAQ,EAAC;YAClC;UACF;SACD;MACH,SAAS,GAAG;AACV,qBAAa,EAAE,cAAc,GAAY,WAAW,QAAO,CAAE;AAC7D,iBAAS,MAAM,CAAC;MAClB;AAEA,aAAO,WAAA;AACL,YAAI;AAAK,cAAI,YAAW;AACxB,YAAI;AAAY,cAAI,YAAW;MACjC;IACF,CAAC;EACH,CAAC;AACH;AAEA,IAAA;;EAAA,SAAA,QAAA;AAA+B,cAAAA,YAAA,MAAA;AAE7B,aAAAA,WAAY,cAAoC;AAC9C,UAAA,QAAA,OAAK,KAAA,IAAA,KAAE;AACP,YAAK,OAAO,QAAQ,YAAY;;IAClC;AAEO,IAAAA,WAAA,UAAA,UAAP,SACE,WACA,SAAiB;AAEjB,aAAO,KAAK,KAAK,QAAQ,WAAW,OAAO;IAC7C;AACF,WAAAA;EAAA,EAb+B,UAAU;;", "names": ["ErrorLink"]}