import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';

export enum AccrualMethod {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  ANNUALLY = 'annually',
  PER_PAY_PERIOD = 'per_pay_period',
  FRONT_LOADED = 'front_loaded',
  NO_ACCRUAL = 'no_accrual',
}

@Entity('leave_types')
@Index(['tenantId', 'isActive'])
export class LeaveType {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ length: 10, unique: true })
  code: string;

  @Column({ type: 'boolean', default: true, name: 'is_active' })
  isActive: boolean;

  @Column({ type: 'boolean', default: true, name: 'is_paid' })
  isPaid: boolean;

  @Column({ type: 'boolean', default: false, name: 'requires_approval' })
  requiresApproval: boolean;

  @Column({ type: 'boolean', default: false, name: 'requires_documentation' })
  requiresDocumentation: boolean;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true, name: 'max_days_per_year' })
  maxDaysPerYear: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true, name: 'max_consecutive_days' })
  maxConsecutiveDays: number;

  @Column({ type: 'integer', nullable: true, name: 'min_notice_days' })
  minNoticeDays: number;

  @Column({
    type: 'enum',
    enum: AccrualMethod,
    default: AccrualMethod.MONTHLY,
    name: 'accrual_method',
  })
  accrualMethod: AccrualMethod;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true, name: 'accrual_rate' })
  accrualRate: number;

  @Column({ type: 'boolean', default: false, name: 'can_carry_forward' })
  canCarryForward: boolean;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true, name: 'max_carry_forward' })
  maxCarryForward: number;

  @Column({ type: 'boolean', default: false, name: 'can_cash_out' })
  canCashOut: boolean;

  @Column({ type: 'json', nullable: true, name: 'eligibility_rules' })
  eligibilityRules: {
    minTenureMonths?: number;
    employeeTypes?: string[];
    departments?: string[];
    positions?: string[];
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;
}
