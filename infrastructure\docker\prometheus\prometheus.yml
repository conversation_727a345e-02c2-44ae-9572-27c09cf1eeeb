global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # API Gateway
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['host.docker.internal:3000']
    metrics_path: '/api/v1/metrics'
    scrape_interval: 30s

  # Auth Service
  - job_name: 'auth-service'
    static_configs:
      - targets: ['host.docker.internal:3001']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Employee Service
  - job_name: 'employee-service'
    static_configs:
      - targets: ['host.docker.internal:3002']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Payroll Service
  - job_name: 'payroll-service'
    static_configs:
      - targets: ['host.docker.internal:3003']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Performance Service
  - job_name: 'performance-service'
    static_configs:
      - targets: ['host.docker.internal:3004']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # AI Services
  - job_name: 'ai-services'
    static_configs:
      - targets: ['host.docker.internal:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # PostgreSQL
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 60s

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 60s

  # Node Exporter (if available)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['host.docker.internal:9100']
    scrape_interval: 60s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093
