import { Entity, Column, Index, ManyToOne, JoinColumn } from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { Employee } from './employee.entity';

export enum SkillLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert',
}

@Entity('employee_skills')
@Index(['employeeId', 'skillName'])
export class EmployeeSkill extends TenantAwareEntity {
  @Column({ type: 'uuid', comment: 'Employee ID' })
  employeeId: string;

  @Column({ type: 'varchar', length: 255, comment: 'Skill name' })
  skillName: string;

  @Column({ type: 'enum', enum: SkillLevel, comment: 'Skill level' })
  level: SkillLevel;

  @Column({ type: 'int', nullable: true, comment: 'Years of experience' })
  yearsOfExperience?: number;

  @Column({ type: 'boolean', default: false, comment: 'Certified in this skill' })
  isCertified: boolean;

  @ManyToOne(() => Employee, employee => employee.skills, { eager: false })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;
}
