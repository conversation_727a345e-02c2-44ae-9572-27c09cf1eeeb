{"name": "@storybook/addon-docs", "version": "8.6.14", "description": "Document component usage and properties in Markdown", "keywords": ["addon", "notes", "documentation", "storybook", "essentials", "organize"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/docs", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/docs"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./preview": {"types": "./dist/preview.d.ts", "import": "./dist/preview.mjs", "require": "./dist/preview.js"}, "./preset": {"types": "./dist/preset.d.ts", "import": "./dist/preset.js", "require": "./dist/preset.js"}, "./blocks": {"types": "./dist/blocks.d.ts", "import": "./dist/blocks.mjs", "require": "./dist/blocks.js"}, "./dist/preview": {"types": "./dist/preview.d.ts", "import": "./dist/preview.mjs", "require": "./dist/preview.js"}, "./dist/preset": {"types": "./dist/preset.d.ts", "require": "./dist/preset.js"}, "./dist/shims/mdx-react-shim": {"types": "./dist/shims/mdx-react-shim.d.ts", "import": "./dist/shims/mdx-react-shim.mjs", "require": "./dist/shims/mdx-react-shim.js"}, "./mdx-react-shim": {"types": "./dist/shims/mdx-react-shim.d.ts", "import": "./dist/shims/mdx-react-shim.mjs", "require": "./dist/shims/mdx-react-shim.js"}, "./mdx-loader": "./dist/mdx-loader.js", "./svelte/HOC.svelte": "./svelte/HOC.svelte", "./ember": "./ember/index.js", "./ember/index.js": "./ember/index.js", "./angular": "./angular/index.js", "./angular/index.js": "./angular/index.js", "./web-components/index.js": "./web-components/index.js", "./package.json": "./package.json", "./manager": {"types": "./dist/manager.d.ts", "import": "./dist/manager.mjs", "require": "./dist/manager.js"}}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/index.d.ts"], "angular": ["angular/index.d.ts"], "blocks": ["dist/blocks.d.ts"], "ember": ["ember/index.d.ts"], "preview": ["dist/preview.d.ts"]}}, "files": ["dist/**/*", "angular/**/*", "common/**/*", "ember/**/*", "html/**/*", "svelte/**/*", "react/**/*", "vue/**/*", "web-components/**/*", "lit/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/bundle.ts"}, "dependencies": {"@mdx-js/react": "^3.0.0", "@storybook/blocks": "8.6.14", "@storybook/csf-plugin": "8.6.14", "@storybook/react-dom-shim": "8.6.14", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "ts-dedent": "^2.0.0"}, "devDependencies": {"@mdx-js/mdx": "^3.0.0", "@rollup/pluginutils": "^5.0.2", "@types/react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "rehype-external-links": "^3.0.0", "rehype-slug": "^6.0.0", "typescript": "^5.7.3", "vite": "^4.0.4"}, "peerDependencies": {"storybook": "^8.6.14"}, "publishConfig": {"access": "public"}, "bundler": {"entries": ["./src/index.ts", "./src/preset.ts", "./src/preview.ts", "./src/blocks.ts", "./src/shims/mdx-react-shim.ts", "./src/mdx-loader.ts", "./src/manager.tsx"], "managerEntries": ["./src/manager.tsx"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae16", "storybook": {"displayName": "Docs", "icon": "https://user-images.githubusercontent.com/263385/101991672-48355c80-3c7c-11eb-82d9-95fa12438f64.png", "unsupportedFrameworks": ["react-native"]}}