import { entry_preview_exports } from './chunk-CQ7IB4U6.mjs';
import { entry_preview_argtypes_exports } from './chunk-N3U7HZRZ.mjs';
import { entry_preview_docs_exports } from './chunk-AWLHXXSE.mjs';
import { normalizeProjectAnnotations, composeConfigs, getCoreAnnotations } from 'storybook/preview-api';

function __definePreview(input){let composed,preview={_tag:"Preview",input,get composed(){if(composed)return composed;let{addons,...rest}=input;return composed=normalizeProjectAnnotations(composeConfigs([...getCoreAnnotations(),...addons??[],rest])),composed},meta(meta){return defineMeta(meta,this)}};return globalThis.globalProjectAnnotations=preview.composed,preview}function defineMeta(input,preview){return {_tag:"Meta",input,preview,get composed(){throw new Error("Not implemented")},story(story){return defineStory(story,this)}}}function defineStory(input,meta){return {_tag:"Story",input,meta,get composed(){throw new Error("Not implemented")}}}function __definePreview2(preview){return __definePreview({...preview,addons:[entry_preview_exports,entry_preview_argtypes_exports,entry_preview_docs_exports,...preview.addons??[]]})}

export { __definePreview2 as __definePreview };
