import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { Employee } from './employee.entity';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';
import { Feedback } from './feedback.entity';
import { Goal } from './goal.entity';

export enum ReviewType {
  ANNUAL = 'annual',
  QUARTERLY = 'quarterly',
  MONTHLY = 'monthly',
  PROBATIONARY = 'probationary',
  PROJECT_BASED = 'project_based',
  PROMOTION = 'promotion',
  DISCIPLINARY = 'disciplinary',
}

export enum ReviewStatus {
  DRAFT = 'draft',
  PENDING_SELF_ASSESSMENT = 'pending_self_assessment',
  PENDING_MANAGER_REVIEW = 'pending_manager_review',
  PENDING_HR_REVIEW = 'pending_hr_review',
  PENDING_CALIBRATION = 'pending_calibration',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum OverallRating {
  EXCEEDS_EXPECTATIONS = 'exceeds_expectations',
  MEETS_EXPECTATIONS = 'meets_expectations',
  PARTIALLY_MEETS_EXPECTATIONS = 'partially_meets_expectations',
  BELOW_EXPECTATIONS = 'below_expectations',
  UNSATISFACTORY = 'unsatisfactory',
}

@Entity('performance_reviews')
@Index(['tenantId', 'employeeId'])
@Index(['tenantId', 'reviewPeriodStart', 'reviewPeriodEnd'])
@Index(['tenantId', 'status'])
@Index(['tenantId', 'reviewType'])
export class PerformanceReview {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ name: 'employee_id' })
  employeeId: string;

  @ManyToOne(() => Employee, { eager: true })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;

  @Column({ name: 'reviewer_id', nullable: true })
  reviewerId: string;

  @ManyToOne(() => Employee, { nullable: true })
  @JoinColumn({ name: 'reviewer_id' })
  reviewer: Employee;

  @Column({ name: 'hr_reviewer_id', nullable: true })
  hrReviewerId: string;

  @ManyToOne(() => Employee, { nullable: true })
  @JoinColumn({ name: 'hr_reviewer_id' })
  hrReviewer: Employee;

  @Column({
    type: 'enum',
    enum: ReviewType,
    name: 'review_type',
  })
  reviewType: ReviewType;

  @Column({
    type: 'enum',
    enum: ReviewStatus,
    default: ReviewStatus.DRAFT,
  })
  status: ReviewStatus;

  @Column({ name: 'review_period_start', type: 'date' })
  reviewPeriodStart: Date;

  @Column({ name: 'review_period_end', type: 'date' })
  reviewPeriodEnd: Date;

  @Column({ name: 'due_date', type: 'date', nullable: true })
  dueDate: Date;

  @Column({ name: 'completed_date', type: 'timestamp', nullable: true })
  completedDate: Date;

  // Self Assessment
  @Column({ type: 'text', nullable: true, name: 'self_assessment' })
  selfAssessment: string;

  @Column({ type: 'json', nullable: true, name: 'self_ratings' })
  selfRatings: {
    [competencyId: string]: {
      rating: number;
      comments?: string;
    };
  };

  @Column({ name: 'self_assessment_completed_at', type: 'timestamp', nullable: true })
  selfAssessmentCompletedAt: Date;

  // Manager Review
  @Column({ type: 'text', nullable: true, name: 'manager_assessment' })
  managerAssessment: string;

  @Column({ type: 'json', nullable: true, name: 'manager_ratings' })
  managerRatings: {
    [competencyId: string]: {
      rating: number;
      comments?: string;
    };
  };

  @Column({ name: 'manager_review_completed_at', type: 'timestamp', nullable: true })
  managerReviewCompletedAt: Date;

  // HR Review
  @Column({ type: 'text', nullable: true, name: 'hr_comments' })
  hrComments: string;

  @Column({ name: 'hr_review_completed_at', type: 'timestamp', nullable: true })
  hrReviewCompletedAt: Date;

  // Overall Assessment
  @Column({
    type: 'enum',
    enum: OverallRating,
    nullable: true,
    name: 'overall_rating',
  })
  overallRating: OverallRating;

  @Column({ type: 'decimal', precision: 3, scale: 2, nullable: true, name: 'overall_score' })
  overallScore: number;

  // Development and Goals
  @Column({ type: 'text', nullable: true, name: 'development_plan' })
  developmentPlan: string;

  @Column({ type: 'json', nullable: true, name: 'strengths' })
  strengths: string[];

  @Column({ type: 'json', nullable: true, name: 'improvement_areas' })
  improvementAreas: string[];

  @Column({ type: 'json', nullable: true, name: 'career_aspirations' })
  careerAspirations: string[];

  // Compensation and Promotion
  @Column({ type: 'boolean', default: false, name: 'promotion_recommended' })
  promotionRecommended: boolean;

  @Column({ type: 'text', nullable: true, name: 'promotion_justification' })
  promotionJustification: string;

  @Column({ type: 'boolean', default: false, name: 'salary_increase_recommended' })
  salaryIncreaseRecommended: boolean;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true, name: 'salary_increase_percentage' })
  salaryIncreasePercentage: number;

  @Column({ type: 'text', nullable: true, name: 'compensation_justification' })
  compensationJustification: string;

  // AI Insights
  @Column({ type: 'json', nullable: true, name: 'ai_insights' })
  aiInsights: {
    sentimentAnalysis?: {
      overallSentiment: string;
      confidence: number;
      keyThemes: string[];
    };
    performancePrediction?: {
      futurePerformance: string;
      confidence: number;
      riskFactors: string[];
    };
    recommendations?: string[];
  };

  // Metadata
  @Column({ type: 'json', nullable: true })
  metadata: {
    template?: string;
    customFields?: Record<string, any>;
    workflow?: {
      currentStep: string;
      completedSteps: string[];
      nextSteps: string[];
    };
  };

  @OneToMany(() => Feedback, feedback => feedback.performanceReview)
  feedback: Feedback[];

  @OneToMany(() => Goal, goal => goal.performanceReview)
  goals: Goal[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater: User;
}
