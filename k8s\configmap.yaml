apiVersion: v1
kind: ConfigMap
metadata:
  name: peoplenest-config
  namespace: peoplenest
  labels:
    app.kubernetes.io/name: peoplenest
    app.kubernetes.io/component: config
data:
  # Application Configuration
  NODE_ENV: "production"
  LOG_LEVEL: "info"
  
  # Database Configuration
  DATABASE_HOST: "postgres-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "peoplenest_prod"
  DATABASE_SSL: "true"
  DATABASE_LOGGING: "false"
  DATABASE_SYNCHRONIZE: "false"
  DATABASE_MIGRATIONS_RUN: "true"
  
  # MongoDB Configuration
  MONGODB_HOST: "mongodb-service"
  MONGODB_PORT: "27017"
  MONGODB_DATABASE: "peoplenest_docs"
  
  # Redis Configuration
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  REDIS_DB: "0"
  REDIS_TTL: "3600"
  
  # Kafka Configuration
  KAFKA_BROKERS: "kafka-service:9092"
  KAFKA_CLIENT_ID: "peoplenest-backend"
  
  # API Configuration
  API_PORT: "3001"
  API_PREFIX: "api"
  API_VERSION: "v1"
  CORS_ORIGIN: "https://app.peoplenest.com"
  
  # GraphQL Configuration
  GRAPHQL_PLAYGROUND: "false"
  GRAPHQL_INTROSPECTION: "false"
  
  # Rate Limiting
  RATE_LIMIT_TTL: "60"
  RATE_LIMIT_LIMIT: "100"
  
  # File Upload
  MAX_FILE_SIZE: "10485760"
  UPLOAD_DEST: "/app/uploads"
  
  # Email Configuration
  EMAIL_FROM: "<EMAIL>"
  EMAIL_REPLY_TO: "<EMAIL>"
  
  # AI Service Configuration
  AI_SERVICE_URL: "http://ai-service:8003"
  AI_SERVICE_TIMEOUT: "30000"
  
  # Monitoring
  METRICS_ENABLED: "true"
  HEALTH_CHECK_ENABLED: "true"
  
  # Security
  BCRYPT_ROUNDS: "12"
  SESSION_TIMEOUT: "3600"
  MAX_LOGIN_ATTEMPTS: "5"
  LOCKOUT_DURATION: "900"
  
  # Compliance
  GDPR_ENABLED: "true"
  AUDIT_ENABLED: "true"
  DATA_RETENTION_DAYS: "2555"
  
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-config
  namespace: peoplenest
  labels:
    app.kubernetes.io/name: peoplenest
    app.kubernetes.io/component: frontend-config
data:
  # Frontend Environment Variables
  VITE_API_URL: "https://api.peoplenest.com"
  VITE_GRAPHQL_URL: "https://api.peoplenest.com/graphql"
  VITE_APP_NAME: "PeopleNest HRMS"
  VITE_APP_VERSION: "1.0.0"
  VITE_ENVIRONMENT: "production"
  
  # Feature Flags
  VITE_ENABLE_ANALYTICS: "true"
  VITE_ENABLE_CHAT: "true"
  VITE_ENABLE_NOTIFICATIONS: "true"
  
  # External Services
  VITE_SENTRY_DSN: ""
  VITE_ANALYTICS_ID: ""
  
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-service-config
  namespace: peoplenest
  labels:
    app.kubernetes.io/name: peoplenest
    app.kubernetes.io/component: ai-service-config
data:
  # AI Service Configuration
  AI_SERVICE_HOST: "0.0.0.0"
  AI_SERVICE_PORT: "8003"
  DEBUG: "false"
  ENVIRONMENT: "production"
  
  # Database
  DATABASE_POOL_SIZE: "10"
  DATABASE_MAX_OVERFLOW: "20"
  
  # AI/ML Configuration
  MODEL_CACHE_DIR: "/app/models"
  ENABLE_GPU: "false"
  MAX_BATCH_SIZE: "32"
  MODEL_TIMEOUT: "30"
  
  # Authentication
  JWT_ALGORITHM: "HS256"
  AUTH_SERVICE_URL: "http://backend-service:3001"
  
  # Monitoring
  ENABLE_METRICS: "true"
  LOG_LEVEL: "INFO"
  
  # Performance
  WORKER_PROCESSES: "4"
  MAX_REQUESTS: "1000"
  MAX_REQUESTS_JITTER: "100"
