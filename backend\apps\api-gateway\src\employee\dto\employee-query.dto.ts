import {
  Is<PERSON>ptional,
  IsString,
  <PERSON><PERSON><PERSON>,
  IsUUID,
  IsDateString,
  IsNumberString,
  IsBoolean,
  Min,
  Max,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  EmployeeStatus,
  EmploymentType,
  Gender,
  MaritalStatus,
} from '@app/common/enums/status.enum';

export class EmployeeQueryDto {
  @ApiPropertyOptional({ description: 'Search term for name, email, or employee ID' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: 'Filter by employee status', enum: EmployeeStatus })
  @IsOptional()
  @IsEnum(EmployeeStatus)
  status?: EmployeeStatus;

  @ApiPropertyOptional({ description: 'Filter by employment type', enum: EmploymentType })
  @IsOptional()
  @IsEnum(EmploymentType)
  employmentType?: EmploymentType;

  @ApiPropertyOptional({ description: 'Filter by department ID' })
  @IsOptional()
  @IsUUID()
  departmentId?: string;

  @ApiPropertyOptional({ description: 'Filter by position ID' })
  @IsOptional()
  @IsUUID()
  positionId?: string;

  @ApiPropertyOptional({ description: 'Filter by manager ID' })
  @IsOptional()
  @IsUUID()
  managerId?: string;

  @ApiPropertyOptional({ description: 'Filter by gender', enum: Gender })
  @IsOptional()
  @IsEnum(Gender)
  gender?: Gender;

  @ApiPropertyOptional({ description: 'Filter by marital status', enum: MaritalStatus })
  @IsOptional()
  @IsEnum(MaritalStatus)
  maritalStatus?: MaritalStatus;

  @ApiPropertyOptional({ description: 'Filter by work location' })
  @IsOptional()
  @IsString()
  workLocation?: string;

  @ApiPropertyOptional({ description: 'Filter by joining date from (YYYY-MM-DD)' })
  @IsOptional()
  @IsDateString()
  joiningDateFrom?: string;

  @ApiPropertyOptional({ description: 'Filter by joining date to (YYYY-MM-DD)' })
  @IsOptional()
  @IsDateString()
  joiningDateTo?: string;

  @ApiPropertyOptional({ description: 'Filter by minimum salary' })
  @IsOptional()
  @IsNumberString()
  minSalary?: number;

  @ApiPropertyOptional({ description: 'Filter by maximum salary' })
  @IsOptional()
  @IsNumberString()
  maxSalary?: number;

  @ApiPropertyOptional({ description: 'Filter by skills (comma-separated)' })
  @IsOptional()
  @IsString()
  skills?: string;

  @ApiPropertyOptional({ description: 'Include only active employees', default: false })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  activeOnly?: boolean;

  @ApiPropertyOptional({ description: 'Include employees in probation period', default: false })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  probationOnly?: boolean;

  @ApiPropertyOptional({ description: 'Page number', default: 1, minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Items per page', default: 20, minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({ 
    description: 'Sort field', 
    enum: ['firstName', 'lastName', 'email', 'employeeId', 'dateOfJoining', 'status', 'department', 'position'],
    default: 'firstName'
  })
  @IsOptional()
  @IsEnum(['firstName', 'lastName', 'email', 'employeeId', 'dateOfJoining', 'status', 'department', 'position'])
  sortBy?: string = 'firstName';

  @ApiPropertyOptional({ description: 'Sort order', enum: ['ASC', 'DESC'], default: 'ASC' })
  @IsOptional()
  @IsEnum(['ASC', 'DESC'])
  sortOrder?: 'ASC' | 'DESC' = 'ASC';

  @ApiPropertyOptional({ description: 'Include related data', type: [String] })
  @IsOptional()
  @IsString()
  include?: string; // comma-separated: contacts,addresses,emergencyContacts,education,experience,skills,certifications

  @ApiPropertyOptional({ description: 'Export format', enum: ['json', 'csv', 'excel'] })
  @IsOptional()
  @IsEnum(['json', 'csv', 'excel'])
  export?: string;
}

export class EmployeeStatsQueryDto {
  @ApiPropertyOptional({ description: 'Department ID for department-specific stats' })
  @IsOptional()
  @IsUUID()
  departmentId?: string;

  @ApiPropertyOptional({ description: 'Date range from (YYYY-MM-DD)' })
  @IsOptional()
  @IsDateString()
  dateFrom?: string;

  @ApiPropertyOptional({ description: 'Date range to (YYYY-MM-DD)' })
  @IsOptional()
  @IsDateString()
  dateTo?: string;

  @ApiPropertyOptional({ description: 'Include detailed breakdown', default: false })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  detailed?: boolean;
}

export class BulkEmployeeActionDto {
  @ApiPropertyOptional({ description: 'Employee IDs for bulk action', type: [String] })
  @IsOptional()
  @IsUUID('4', { each: true })
  employeeIds?: string[];

  @ApiPropertyOptional({ description: 'Action to perform', enum: ['activate', 'deactivate', 'terminate', 'export'] })
  @IsEnum(['activate', 'deactivate', 'terminate', 'export'])
  action: string;

  @ApiPropertyOptional({ description: 'Reason for bulk action' })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiPropertyOptional({ description: 'Effective date for action (YYYY-MM-DD)' })
  @IsOptional()
  @IsDateString()
  effectiveDate?: string;

  @ApiPropertyOptional({ description: 'Send notification to affected employees', default: true })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  sendNotification?: boolean = true;
}
