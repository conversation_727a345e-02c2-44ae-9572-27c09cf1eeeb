import {
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';
import { Observable } from 'rxjs';

import { IS_PUBLIC_KEY } from '../decorators/public.decorator';
import { SessionService } from '../services/session.service';
import { AuditService } from '../services/audit.service';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(
    private reflector: Reflector,
    private sessionService: SessionService,
    private auditService: AuditService,
  ) {
    super();
  }

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    return super.canActivate(context);
  }

  async handleRequest(
    err: any,
    user: any,
    info: any,
    context: ExecutionContext,
  ) {
    const request = context.switchToHttp().getRequest();
    
    // Log authentication attempt
    await this.auditService.logAuthAttempt({
      userId: user?.id,
      ip: request.ip,
      userAgent: request.get('User-Agent'),
      success: !err && !!user,
      error: err?.message || info?.message,
      timestamp: new Date(),
    });

    if (err || !user) {
      throw err || new UnauthorizedException('Invalid or expired token');
    }

    // Check if user account is active
    if (user.status !== 'active') {
      throw new ForbiddenException(`Account is ${user.status}`);
    }

    // Check session validity
    const sessionId = request.headers['x-session-id'];
    if (sessionId) {
      const isValidSession = await this.sessionService.validateSession(
        user.id,
        sessionId,
      );
      
      if (!isValidSession) {
        throw new UnauthorizedException('Invalid session');
      }

      // Update session activity
      await this.sessionService.updateSessionActivity(sessionId);
    }

    // Add user context to request
    request.user = user;
    request.sessionId = sessionId;

    return user;
  }
}
