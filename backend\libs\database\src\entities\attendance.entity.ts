import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyTo<PERSON>ne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { Employee } from './employee.entity';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';

export enum AttendanceStatus {
  PRESENT = 'present',
  ABSENT = 'absent',
  LATE = 'late',
  EARLY_DEPARTURE = 'early_departure',
  HALF_DAY = 'half_day',
  ON_LEAVE = 'on_leave',
  HOLIDAY = 'holiday',
  WEEKEND = 'weekend',
}

@Entity('attendance')
@Index(['tenantId', 'employeeId'])
@Index(['tenantId', 'date'])
@Index(['tenantId', 'status'])
export class Attendance {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ name: 'employee_id' })
  employeeId: string;

  @ManyToOne(() => Employee, { eager: true })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;

  @Column({ type: 'date' })
  date: Date;

  @Column({
    type: 'enum',
    enum: AttendanceStatus,
    default: AttendanceStatus.PRESENT,
  })
  status: AttendanceStatus;

  @Column({ type: 'time', nullable: true, name: 'check_in_time' })
  checkInTime: string;

  @Column({ type: 'time', nullable: true, name: 'check_out_time' })
  checkOutTime: string;

  @Column({ type: 'time', nullable: true, name: 'scheduled_in_time' })
  scheduledInTime: string;

  @Column({ type: 'time', nullable: true, name: 'scheduled_out_time' })
  scheduledOutTime: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true, name: 'hours_worked' })
  hoursWorked: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true, name: 'overtime_hours' })
  overtimeHours: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true, name: 'break_hours' })
  breakHours: number;

  @Column({ type: 'boolean', default: false, name: 'is_late' })
  isLate: boolean;

  @Column({ type: 'integer', nullable: true, name: 'late_minutes' })
  lateMinutes: number;

  @Column({ type: 'boolean', default: false, name: 'is_early_departure' })
  isEarlyDeparture: boolean;

  @Column({ type: 'integer', nullable: true, name: 'early_departure_minutes' })
  earlyDepartureMinutes: number;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'json', nullable: true, name: 'location_data' })
  locationData: {
    checkInLocation?: { latitude: number; longitude: number; address?: string };
    checkOutLocation?: { latitude: number; longitude: number; address?: string };
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;
}
