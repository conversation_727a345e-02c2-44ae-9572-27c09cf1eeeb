"""
Pydantic models for anomaly detection results.
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field


class PayrollAnomaly(BaseModel):
    """Individual payroll anomaly detection result."""
    employee_id: str = Field(..., description="Employee identifier")
    anomaly_type: str = Field(..., description="Type of anomaly detected")
    description: str = Field(..., description="Human-readable description of the anomaly")
    severity: str = Field(..., description="Severity level: low, medium, high, critical")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence in anomaly detection")
    detected_value: Union[int, float, str] = Field(..., description="The anomalous value detected")
    expected_range: Optional[Dict[str, Any]] = Field(None, description="Expected value range")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    detected_at: datetime = Field(default_factory=datetime.utcnow, description="Detection timestamp")


class ComplianceAlert(BaseModel):
    """Compliance violation alert."""
    alert_type: str = Field(..., description="Type of compliance alert")
    description: str = Field(..., description="Description of the compliance issue")
    severity: str = Field(..., description="Severity level: low, medium, high, critical")
    employee_id: Optional[str] = Field(None, description="Employee identifier if applicable")
    regulation: str = Field(..., description="Regulation or policy violated")
    recommended_action: str = Field(..., description="Recommended corrective action")
    deadline: Optional[datetime] = Field(None, description="Deadline for corrective action")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Alert creation timestamp")


class FraudRiskAssessment(BaseModel):
    """Fraud risk assessment result."""
    overall_risk_score: float = Field(..., ge=0.0, le=1.0, description="Overall fraud risk score")
    risk_level: str = Field(..., description="Risk level: low, medium, high, critical")
    risk_factors: List[str] = Field(..., description="Identified risk factors")
    fraud_indicators: List[str] = Field(..., description="Specific fraud indicators")
    recommendations: List[str] = Field(..., description="Risk mitigation recommendations")
    assessed_at: datetime = Field(default_factory=datetime.utcnow, description="Assessment timestamp")


class AnomalyDetectionResult(BaseModel):
    """Complete anomaly detection result."""
    anomalies: List[PayrollAnomaly] = Field(..., description="Detected anomalies")
    risk_assessment: FraudRiskAssessment = Field(..., description="Fraud risk assessment")
    compliance_alerts: List[ComplianceAlert] = Field(..., description="Compliance alerts")
    summary: Dict[str, Any] = Field(..., description="Summary statistics")
    analyzed_at: datetime = Field(default_factory=datetime.utcnow, description="Analysis timestamp")
    data_period: Dict[str, Optional[datetime]] = Field(..., description="Data period analyzed")


class AnomalyReport(BaseModel):
    """Comprehensive anomaly detection report."""
    report_id: str = Field(..., description="Report identifier")
    organization_id: Optional[str] = Field(None, description="Organization identifier")
    report_type: str = Field(..., description="Type of report: daily, weekly, monthly")
    
    # Summary metrics
    total_records_analyzed: int = Field(..., description="Total records analyzed")
    total_anomalies: int = Field(..., description="Total anomalies detected")
    anomaly_rate: float = Field(..., description="Anomaly detection rate")
    
    # Anomaly breakdown
    anomalies_by_type: Dict[str, int] = Field(..., description="Anomalies grouped by type")
    anomalies_by_severity: Dict[str, int] = Field(..., description="Anomalies grouped by severity")
    
    # Risk assessment
    overall_risk_score: float = Field(..., description="Overall organizational risk score")
    risk_trend: str = Field(..., description="Risk trend: increasing, stable, decreasing")
    
    # Top issues
    top_anomaly_types: List[Dict[str, Any]] = Field(..., description="Most common anomaly types")
    high_risk_employees: List[str] = Field(..., description="Employees with high risk scores")
    
    # Compliance status
    compliance_violations: int = Field(..., description="Number of compliance violations")
    critical_alerts: int = Field(..., description="Number of critical alerts")
    
    # Recommendations
    priority_actions: List[str] = Field(..., description="Priority actions to take")
    process_improvements: List[str] = Field(..., description="Process improvement suggestions")
    
    # Metadata
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Report generation timestamp")
    data_period: Dict[str, datetime] = Field(..., description="Data period covered")
    next_report_date: Optional[datetime] = Field(None, description="Next scheduled report date")


class AnomalyTrend(BaseModel):
    """Anomaly trend analysis."""
    trend_period: str = Field(..., description="Period analyzed: daily, weekly, monthly")
    data_points: List[Dict[str, Any]] = Field(..., description="Trend data points")
    trend_direction: str = Field(..., description="Trend direction: up, down, stable")
    trend_strength: float = Field(..., description="Strength of the trend")
    seasonal_patterns: Optional[Dict[str, Any]] = Field(None, description="Seasonal patterns detected")
    forecasted_values: Optional[List[Dict[str, Any]]] = Field(None, description="Forecasted future values")
    analyzed_at: datetime = Field(default_factory=datetime.utcnow, description="Analysis timestamp")


class AnomalyThreshold(BaseModel):
    """Anomaly detection threshold configuration."""
    threshold_id: str = Field(..., description="Threshold identifier")
    anomaly_type: str = Field(..., description="Type of anomaly")
    metric_name: str = Field(..., description="Metric being monitored")
    threshold_value: float = Field(..., description="Threshold value")
    operator: str = Field(..., description="Comparison operator: gt, lt, eq, ne")
    severity: str = Field(..., description="Severity when threshold is breached")
    enabled: bool = Field(default=True, description="Whether threshold is active")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")


class AnomalyDetectionRequest(BaseModel):
    """Request for anomaly detection analysis."""
    data: List[Dict[str, Any]] = Field(..., min_items=1, description="Data to analyze")
    detection_types: List[str] = Field(default_factory=list, description="Specific detection types to run")
    sensitivity: str = Field(default="medium", description="Detection sensitivity: low, medium, high")
    include_fraud_assessment: bool = Field(default=True, description="Include fraud risk assessment")
    include_compliance_check: bool = Field(default=True, description="Include compliance checking")
    custom_thresholds: Optional[Dict[str, float]] = Field(None, description="Custom detection thresholds")


class BatchAnomalyDetectionRequest(BaseModel):
    """Request for batch anomaly detection."""
    datasets: List[Dict[str, Any]] = Field(..., min_items=1, description="Multiple datasets to analyze")
    batch_id: Optional[str] = Field(None, description="Batch identifier")
    detection_config: Dict[str, Any] = Field(default_factory=dict, description="Detection configuration")
    parallel_processing: bool = Field(default=True, description="Process datasets in parallel")


class AnomalyInvestigation(BaseModel):
    """Anomaly investigation record."""
    investigation_id: str = Field(..., description="Investigation identifier")
    anomaly_id: str = Field(..., description="Related anomaly identifier")
    investigator_id: str = Field(..., description="Investigator identifier")
    status: str = Field(..., description="Investigation status: open, in_progress, closed")
    priority: str = Field(..., description="Investigation priority: low, medium, high, urgent")
    
    # Investigation details
    findings: List[str] = Field(default_factory=list, description="Investigation findings")
    evidence: List[Dict[str, Any]] = Field(default_factory=list, description="Evidence collected")
    root_cause: Optional[str] = Field(None, description="Identified root cause")
    
    # Actions taken
    corrective_actions: List[str] = Field(default_factory=list, description="Corrective actions taken")
    preventive_measures: List[str] = Field(default_factory=list, description="Preventive measures implemented")
    
    # Timeline
    opened_at: datetime = Field(default_factory=datetime.utcnow, description="Investigation opened timestamp")
    closed_at: Optional[datetime] = Field(None, description="Investigation closed timestamp")
    due_date: Optional[datetime] = Field(None, description="Investigation due date")


class AnomalyNotification(BaseModel):
    """Anomaly detection notification."""
    notification_id: str = Field(..., description="Notification identifier")
    anomaly_id: str = Field(..., description="Related anomaly identifier")
    recipient_id: str = Field(..., description="Notification recipient")
    notification_type: str = Field(..., description="Type: email, sms, dashboard, webhook")
    
    # Content
    subject: str = Field(..., description="Notification subject")
    message: str = Field(..., description="Notification message")
    urgency: str = Field(..., description="Urgency level: low, medium, high, critical")
    
    # Delivery
    sent_at: Optional[datetime] = Field(None, description="Sent timestamp")
    delivered_at: Optional[datetime] = Field(None, description="Delivered timestamp")
    read_at: Optional[datetime] = Field(None, description="Read timestamp")
    status: str = Field(default="pending", description="Delivery status")


class AnomalyMetrics(BaseModel):
    """Anomaly detection performance metrics."""
    metric_period: str = Field(..., description="Metrics period: daily, weekly, monthly")
    
    # Detection metrics
    total_detections: int = Field(..., description="Total anomalies detected")
    true_positives: int = Field(..., description="Confirmed true anomalies")
    false_positives: int = Field(..., description="False positive detections")
    false_negatives: int = Field(..., description="Missed anomalies")
    
    # Performance metrics
    precision: float = Field(..., description="Detection precision")
    recall: float = Field(..., description="Detection recall")
    f1_score: float = Field(..., description="F1 score")
    accuracy: float = Field(..., description="Overall accuracy")
    
    # Response metrics
    average_response_time: float = Field(..., description="Average response time in hours")
    resolution_rate: float = Field(..., description="Percentage of resolved anomalies")
    
    # System metrics
    processing_time: float = Field(..., description="Average processing time per record")
    system_uptime: float = Field(..., description="System uptime percentage")
    
    calculated_at: datetime = Field(default_factory=datetime.utcnow, description="Calculation timestamp")


class AnomalyConfiguration(BaseModel):
    """Anomaly detection system configuration."""
    config_id: str = Field(..., description="Configuration identifier")
    organization_id: str = Field(..., description="Organization identifier")
    
    # Detection settings
    enabled_detectors: List[str] = Field(..., description="Enabled detection algorithms")
    sensitivity_level: str = Field(default="medium", description="Overall sensitivity level")
    
    # Thresholds
    global_thresholds: Dict[str, float] = Field(default_factory=dict, description="Global detection thresholds")
    custom_rules: List[Dict[str, Any]] = Field(default_factory=list, description="Custom detection rules")
    
    # Notification settings
    notification_rules: List[Dict[str, Any]] = Field(default_factory=list, description="Notification rules")
    escalation_rules: List[Dict[str, Any]] = Field(default_factory=list, description="Escalation rules")
    
    # Processing settings
    batch_size: int = Field(default=1000, description="Batch processing size")
    processing_frequency: str = Field(default="hourly", description="Processing frequency")
    retention_period: int = Field(default=365, description="Data retention period in days")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Configuration creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    version: str = Field(default="1.0", description="Configuration version")


class AnomalyDashboard(BaseModel):
    """Anomaly detection dashboard data."""
    dashboard_id: str = Field(..., description="Dashboard identifier")
    organization_id: str = Field(..., description="Organization identifier")
    
    # Current status
    active_anomalies: int = Field(..., description="Number of active anomalies")
    critical_alerts: int = Field(..., description="Number of critical alerts")
    pending_investigations: int = Field(..., description="Number of pending investigations")
    
    # Recent activity
    recent_anomalies: List[PayrollAnomaly] = Field(..., description="Recent anomaly detections")
    recent_alerts: List[ComplianceAlert] = Field(..., description="Recent compliance alerts")
    
    # Trends
    anomaly_trends: List[Dict[str, Any]] = Field(..., description="Anomaly trend data")
    risk_score_trend: List[Dict[str, Any]] = Field(..., description="Risk score trend")
    
    # Charts data
    anomaly_distribution: Dict[str, int] = Field(..., description="Anomaly distribution by type")
    severity_breakdown: Dict[str, int] = Field(..., description="Anomaly breakdown by severity")
    department_risks: Dict[str, float] = Field(..., description="Risk scores by department")
    
    # Performance indicators
    detection_accuracy: float = Field(..., description="Current detection accuracy")
    response_time: float = Field(..., description="Average response time")
    resolution_rate: float = Field(..., description="Current resolution rate")
    
    last_updated: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    refresh_interval: int = Field(default=300, description="Refresh interval in seconds")
