import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { User } from '@app/database/entities/user.entity';
import { Permission } from '@app/database/entities/permission.entity';
import { UserPermission } from '@app/database/entities/user-permission.entity';
import { UserRole, ROLE_HIERARCHY } from '@app/common/enums/user-role.enum';

@Injectable()
export class PermissionService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Permission)
    private permissionRepository: Repository<Permission>,
    @InjectRepository(UserPermission)
    private userPermissionRepository: Repository<UserPermission>,
  ) {}

  /**
   * Check if user has specific permission
   */
  async hasPermission(
    userId: string,
    permissionName: string,
  ): Promise<boolean> {
    // Get user with role
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['role'],
    });

    if (!user) {
      return false;
    }

    // Check role-based permissions first
    const hasRolePermission = await this.hasRolePermission(
      user.role,
      permissionName,
    );

    if (hasRolePermission) {
      return true;
    }

    // Check custom user permissions
    const hasCustomPermission = await this.hasCustomPermission(
      userId,
      permissionName,
    );

    return hasCustomPermission;
  }

  /**
   * Check if user has any of the required roles
   */
  async hasRequiredRole(
    userId: string,
    requiredRoles: UserRole[],
  ): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['role'],
    });

    if (!user) {
      return false;
    }

    // Check direct role match
    if (requiredRoles.includes(user.role)) {
      return true;
    }

    // Check role hierarchy
    const userRoleHierarchy = ROLE_HIERARCHY[user.role] || [];
    return requiredRoles.some(role => userRoleHierarchy.includes(role));
  }

  /**
   * Get all permissions for a user
   */
  async getUserPermissions(userId: string): Promise<string[]> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['role'],
    });

    if (!user) {
      return [];
    }

    // Get role-based permissions
    const rolePermissions = await this.getRolePermissions(user.role);

    // Get custom user permissions
    const customPermissions = await this.getCustomUserPermissions(userId);

    // Combine and deduplicate
    const allPermissions = [...rolePermissions, ...customPermissions];
    return [...new Set(allPermissions)];
  }

  /**
   * Grant permission to user
   */
  async grantPermission(
    userId: string,
    permissionName: string,
    grantedBy: string,
  ): Promise<void> {
    // Check if permission exists
    const permission = await this.permissionRepository.findOne({
      where: { name: permissionName },
    });

    if (!permission) {
      throw new Error(`Permission '${permissionName}' does not exist`);
    }

    // Check if user already has this permission
    const existingPermission = await this.userPermissionRepository.findOne({
      where: {
        userId,
        permissionId: permission.id,
      },
    });

    if (existingPermission) {
      return; // Already has permission
    }

    // Grant permission
    const userPermission = this.userPermissionRepository.create({
      userId,
      permissionId: permission.id,
      grantedBy,
      grantedAt: new Date(),
    });

    await this.userPermissionRepository.save(userPermission);
  }

  /**
   * Revoke permission from user
   */
  async revokePermission(
    userId: string,
    permissionName: string,
    revokedBy: string,
  ): Promise<void> {
    const permission = await this.permissionRepository.findOne({
      where: { name: permissionName },
    });

    if (!permission) {
      return; // Permission doesn't exist
    }

    await this.userPermissionRepository.update(
      {
        userId,
        permissionId: permission.id,
      },
      {
        revokedBy,
        revokedAt: new Date(),
      },
    );
  }

  /**
   * Get all available permissions
   */
  async getAllPermissions(): Promise<Permission[]> {
    return this.permissionRepository.find({
      order: { category: 'ASC', name: 'ASC' },
    });
  }

  /**
   * Create a new permission
   */
  async createPermission(
    name: string,
    description: string,
    category: string,
  ): Promise<Permission> {
    const permission = this.permissionRepository.create({
      name,
      description,
      category,
    });

    return this.permissionRepository.save(permission);
  }

  /**
   * Check role-based permission
   */
  private async hasRolePermission(
    role: UserRole,
    permissionName: string,
  ): Promise<boolean> {
    // Define role-based permissions
    const rolePermissions: Record<UserRole, string[]> = {
      [UserRole.EMPLOYEE]: [
        'profile.read',
        'profile.update',
        'timesheet.create',
        'timesheet.read',
        'leave.request',
        'payslip.read',
      ],
      [UserRole.MANAGER]: [
        'team.read',
        'team.manage',
        'leave.approve',
        'timesheet.approve',
        'performance.read',
        'performance.create',
        'reports.team',
      ],
      [UserRole.HR]: [
        'employees.read',
        'employees.create',
        'employees.update',
        'leave.manage',
        'performance.manage',
        'reports.hr',
        'recruitment.manage',
      ],
      [UserRole.FINANCE]: [
        'payroll.read',
        'payroll.process',
        'expenses.approve',
        'reports.financial',
        'budgets.manage',
      ],
      [UserRole.ADMIN]: [
        'system.configure',
        'users.manage',
        'roles.manage',
        'permissions.manage',
        'audit.read',
        'reports.all',
      ],
      [UserRole.SUPER_ADMIN]: [
        'system.full_access',
        'tenants.manage',
        'security.manage',
        'data.export',
        'data.import',
      ],
    };

    const permissions = rolePermissions[role] || [];
    
    // Check direct permission
    if (permissions.includes(permissionName)) {
      return true;
    }

    // Check inherited permissions from lower roles
    const hierarchy = ROLE_HIERARCHY[role] || [];
    for (const inheritedRole of hierarchy) {
      const inheritedPermissions = rolePermissions[inheritedRole] || [];
      if (inheritedPermissions.includes(permissionName)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check custom user permission
   */
  private async hasCustomPermission(
    userId: string,
    permissionName: string,
  ): Promise<boolean> {
    const permission = await this.permissionRepository.findOne({
      where: { name: permissionName },
    });

    if (!permission) {
      return false;
    }

    const userPermission = await this.userPermissionRepository.findOne({
      where: {
        userId,
        permissionId: permission.id,
        revokedAt: null, // Not revoked
      },
    });

    return !!userPermission;
  }

  /**
   * Get role-based permissions
   */
  private async getRolePermissions(role: UserRole): Promise<string[]> {
    const permissions: string[] = [];
    
    // Add direct role permissions
    const directPermissions = await this.getDirectRolePermissions(role);
    permissions.push(...directPermissions);

    // Add inherited permissions
    const hierarchy = ROLE_HIERARCHY[role] || [];
    for (const inheritedRole of hierarchy) {
      const inheritedPermissions = await this.getDirectRolePermissions(inheritedRole);
      permissions.push(...inheritedPermissions);
    }

    return [...new Set(permissions)];
  }

  /**
   * Get direct role permissions (without inheritance)
   */
  private async getDirectRolePermissions(role: UserRole): Promise<string[]> {
    // This would typically come from a database table
    // For now, we'll use the same logic as hasRolePermission
    const rolePermissions: Record<UserRole, string[]> = {
      [UserRole.EMPLOYEE]: [
        'profile.read',
        'profile.update',
        'timesheet.create',
        'timesheet.read',
        'leave.request',
        'payslip.read',
      ],
      [UserRole.MANAGER]: [
        'team.read',
        'team.manage',
        'leave.approve',
        'timesheet.approve',
        'performance.read',
        'performance.create',
        'reports.team',
      ],
      [UserRole.HR]: [
        'employees.read',
        'employees.create',
        'employees.update',
        'leave.manage',
        'performance.manage',
        'reports.hr',
        'recruitment.manage',
      ],
      [UserRole.FINANCE]: [
        'payroll.read',
        'payroll.process',
        'expenses.approve',
        'reports.financial',
        'budgets.manage',
      ],
      [UserRole.ADMIN]: [
        'system.configure',
        'users.manage',
        'roles.manage',
        'permissions.manage',
        'audit.read',
        'reports.all',
      ],
      [UserRole.SUPER_ADMIN]: [
        'system.full_access',
        'tenants.manage',
        'security.manage',
        'data.export',
        'data.import',
      ],
    };

    return rolePermissions[role] || [];
  }

  /**
   * Get custom user permissions
   */
  private async getCustomUserPermissions(userId: string): Promise<string[]> {
    const userPermissions = await this.userPermissionRepository.find({
      where: {
        userId,
        revokedAt: null,
      },
      relations: ['permission'],
    });

    return userPermissions.map(up => up.permission.name);
  }
}
