import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  Api<PERSON>earerA<PERSON>,
  <PERSON>pi<PERSON><PERSON>y,
  <PERSON>pi<PERSON>aram,
} from '@nestjs/swagger';

import { JwtAuthGuard } from '@app/security/guards/jwt-auth.guard';
import { RolesGuard } from '@app/security/guards/roles.guard';
import { Roles } from '@app/security/decorators/roles.decorator';
import { UserRole } from '@app/common/enums/user-role.enum';
import { RequestWithUser } from '@app/security/interfaces/auth.interface';

import { GDPRComplianceService } from '@app/security/services/gdpr-compliance.service';
import { SOC2ComplianceService } from '@app/security/services/soc2-compliance.service';
import { SecurityMonitoringService } from '@app/security/services/security-monitoring.service';
import { EncryptionService } from '@app/security/services/encryption.service';

@ApiTags('Security & Compliance')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('security')
export class SecurityComplianceController {
  constructor(
    private readonly gdprService: GDPRComplianceService,
    private readonly soc2Service: SOC2ComplianceService,
    private readonly securityMonitoringService: SecurityMonitoringService,
    private readonly encryptionService: EncryptionService,
  ) {}

  // GDPR Compliance Endpoints
  @Post('gdpr/access-request')
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Handle GDPR data access request',
    description: 'Process a data subject access request under GDPR Article 15',
  })
  @ApiResponse({ status: 200, description: 'Access request processed successfully' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async handleAccessRequest(
    @Body() body: { subjectId: string },
    @Request() req: RequestWithUser,
  ) {
    try {
      const result = await this.gdprService.handleAccessRequest(
        body.subjectId,
        req.user.id,
        req.user.tenantId,
      );
      return {
        status: 'success',
        data: result,
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Post('gdpr/rectification-request')
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Handle GDPR data rectification request',
    description: 'Process a data rectification request under GDPR Article 16',
  })
  async handleRectificationRequest(
    @Body() body: { subjectId: string; corrections: Record<string, any> },
    @Request() req: RequestWithUser,
  ) {
    try {
      await this.gdprService.handleRectificationRequest(
        body.subjectId,
        body.corrections,
        req.user.id,
        req.user.tenantId,
      );
      return {
        status: 'success',
        message: 'Rectification request processed successfully',
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Post('gdpr/erasure-request')
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Handle GDPR data erasure request',
    description: 'Process a data erasure request under GDPR Article 17 (Right to be forgotten)',
  })
  async handleErasureRequest(
    @Body() body: { subjectId: string; reason: string },
    @Request() req: RequestWithUser,
  ) {
    try {
      await this.gdprService.handleErasureRequest(
        body.subjectId,
        req.user.id,
        req.user.tenantId,
        body.reason,
      );
      return {
        status: 'success',
        message: 'Erasure request processed successfully',
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Post('gdpr/portability-request')
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Handle GDPR data portability request',
    description: 'Process a data portability request under GDPR Article 20',
  })
  @ApiQuery({ name: 'format', enum: ['json', 'csv', 'xml'], required: false })
  async handlePortabilityRequest(
    @Body() body: { subjectId: string },
    @Query('format') format: 'json' | 'csv' | 'xml' = 'json',
    @Request() req: RequestWithUser,
  ) {
    try {
      const result = await this.gdprService.handlePortabilityRequest(
        body.subjectId,
        req.user.id,
        req.user.tenantId,
        format,
      );
      return {
        status: 'success',
        data: result,
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Post('gdpr/consent')
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Record GDPR consent',
    description: 'Record user consent for data processing',
  })
  async recordConsent(
    @Body() consentData: {
      subjectId: string;
      purpose: string;
      legalBasis: string;
      dataCategories: string[];
      retentionPeriod: number;
    },
    @Request() req: RequestWithUser,
  ) {
    try {
      const consentId = await this.gdprService.recordConsent({
        ...consentData,
        tenantId: req.user.tenantId,
        consentGiven: true,
        consentDate: new Date(),
      });
      return {
        status: 'success',
        consentId,
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('gdpr/compliance-report')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Generate GDPR compliance report',
    description: 'Generate comprehensive GDPR compliance report for the tenant',
  })
  async getGDPRComplianceReport(@Request() req: RequestWithUser) {
    try {
      const report = await this.gdprService.generateComplianceReport(req.user.tenantId);
      return {
        status: 'success',
        data: report,
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // SOC 2 Compliance Endpoints
  @Get('soc2/controls')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Get SOC 2 controls',
    description: 'Retrieve all SOC 2 controls and their status',
  })
  async getSOC2Controls() {
    try {
      const controls = this.soc2Service.getAllControls();
      return {
        status: 'success',
        data: controls,
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('soc2/controls/:controlId/test')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Test SOC 2 control',
    description: 'Test a specific SOC 2 control and record results',
  })
  @ApiParam({ name: 'controlId', description: 'SOC 2 control ID' })
  async testSOC2Control(
    @Param('controlId') controlId: string,
    @Body() testResults: {
      status: 'compliant' | 'non_compliant';
      evidence: string[];
      exceptions?: string[];
      notes?: string;
    },
  ) {
    try {
      await this.soc2Service.testControl(controlId, testResults);
      return {
        status: 'success',
        message: 'Control test recorded successfully',
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Post('soc2/incidents')
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Report security incident',
    description: 'Report a security incident for SOC 2 compliance tracking',
  })
  async reportSecurityIncident(
    @Body() incident: {
      title: string;
      description: string;
      severity: 'low' | 'medium' | 'high' | 'critical';
      category: string;
      affectedSystems: string[];
      affectedUsers: number;
      containmentActions: string[];
    },
    @Request() req: RequestWithUser,
  ) {
    try {
      const incidentId = await this.soc2Service.reportIncident({
        ...incident,
        reportedBy: req.user.id,
      });
      return {
        status: 'success',
        incidentId,
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('soc2/compliance-report')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Generate SOC 2 compliance report',
    description: 'Generate comprehensive SOC 2 compliance report',
  })
  async getSOC2ComplianceReport() {
    try {
      const report = await this.soc2Service.generateComplianceReport();
      return {
        status: 'success',
        data: report,
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('soc2/metrics')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Get SOC 2 compliance metrics',
    description: 'Get current SOC 2 compliance metrics and status',
  })
  async getSOC2Metrics() {
    try {
      const metrics = this.soc2Service.getComplianceMetrics();
      return {
        status: 'success',
        data: metrics,
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // Security Monitoring Endpoints
  @Get('monitoring/alerts')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Get security alerts',
    description: 'Retrieve all security alerts',
  })
  async getSecurityAlerts() {
    try {
      const alerts = this.securityMonitoringService.getAllAlerts();
      return {
        status: 'success',
        data: alerts,
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Put('monitoring/alerts/:alertId')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Update security alert',
    description: 'Update the status or details of a security alert',
  })
  @ApiParam({ name: 'alertId', description: 'Security alert ID' })
  async updateSecurityAlert(
    @Param('alertId') alertId: string,
    @Body() updates: {
      status?: 'open' | 'investigating' | 'resolved' | 'false_positive';
      assignedTo?: string;
      resolution?: string;
    },
    @Request() req: RequestWithUser,
  ) {
    try {
      await this.securityMonitoringService.updateAlert(alertId, {
        ...updates,
        assignedTo: updates.assignedTo || req.user.id,
      });
      return {
        status: 'success',
        message: 'Alert updated successfully',
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('monitoring/metrics')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Get security metrics',
    description: 'Get current security monitoring metrics',
  })
  async getSecurityMetrics() {
    try {
      const metrics = this.securityMonitoringService.getSecurityMetrics();
      return {
        status: 'success',
        data: metrics,
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('monitoring/threats')
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Get threat indicators',
    description: 'Retrieve all threat indicators',
  })
  async getThreatIndicators() {
    try {
      const threats = this.securityMonitoringService.getAllThreatIndicators();
      return {
        status: 'success',
        data: threats,
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  // Encryption Management Endpoints
  @Post('encryption/rotate-key/:tenantId')
  @Roles(UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Rotate encryption key',
    description: 'Rotate encryption key for a specific tenant (Super Admin only)',
  })
  @ApiParam({ name: 'tenantId', description: 'Tenant ID' })
  async rotateEncryptionKey(@Param('tenantId') tenantId: string) {
    try {
      await this.encryptionService.rotateTenantKey(tenantId);
      return {
        status: 'success',
        message: 'Encryption key rotated successfully',
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('encryption/validate-config')
  @Roles(UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Validate encryption configuration',
    description: 'Validate the current encryption configuration (Super Admin only)',
  })
  async validateEncryptionConfig() {
    try {
      const isValid = this.encryptionService.validateConfiguration();
      return {
        status: 'success',
        data: { isValid },
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Delete('encryption/clear-cache')
  @Roles(UserRole.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Clear encryption key cache',
    description: 'Clear the encryption key cache for security (Super Admin only)',
  })
  async clearEncryptionCache() {
    try {
      this.encryptionService.clearKeyCache();
      return {
        status: 'success',
        message: 'Encryption key cache cleared successfully',
      };
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
