import {
  Entity,
  Column,
  Index,
  OneToMany,
  ManyToOne,
  JoinColumn,
  Tree,
  TreeParent,
  TreeChildren,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { Employee } from './employee.entity';
import { Position } from './position.entity';

@Entity('departments')
@Tree('closure-table')
@Index(['code', 'tenantId'], { unique: true })
@Index(['name', 'tenantId'])
export class Department extends TenantAwareEntity {
  @Column({
    type: 'varchar',
    length: 50,
    comment: 'Department code',
  })
  code: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Department name',
  })
  name: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Department description',
  })
  description?: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Department head employee ID',
  })
  headId?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Department location',
  })
  location?: string;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: 'Department phone number',
  })
  phoneNumber?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Department email',
  })
  email?: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Department budget',
  })
  budget?: number;

  @Column({
    type: 'varchar',
    length: 10,
    default: 'USD',
    comment: 'Budget currency',
  })
  budgetCurrency: string;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether the department is active',
  })
  @Index()
  isActive: boolean;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Display order',
  })
  sortOrder: number;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Department metadata',
  })
  metadata?: Record<string, any>;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Department icon',
  })
  icon?: string;

  @Column({
    type: 'varchar',
    length: 7,
    nullable: true,
    comment: 'Department color',
  })
  color?: string;

  // Tree relationships
  @TreeParent()
  parent?: Department;

  @TreeChildren()
  children: Department[];

  // Other relationships
  @ManyToOne(() => Employee, { nullable: true })
  @JoinColumn({ name: 'head_id' })
  head?: Employee;

  @OneToMany(() => Employee, employee => employee.department)
  employees: Employee[];

  @OneToMany(() => Position, position => position.department)
  positions: Position[];

  // Virtual properties
  get employeeCount(): number {
    return this.employees ? this.employees.length : 0;
  }

  get isRoot(): boolean {
    return !this.parent;
  }

  get hasChildren(): boolean {
    return this.children && this.children.length > 0;
  }

  get fullPath(): string {
    const path = [];
    let current: Department | undefined = this;
    while (current) {
      path.unshift(current.name);
      current = current.parent;
    }
    return path.join(' > ');
  }
}
