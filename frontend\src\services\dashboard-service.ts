import { apiClient } from './api-client';

export interface DashboardMetrics {
  employees: {
    total: number;
    active: number;
    newHires: number;
    turnover: number;
  };
  performance: {
    averageRating: number;
    completedReviews: number;
    pendingReviews: number;
    goalAchievementRate: number;
  };
  payroll: {
    totalPayroll: number;
    averageSalary: number;
    pendingPayments: number;
    payrollCosts: number;
  };
  attendance: {
    presentToday: number;
    onLeave: number;
    lateArrivals: number;
    averageHours: number;
  };
}

export interface RecentActivity {
  id: string;
  type: 'employee' | 'performance' | 'payroll' | 'leave';
  title: string;
  description: string;
  timestamp: string;
  status: 'success' | 'warning' | 'error' | 'info';
}

export interface ChartData {
  employeeGrowth: Array<{
    month: string;
    employees: number;
    newHires: number;
    departures: number;
  }>;
  departmentDistribution: Array<{
    department: string;
    count: number;
    percentage: number;
  }>;
  performanceTrends: Array<{
    month: string;
    averageRating: number;
    completedReviews: number;
  }>;
  attendanceTrends: Array<{
    date: string;
    presentCount: number;
    absentCount: number;
    lateCount: number;
  }>;
}

class DashboardService {
  /**
   * Get dashboard metrics for the specified time range
   */
  async getMetrics(timeRange: string = '30d'): Promise<DashboardMetrics> {
    const response = await apiClient.get('/dashboard/metrics', {
      params: { timeRange },
    });
    return response.data;
  }

  /**
   * Get recent activities across the system
   */
  async getRecentActivities(limit: number = 10): Promise<RecentActivity[]> {
    const response = await apiClient.get('/dashboard/activities', {
      params: { limit },
    });
    return response.data;
  }

  /**
   * Get chart data for dashboard visualizations
   */
  async getChartData(timeRange: string = '30d'): Promise<ChartData> {
    const response = await apiClient.get('/dashboard/charts', {
      params: { timeRange },
    });
    return response.data;
  }

  /**
   * Get real-time system status
   */
  async getSystemStatus(): Promise<{
    status: 'healthy' | 'warning' | 'error';
    uptime: number;
    activeUsers: number;
    systemLoad: number;
    lastUpdated: string;
  }> {
    const response = await apiClient.get('/dashboard/system-status');
    return response.data;
  }

  /**
   * Get user-specific dashboard data
   */
  async getUserDashboard(): Promise<{
    pendingTasks: number;
    upcomingReviews: number;
    goalProgress: number;
    teamSize?: number;
    directReports?: Array<{
      id: string;
      name: string;
      status: string;
    }>;
  }> {
    const response = await apiClient.get('/dashboard/user');
    return response.data;
  }

  /**
   * Get department-specific metrics
   */
  async getDepartmentMetrics(departmentId: string, timeRange: string = '30d'): Promise<{
    employeeCount: number;
    averagePerformance: number;
    attendanceRate: number;
    turnoverRate: number;
    budgetUtilization: number;
    topPerformers: Array<{
      id: string;
      name: string;
      rating: number;
    }>;
  }> {
    const response = await apiClient.get(`/dashboard/departments/${departmentId}/metrics`, {
      params: { timeRange },
    });
    return response.data;
  }

  /**
   * Export dashboard data
   */
  async exportData(
    type: 'metrics' | 'activities' | 'charts',
    format: 'csv' | 'xlsx' | 'pdf' = 'xlsx',
    timeRange: string = '30d'
  ): Promise<Blob> {
    const response = await apiClient.get(`/dashboard/export/${type}`, {
      params: { format, timeRange },
      responseType: 'blob',
    });
    return response.data;
  }

  /**
   * Get performance insights using AI
   */
  async getPerformanceInsights(timeRange: string = '30d'): Promise<{
    insights: Array<{
      type: 'trend' | 'anomaly' | 'recommendation';
      title: string;
      description: string;
      impact: 'high' | 'medium' | 'low';
      actionable: boolean;
    }>;
    predictions: Array<{
      metric: string;
      currentValue: number;
      predictedValue: number;
      confidence: number;
      timeframe: string;
    }>;
  }> {
    const response = await apiClient.get('/dashboard/ai-insights', {
      params: { timeRange },
    });
    return response.data;
  }

  /**
   * Get alerts and notifications
   */
  async getAlerts(): Promise<Array<{
    id: string;
    type: 'info' | 'warning' | 'error' | 'success';
    title: string;
    message: string;
    timestamp: string;
    read: boolean;
    actionUrl?: string;
  }>> {
    const response = await apiClient.get('/dashboard/alerts');
    return response.data;
  }

  /**
   * Mark alert as read
   */
  async markAlertAsRead(alertId: string): Promise<void> {
    await apiClient.patch(`/dashboard/alerts/${alertId}/read`);
  }

  /**
   * Get quick actions for the current user
   */
  async getQuickActions(): Promise<Array<{
    id: string;
    title: string;
    description: string;
    icon: string;
    url: string;
    category: 'employee' | 'performance' | 'payroll' | 'reports';
    permissions: string[];
  }>> {
    const response = await apiClient.get('/dashboard/quick-actions');
    return response.data;
  }

  /**
   * Get upcoming deadlines and reminders
   */
  async getUpcomingDeadlines(): Promise<Array<{
    id: string;
    title: string;
    description: string;
    dueDate: string;
    type: 'review' | 'goal' | 'payroll' | 'compliance';
    priority: 'high' | 'medium' | 'low';
    assignee?: {
      id: string;
      name: string;
    };
  }>> {
    const response = await apiClient.get('/dashboard/deadlines');
    return response.data;
  }

  /**
   * Get weather widget data (for office locations)
   */
  async getWeatherData(): Promise<Array<{
    location: string;
    temperature: number;
    condition: string;
    icon: string;
    humidity: number;
    windSpeed: number;
  }>> {
    const response = await apiClient.get('/dashboard/weather');
    return response.data;
  }

  /**
   * Get company announcements
   */
  async getAnnouncements(): Promise<Array<{
    id: string;
    title: string;
    content: string;
    author: {
      name: string;
      role: string;
    };
    publishedAt: string;
    priority: 'high' | 'medium' | 'low';
    category: 'general' | 'hr' | 'it' | 'finance';
    readBy: string[];
  }>> {
    const response = await apiClient.get('/dashboard/announcements');
    return response.data;
  }

  /**
   * Mark announcement as read
   */
  async markAnnouncementAsRead(announcementId: string): Promise<void> {
    await apiClient.patch(`/dashboard/announcements/${announcementId}/read`);
  }
}

export const dashboardService = new DashboardService();
