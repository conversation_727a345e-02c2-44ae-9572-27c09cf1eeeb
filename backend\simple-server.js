const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.APP_PORT || 4000;

// Middleware
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Basic routes
app.get('/', (req, res) => {
  res.json({
    message: 'Hello from PeopleNest HRMS API Gateway!',
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'PeopleNest HRMS API Gateway',
    version: '1.0.0'
  });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'PeopleNest HRMS API Gateway',
    version: '1.0.0',
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API routes
app.get('/api/v1/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      database: 'connected',
      redis: 'connected',
      mongodb: 'connected'
    },
    version: '1.0.0'
  });
});

// Auth routes
app.post('/api/v1/auth/login', (req, res) => {
  const { email, password } = req.body;
  
  // Simple mock authentication
  if (email === '<EMAIL>' && password === 'Password1234') {
    res.json({
      success: true,
      message: 'Login successful',
      user: {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        role: 'SUPER_ADMIN'
      },
      token: 'mock-jwt-token-' + Date.now()
    });
  } else if (email === 'awadhesh' && password === 'awadhesh123') {
    res.json({
      success: true,
      message: 'Login successful',
      user: {
        id: '2',
        email: '<EMAIL>',
        firstName: 'Awadhesh',
        lastName: 'Admin',
        role: 'SUPER_ADMIN'
      },
      token: 'mock-jwt-token-' + Date.now()
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

// Employee routes
app.get('/api/v1/employees', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        employeeId: 'EMP001',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        department: 'Engineering',
        position: 'Software Engineer',
        status: 'ACTIVE'
      },
      {
        id: '2',
        employeeId: 'EMP002',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        department: 'HR',
        position: 'HR Manager',
        status: 'ACTIVE'
      }
    ],
    total: 2,
    page: 1,
    limit: 10
  });
});

app.post('/api/v1/employees', (req, res) => {
  const employee = req.body;
  res.json({
    success: true,
    message: 'Employee created successfully',
    data: {
      id: Date.now().toString(),
      employeeId: 'EMP' + Date.now(),
      ...employee,
      status: 'ACTIVE',
      createdAt: new Date().toISOString()
    }
  });
});

// Department routes
app.get('/api/v1/departments', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        name: 'Engineering',
        description: 'Software development and engineering',
        employeeCount: 25
      },
      {
        id: '2',
        name: 'Human Resources',
        description: 'HR and people operations',
        employeeCount: 5
      },
      {
        id: '3',
        name: 'Finance',
        description: 'Financial operations and accounting',
        employeeCount: 8
      }
    ]
  });
});

// Payroll routes
app.get('/api/v1/payroll', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        employeeId: 'EMP001',
        employeeName: 'John Doe',
        period: '2024-01',
        grossSalary: 5000,
        deductions: 500,
        netSalary: 4500,
        status: 'PROCESSED'
      }
    ]
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.originalUrl
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 PeopleNest HRMS API Gateway started successfully`);
  console.log(`📱 Application is running on: http://localhost:${PORT}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`⏰ Started at: ${new Date().toISOString()}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});
