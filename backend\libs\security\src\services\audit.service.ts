import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';

import { AuditLog } from '@app/database/entities/audit-log.entity';
import { AuditLogEntry } from '../interfaces/auth.interface';

@Injectable()
export class AuditService {
  constructor(
    @InjectRepository(AuditLog)
    private auditLogRepository: Repository<AuditLog>,
  ) {}

  /**
   * Log authentication attempt
   */
  async logAuthAttempt(data: {
    userId?: string;
    email?: string;
    ip?: string;
    userAgent?: string;
    success: boolean;
    error?: string;
    action?: string;
    timestamp: Date;
  }): Promise<void> {
    const auditLog = this.auditLogRepository.create({
      userId: data.userId,
      email: data.email,
      action: data.action || 'authentication',
      ip: data.ip,
      userAgent: data.userAgent,
      success: data.success,
      error: data.error,
      timestamp: data.timestamp,
      metadata: {
        type: 'auth_attempt',
      },
    });

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Log user action
   */
  async logUserAction(data: {
    userId: string;
    action: string;
    resource?: string;
    resourceId?: string;
    ip?: string;
    userAgent?: string;
    success: boolean;
    error?: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    const auditLog = this.auditLogRepository.create({
      userId: data.userId,
      action: data.action,
      resource: data.resource,
      resourceId: data.resourceId,
      ip: data.ip,
      userAgent: data.userAgent,
      success: data.success,
      error: data.error,
      timestamp: new Date(),
      metadata: data.metadata || {},
    });

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Log system event
   */
  async logSystemEvent(data: {
    action: string;
    resource?: string;
    success: boolean;
    error?: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    const auditLog = this.auditLogRepository.create({
      action: data.action,
      resource: data.resource,
      success: data.success,
      error: data.error,
      timestamp: new Date(),
      metadata: {
        ...data.metadata,
        type: 'system_event',
      },
    });

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Log data access
   */
  async logDataAccess(data: {
    userId: string;
    resource: string;
    resourceId?: string;
    action: 'read' | 'create' | 'update' | 'delete';
    ip?: string;
    userAgent?: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    const auditLog = this.auditLogRepository.create({
      userId: data.userId,
      action: `${data.resource}.${data.action}`,
      resource: data.resource,
      resourceId: data.resourceId,
      ip: data.ip,
      userAgent: data.userAgent,
      success: true,
      timestamp: new Date(),
      metadata: {
        ...data.metadata,
        type: 'data_access',
        operation: data.action,
      },
    });

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Log security event
   */
  async logSecurityEvent(data: {
    userId?: string;
    action: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    ip?: string;
    userAgent?: string;
    description: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    const auditLog = this.auditLogRepository.create({
      userId: data.userId,
      action: data.action,
      ip: data.ip,
      userAgent: data.userAgent,
      success: false, // Security events are typically failures or suspicious activities
      error: data.description,
      timestamp: new Date(),
      metadata: {
        ...data.metadata,
        type: 'security_event',
        severity: data.severity,
      },
    });

    await this.auditLogRepository.save(auditLog);
  }

  /**
   * Get audit logs for a user
   */
  async getUserAuditLogs(
    userId: string,
    limit: number = 100,
    offset: number = 0,
  ): Promise<AuditLogEntry[]> {
    const logs = await this.auditLogRepository.find({
      where: { userId },
      order: { timestamp: 'DESC' },
      take: limit,
      skip: offset,
    });

    return logs.map(this.mapToAuditLogEntry);
  }

  /**
   * Get audit logs by action
   */
  async getAuditLogsByAction(
    action: string,
    limit: number = 100,
    offset: number = 0,
  ): Promise<AuditLogEntry[]> {
    const logs = await this.auditLogRepository.find({
      where: { action },
      order: { timestamp: 'DESC' },
      take: limit,
      skip: offset,
    });

    return logs.map(this.mapToAuditLogEntry);
  }

  /**
   * Get audit logs by date range
   */
  async getAuditLogsByDateRange(
    startDate: Date,
    endDate: Date,
    limit: number = 100,
    offset: number = 0,
  ): Promise<AuditLogEntry[]> {
    const logs = await this.auditLogRepository.find({
      where: {
        timestamp: Between(startDate, endDate),
      },
      order: { timestamp: 'DESC' },
      take: limit,
      skip: offset,
    });

    return logs.map(this.mapToAuditLogEntry);
  }

  /**
   * Get failed authentication attempts
   */
  async getFailedAuthAttempts(
    timeWindow: number = 3600000, // 1 hour in milliseconds
    limit: number = 100,
  ): Promise<AuditLogEntry[]> {
    const since = new Date(Date.now() - timeWindow);
    
    const logs = await this.auditLogRepository.find({
      where: {
        action: 'authentication',
        success: false,
        timestamp: Between(since, new Date()),
      },
      order: { timestamp: 'DESC' },
      take: limit,
    });

    return logs.map(this.mapToAuditLogEntry);
  }

  /**
   * Get security events
   */
  async getSecurityEvents(
    severity?: 'low' | 'medium' | 'high' | 'critical',
    limit: number = 100,
    offset: number = 0,
  ): Promise<AuditLogEntry[]> {
    const queryBuilder = this.auditLogRepository
      .createQueryBuilder('audit')
      .where("audit.metadata->>'type' = :type", { type: 'security_event' });

    if (severity) {
      queryBuilder.andWhere("audit.metadata->>'severity' = :severity", { severity });
    }

    const logs = await queryBuilder
      .orderBy('audit.timestamp', 'DESC')
      .take(limit)
      .skip(offset)
      .getMany();

    return logs.map(this.mapToAuditLogEntry);
  }

  /**
   * Get audit statistics
   */
  async getAuditStats(
    startDate: Date,
    endDate: Date,
  ): Promise<{
    totalEvents: number;
    successfulEvents: number;
    failedEvents: number;
    authAttempts: number;
    failedAuthAttempts: number;
    securityEvents: number;
    uniqueUsers: number;
    uniqueIPs: number;
  }> {
    const [
      totalEvents,
      successfulEvents,
      failedEvents,
      authAttempts,
      failedAuthAttempts,
      securityEvents,
      uniqueUsers,
      uniqueIPs,
    ] = await Promise.all([
      this.auditLogRepository.count({
        where: { timestamp: Between(startDate, endDate) },
      }),
      this.auditLogRepository.count({
        where: { 
          timestamp: Between(startDate, endDate),
          success: true,
        },
      }),
      this.auditLogRepository.count({
        where: { 
          timestamp: Between(startDate, endDate),
          success: false,
        },
      }),
      this.auditLogRepository.count({
        where: { 
          timestamp: Between(startDate, endDate),
          action: 'authentication',
        },
      }),
      this.auditLogRepository.count({
        where: { 
          timestamp: Between(startDate, endDate),
          action: 'authentication',
          success: false,
        },
      }),
      this.auditLogRepository
        .createQueryBuilder('audit')
        .where("audit.metadata->>'type' = :type", { type: 'security_event' })
        .andWhere('audit.timestamp BETWEEN :startDate AND :endDate', { startDate, endDate })
        .getCount(),
      this.auditLogRepository
        .createQueryBuilder('audit')
        .select('COUNT(DISTINCT audit.userId)', 'count')
        .where('audit.timestamp BETWEEN :startDate AND :endDate', { startDate, endDate })
        .andWhere('audit.userId IS NOT NULL')
        .getRawOne()
        .then(result => parseInt(result.count, 10)),
      this.auditLogRepository
        .createQueryBuilder('audit')
        .select('COUNT(DISTINCT audit.ip)', 'count')
        .where('audit.timestamp BETWEEN :startDate AND :endDate', { startDate, endDate })
        .andWhere('audit.ip IS NOT NULL')
        .getRawOne()
        .then(result => parseInt(result.count, 10)),
    ]);

    return {
      totalEvents,
      successfulEvents,
      failedEvents,
      authAttempts,
      failedAuthAttempts,
      securityEvents,
      uniqueUsers,
      uniqueIPs,
    };
  }

  /**
   * Clean up old audit logs
   */
  async cleanupOldLogs(retentionDays: number = 90): Promise<void> {
    const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);
    
    await this.auditLogRepository.delete({
      timestamp: Between(new Date(0), cutoffDate),
    });
  }

  /**
   * Map audit log entity to audit log entry
   */
  private mapToAuditLogEntry(log: AuditLog): AuditLogEntry {
    return {
      id: log.id,
      userId: log.userId,
      email: log.email,
      action: log.action,
      ip: log.ip,
      userAgent: log.userAgent,
      success: log.success,
      error: log.error,
      metadata: log.metadata,
      timestamp: log.timestamp,
    };
  }
}
