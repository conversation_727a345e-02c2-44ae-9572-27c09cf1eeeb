import {
  Entity,
  Column,
  Index,
  ManyToOne,
  OneToMany,
  JoinColumn,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { Employee } from './employee.entity';
import { PayrollPeriod } from './payroll-period.entity';
import { PayrollItem } from './payroll-item.entity';
import { PayrollStatus, Currency, PaymentMethod } from '@app/common/enums/status.enum';

@Entity('payslips')
@Index(['tenantId', 'employeeId', 'payrollPeriodId'], { unique: true })
@Index(['tenantId', 'status'])
@Index(['tenantId', 'payDate'])
@Index(['employeeId', 'payDate'])
export class Payslip extends TenantAwareEntity {
  @Column({
    type: 'varchar',
    length: 50,
    comment: 'Unique payslip number',
  })
  @Index()
  payslipNumber: string;

  @Column({
    type: 'uuid',
    comment: 'Employee ID',
  })
  employeeId: string;

  @Column({
    type: 'uuid',
    comment: 'Payroll period ID',
  })
  payrollPeriodId: string;

  @Column({
    type: 'enum',
    enum: PayrollStatus,
    default: PayrollStatus.DRAFT,
    comment: 'Payslip status',
  })
  @Index()
  status: PayrollStatus;

  @Column({
    type: 'date',
    comment: 'Pay date',
  })
  @Index()
  payDate: Date;

  @Column({
    type: 'date',
    comment: 'Period start date',
  })
  periodStartDate: Date;

  @Column({
    type: 'date',
    comment: 'Period end date',
  })
  periodEndDate: Date;

  @Column({
    type: 'enum',
    enum: Currency,
    default: Currency.USD,
    comment: 'Payslip currency',
  })
  currency: Currency;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 4,
    default: 1.0,
    comment: 'Exchange rate to base currency',
  })
  exchangeRate: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Total gross pay',
  })
  grossPay: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Total net pay',
  })
  netPay: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Total taxes',
  })
  totalTaxes: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Total deductions',
  })
  totalDeductions: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Total benefits',
  })
  totalBenefits: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Total reimbursements',
  })
  totalReimbursements: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
    default: 0,
    comment: 'Regular hours worked',
  })
  regularHours: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
    default: 0,
    comment: 'Overtime hours worked',
  })
  overtimeHours: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
    default: 0,
    comment: 'Total hours worked',
  })
  totalHours: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Regular pay amount',
  })
  regularPay: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0,
    comment: 'Overtime pay amount',
  })
  overtimePay: number;

  @Column({
    type: 'enum',
    enum: PaymentMethod,
    default: PaymentMethod.DIRECT_DEPOSIT,
    comment: 'Payment method',
  })
  paymentMethod: PaymentMethod;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Payment reference number',
  })
  paymentReference?: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When payment was processed',
  })
  paymentProcessedAt?: Date;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Year-to-date totals',
  })
  ytdTotals?: {
    grossPay: number;
    netPay: number;
    taxes: number;
    deductions: number;
    benefits: number;
    hours: number;
    regularHours: number;
    overtimeHours: number;
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Tax withholding information',
  })
  taxWithholding?: {
    federalIncome: number;
    stateIncome: number;
    localIncome: number;
    socialSecurity: number;
    medicare: number;
    unemployment: number;
    disability: number;
    workersComp: number;
    other: Array<{
      type: string;
      amount: number;
      description: string;
    }>;
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Deduction breakdown',
  })
  deductionBreakdown?: {
    preTax: Array<{
      type: string;
      amount: number;
      description: string;
    }>;
    postTax: Array<{
      type: string;
      amount: number;
      description: string;
    }>;
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Benefit breakdown',
  })
  benefitBreakdown?: {
    employerContributions: Array<{
      type: string;
      amount: number;
      description: string;
    }>;
    employeeContributions: Array<{
      type: string;
      amount: number;
      description: string;
    }>;
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Calculation details and audit trail',
  })
  calculationDetails?: {
    baseSalary: number;
    hourlyRate: number;
    overtimeRate: number;
    calculationMethod: string;
    adjustments: Array<{
      type: string;
      amount: number;
      reason: string;
      appliedBy: string;
      appliedAt: Date;
    }>;
    rounding: {
      hoursRounded: number;
      amountRounded: number;
      method: string;
    };
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Processing errors and warnings',
  })
  processingIssues?: {
    errors: Array<{
      code: string;
      message: string;
      field: string;
      severity: 'warning' | 'error' | 'critical';
      timestamp: Date;
    }>;
    warnings: Array<{
      code: string;
      message: string;
      field: string;
      timestamp: Date;
    }>;
  };

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this is a correction payslip',
  })
  isCorrection: boolean;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Original payslip ID if this is a correction',
  })
  originalPayslipId?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Correction reason',
  })
  correctionReason?: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether employee has viewed this payslip',
  })
  isViewed: boolean;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When employee first viewed this payslip',
  })
  viewedAt?: Date;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether payslip is locked for editing',
  })
  isLocked: boolean;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Payslip metadata',
  })
  metadata?: Record<string, any>;

  // Relationships
  @ManyToOne(() => Employee, employee => employee.payslips, { eager: false })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;

  @ManyToOne(() => PayrollPeriod, period => period.payslips, { eager: false })
  @JoinColumn({ name: 'payroll_period_id' })
  payrollPeriod: PayrollPeriod;

  @OneToMany(() => PayrollItem, item => item.payslip, { cascade: true })
  payrollItems: PayrollItem[];

  @ManyToOne(() => Payslip, { nullable: true })
  @JoinColumn({ name: 'original_payslip_id' })
  originalPayslip?: Payslip;

  // Virtual properties
  get isProcessed(): boolean {
    return [PayrollStatus.PROCESSED, PayrollStatus.PAID].includes(this.status);
  }

  get isPaid(): boolean {
    return this.status === PayrollStatus.PAID;
  }

  get hasErrors(): boolean {
    return this.processingIssues?.errors && this.processingIssues.errors.length > 0;
  }

  get hasWarnings(): boolean {
    return this.processingIssues?.warnings && this.processingIssues.warnings.length > 0;
  }

  get effectiveHourlyRate(): number {
    if (this.totalHours === 0) return 0;
    return this.grossPay / this.totalHours;
  }

  get taxRate(): number {
    if (this.grossPay === 0) return 0;
    return (this.totalTaxes / this.grossPay) * 100;
  }

  get deductionRate(): number {
    if (this.grossPay === 0) return 0;
    return (this.totalDeductions / this.grossPay) * 100;
  }

  get netPayRate(): number {
    if (this.grossPay === 0) return 0;
    return (this.netPay / this.grossPay) * 100;
  }

  // Methods
  canBeProcessed(): boolean {
    return this.status === PayrollStatus.APPROVED && !this.hasErrors;
  }

  canBePaid(): boolean {
    return this.status === PayrollStatus.PROCESSED;
  }

  canBeEdited(): boolean {
    return !this.isLocked && [PayrollStatus.DRAFT, PayrollStatus.PENDING].includes(this.status);
  }

  addError(code: string, message: string, field: string, severity: 'warning' | 'error' | 'critical' = 'error'): void {
    if (!this.processingIssues) {
      this.processingIssues = { errors: [], warnings: [] };
    }
    this.processingIssues.errors.push({
      code,
      message,
      field,
      severity,
      timestamp: new Date(),
    });
  }

  addWarning(code: string, message: string, field: string): void {
    if (!this.processingIssues) {
      this.processingIssues = { errors: [], warnings: [] };
    }
    this.processingIssues.warnings.push({
      code,
      message,
      field,
      timestamp: new Date(),
    });
  }

  @BeforeInsert()
  @BeforeUpdate()
  calculateTotals(): void {
    this.totalHours = this.regularHours + this.overtimeHours;
    // Additional validation and calculations can be added here
  }
}
