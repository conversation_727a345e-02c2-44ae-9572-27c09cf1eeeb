{"name": "@storybook/csf-plugin", "version": "9.0.13", "description": "Enrich CSF files via static analysis", "keywords": ["storybook"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/lib/csf-plugin", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/lib/csf-plugin"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.js", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./dist/webpack-loader": {"types": "./dist/webpack-loader.d.ts", "node": "./dist/webpack-loader.js", "import": "./dist/webpack-loader.mjs", "require": "./dist/webpack-loader.js"}, "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/bundle.ts"}, "dependencies": {"unplugin": "^1.3.1"}, "devDependencies": {"typescript": "^5.8.3"}, "peerDependencies": {"storybook": "^9.0.13"}, "publishConfig": {"access": "public"}, "bundler": {"entries": ["./src/index.ts", "./src/webpack-loader.ts"], "externals": ["webpack", "vite", "rollup", "esbuild"], "platform": "node"}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae16"}