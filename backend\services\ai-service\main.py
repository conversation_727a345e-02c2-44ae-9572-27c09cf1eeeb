"""
PeopleNest AI/ML Microservice
FastAPI-based service for AI-powered HR features including:
- Resume parsing and analysis
- Sentiment analysis
- Predictive analytics
- Natural language querying
- Payroll anomaly detection
"""

import logging
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse

from app.core.config import settings
from app.core.logging_config import setup_logging
from app.api.routes import router
from app.services.resume_parser import ResumeParser
from app.services.sentiment_analyzer import SentimentAnalyzer
from app.services.predictive_analytics import PredictiveAnalytics
from app.services.nlp_query import NLPQueryService
from app.services.anomaly_detection import AnomalyDetectionService

# Setup structured logging
setup_logging()
logger = structlog.get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    logger.info("Starting PeopleNest AI Service")

    try:
        # Initialize AI services
        resume_parser = ResumeParser()
        sentiment_analyzer = SentimentAnalyzer()
        predictive_analytics = PredictiveAnalytics()
        nlp_query_service = NLPQueryService()
        anomaly_detection = AnomalyDetectionService()

        # Initialize all services
        await resume_parser.initialize()
        await sentiment_analyzer.initialize()
        await predictive_analytics.initialize()
        await nlp_query_service.initialize()
        await anomaly_detection.initialize()

        # Store services in app state
        app.state.resume_parser = resume_parser
        app.state.sentiment_analyzer = sentiment_analyzer
        app.state.predictive_analytics = predictive_analytics
        app.state.nlp_query_service = nlp_query_service
        app.state.anomaly_detection = anomaly_detection

        logger.info("AI Service startup completed successfully")

        yield

    except Exception as e:
        logger.error(f"Failed to start AI Service: {e}")
        # Don't exit, allow service to start in degraded mode

    finally:
        # Cleanup on shutdown
        logger.info("Shutting down AI Service")
        logger.info("AI Service shutdown completed")


def create_application() -> FastAPI:
    """Create and configure the FastAPI application."""

    app = FastAPI(
        title="PeopleNest AI Service",
        description="AI/ML microservice for HR analytics and automation",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=lifespan,
    )

    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_ORIGINS,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
    )

    # Trusted host middleware
    if hasattr(settings, 'ALLOWED_HOSTS') and settings.ALLOWED_HOSTS:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=settings.ALLOWED_HOSTS,
        )

    # Include API routes
    app.include_router(router)

    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "service": "PeopleNest AI/ML Service",
            "version": "1.0.0",
            "status": "running",
            "description": "AI and Machine Learning microservice for HR analytics",
            "features": [
                "Resume Parsing & Analysis",
                "Sentiment Analysis",
                "Predictive Analytics",
                "Natural Language Queries",
                "Anomaly Detection"
            ],
            "docs": "/docs"
        }

    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint for load balancers and monitoring."""
        try:
            # Check service status
            services_status = {}
            if hasattr(app.state, 'resume_parser'):
                services_status["resume_parser"] = app.state.resume_parser.initialized
            if hasattr(app.state, 'sentiment_analyzer'):
                services_status["sentiment_analyzer"] = app.state.sentiment_analyzer.initialized
            if hasattr(app.state, 'predictive_analytics'):
                services_status["predictive_analytics"] = app.state.predictive_analytics.initialized
            if hasattr(app.state, 'nlp_query_service'):
                services_status["nlp_query_service"] = app.state.nlp_query_service.initialized
            if hasattr(app.state, 'anomaly_detection'):
                services_status["anomaly_detection"] = app.state.anomaly_detection.initialized

            all_healthy = all(services_status.values()) if services_status else False

            return {
                "status": "healthy" if all_healthy else "degraded",
                "service": "ai-service",
                "version": "1.0.0",
                "services": services_status,
            }

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return JSONResponse(
                status_code=503,
                content={
                    "status": "unhealthy",
                    "error": str(e),
                }
            )

    return app


# Create the application instance
app = create_application()


if __name__ == "__main__":
    """Run the application directly for development."""
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8003,
        reload=settings.DEBUG,
        log_level="info",
        access_log=True,
    )
