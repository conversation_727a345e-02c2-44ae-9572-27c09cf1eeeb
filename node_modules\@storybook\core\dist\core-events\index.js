// src/core-events/index.ts
var R = /* @__PURE__ */ ((E) => (E.CHANNEL_WS_DISCONNECT = "channelWSDisconnect", E.CHANNEL_CREATED = "channelCreated", E.CONFIG_ERROR = "co\
nfigError", E<PERSON>STORY_INDEX_INVALIDATED = "storyIndexInvalidated", E.STORY_SPECIFIED = "storySpecified", E.SET_CONFIG = "setConfig", E.SET_STORIES =
"setStories", E.SET_INDEX = "setIndex", E.SET_CURRENT_STORY = "setCurrentStory", E.CURRENT_STORY_WAS_SET = "currentStoryWasSet", E.FORCE_RE_RENDER =
"forceReRender", E.FORCE_REMOUNT = "forceRemount", E.PRELOAD_ENTRIES = "preloadStories", E.STORY_PREPARED = "storyPrepared", E.DOCS_PREPARED =
"docsPrepared", E.STORY_CHANGED = "storyChanged", <PERSON><PERSON>OR<PERSON>_<PERSON>CHANGED = "storyUnchanged", E.STORY_RENDERED = "storyRendered", E.STORY_FINISHED =
"storyFinished", E.STORY_MISSING = "storyMissing", E.STORY_ERRORED = "storyErrored", E.STORY_THREW_EXCEPTION = "storyThrewException", E.STORY_RENDER_PHASE_CHANGED =
"storyRenderPhaseChanged", E.PLAY_FUNCTION_THREW_EXCEPTION = "playFunctionThrewException", E.UNHANDLED_ERRORS_WHILE_PLAYING = "unhandledErro\
rsWhilePlaying", E.UPDATE_STORY_ARGS = "updateStoryArgs", E.STORY_ARGS_UPDATED = "storyArgsUpdated", E.RESET_STORY_ARGS = "resetStoryArgs", E.
SET_FILTER = "setFilter", E.SET_GLOBALS = "setGlobals", E.UPDATE_GLOBALS = "updateGlobals", E.GLOBALS_UPDATED = "globalsUpdated", E.REGISTER_SUBSCRIPTION =
"registerSubscription", E.PREVIEW_KEYDOWN = "previewKeydown", E.PREVIEW_BUILDER_PROGRESS = "preview_builder_progress", E.SELECT_STORY = "sel\
ectStory", E.STORIES_COLLAPSE_ALL = "storiesCollapseAll", E.STORIES_EXPAND_ALL = "storiesExpandAll", E.DOCS_RENDERED = "docsRendered", E.SHARED_STATE_CHANGED =
"sharedStateChanged", E.SHARED_STATE_SET = "sharedStateSet", E.NAVIGATE_URL = "navigateUrl", E.UPDATE_QUERY_PARAMS = "updateQueryParams", E.
REQUEST_WHATS_NEW_DATA = "requestWhatsNewData", E.RESULT_WHATS_NEW_DATA = "resultWhatsNewData", E.SET_WHATS_NEW_CACHE = "setWhatsNewCache", E.
TOGGLE_WHATS_NEW_NOTIFICATIONS = "toggleWhatsNewNotifications", E.TELEMETRY_ERROR = "telemetryError", E.FILE_COMPONENT_SEARCH_REQUEST = "fil\
eComponentSearchRequest", E.FILE_COMPONENT_SEARCH_RESPONSE = "fileComponentSearchResponse", E.SAVE_STORY_REQUEST = "saveStoryRequest", E.SAVE_STORY_RESPONSE =
"saveStoryResponse", E.ARGTYPES_INFO_REQUEST = "argtypesInfoRequest", E.ARGTYPES_INFO_RESPONSE = "argtypesInfoResponse", E.CREATE_NEW_STORYFILE_REQUEST =
"createNewStoryfileRequest", E.CREATE_NEW_STORYFILE_RESPONSE = "createNewStoryfileResponse", E.TESTING_MODULE_CRASH_REPORT = "testingModuleC\
rashReport", E.TESTING_MODULE_PROGRESS_REPORT = "testingModuleProgressReport", E.TESTING_MODULE_RUN_REQUEST = "testingModuleRunRequest", E.TESTING_MODULE_RUN_ALL_REQUEST =
"testingModuleRunAllRequest", E.TESTING_MODULE_CANCEL_TEST_RUN_REQUEST = "testingModuleCancelTestRunRequest", E.TESTING_MODULE_CANCEL_TEST_RUN_RESPONSE =
"testingModuleCancelTestRunResponse", E))(R || {}), _ = R, {
  CHANNEL_WS_DISCONNECT: S,
  CHANNEL_CREATED: T,
  CONFIG_ERROR: N,
  CREATE_NEW_STORYFILE_REQUEST: O,
  CREATE_NEW_STORYFILE_RESPONSE: A,
  CURRENT_STORY_WAS_SET: r,
  DOCS_PREPARED: D,
  DOCS_RENDERED: I,
  FILE_COMPONENT_SEARCH_REQUEST: o,
  FILE_COMPONENT_SEARCH_RESPONSE: C,
  FORCE_RE_RENDER: L,
  FORCE_REMOUNT: P,
  GLOBALS_UPDATED: U,
  NAVIGATE_URL: G,
  PLAY_FUNCTION_THREW_EXCEPTION: a,
  UNHANDLED_ERRORS_WHILE_PLAYING: Y,
  PRELOAD_ENTRIES: t,
  PREVIEW_BUILDER_PROGRESS: d,
  PREVIEW_KEYDOWN: e,
  REGISTER_SUBSCRIPTION: H,
  RESET_STORY_ARGS: W,
  SELECT_STORY: l,
  SET_CONFIG: i,
  SET_CURRENT_STORY: p,
  SET_FILTER: F,
  SET_GLOBALS: u,
  SET_INDEX: M,
  SET_STORIES: s,
  SHARED_STATE_CHANGED: y,
  SHARED_STATE_SET: c,
  STORIES_COLLAPSE_ALL: g,
  STORIES_EXPAND_ALL: h,
  STORY_ARGS_UPDATED: f,
  STORY_CHANGED: Q,
  STORY_ERRORED: x,
  STORY_INDEX_INVALIDATED: m,
  STORY_MISSING: V,
  STORY_PREPARED: w,
  STORY_RENDER_PHASE_CHANGED: B,
  STORY_RENDERED: X,
  STORY_FINISHED: q,
  STORY_SPECIFIED: b,
  STORY_THREW_EXCEPTION: K,
  STORY_UNCHANGED: j,
  UPDATE_GLOBALS: k,
  UPDATE_QUERY_PARAMS: n,
  UPDATE_STORY_ARGS: z,
  REQUEST_WHATS_NEW_DATA: J,
  RESULT_WHATS_NEW_DATA: Z,
  SET_WHATS_NEW_CACHE: $,
  TOGGLE_WHATS_NEW_NOTIFICATIONS: v,
  TELEMETRY_ERROR: EE,
  SAVE_STORY_REQUEST: RE,
  SAVE_STORY_RESPONSE: _E,
  ARGTYPES_INFO_REQUEST: SE,
  ARGTYPES_INFO_RESPONSE: TE,
  TESTING_MODULE_CRASH_REPORT: NE,
  TESTING_MODULE_PROGRESS_REPORT: OE,
  TESTING_MODULE_RUN_REQUEST: AE,
  TESTING_MODULE_RUN_ALL_REQUEST: rE,
  TESTING_MODULE_CANCEL_TEST_RUN_REQUEST: DE,
  TESTING_MODULE_CANCEL_TEST_RUN_RESPONSE: IE
} = R;
export {
  SE as ARGTYPES_INFO_REQUEST,
  TE as ARGTYPES_INFO_RESPONSE,
  T as CHANNEL_CREATED,
  S as CHANNEL_WS_DISCONNECT,
  N as CONFIG_ERROR,
  O as CREATE_NEW_STORYFILE_REQUEST,
  A as CREATE_NEW_STORYFILE_RESPONSE,
  r as CURRENT_STORY_WAS_SET,
  D as DOCS_PREPARED,
  I as DOCS_RENDERED,
  o as FILE_COMPONENT_SEARCH_REQUEST,
  C as FILE_COMPONENT_SEARCH_RESPONSE,
  P as FORCE_REMOUNT,
  L as FORCE_RE_RENDER,
  U as GLOBALS_UPDATED,
  G as NAVIGATE_URL,
  a as PLAY_FUNCTION_THREW_EXCEPTION,
  t as PRELOAD_ENTRIES,
  d as PREVIEW_BUILDER_PROGRESS,
  e as PREVIEW_KEYDOWN,
  H as REGISTER_SUBSCRIPTION,
  J as REQUEST_WHATS_NEW_DATA,
  W as RESET_STORY_ARGS,
  Z as RESULT_WHATS_NEW_DATA,
  RE as SAVE_STORY_REQUEST,
  _E as SAVE_STORY_RESPONSE,
  l as SELECT_STORY,
  i as SET_CONFIG,
  p as SET_CURRENT_STORY,
  F as SET_FILTER,
  u as SET_GLOBALS,
  M as SET_INDEX,
  s as SET_STORIES,
  $ as SET_WHATS_NEW_CACHE,
  y as SHARED_STATE_CHANGED,
  c as SHARED_STATE_SET,
  g as STORIES_COLLAPSE_ALL,
  h as STORIES_EXPAND_ALL,
  f as STORY_ARGS_UPDATED,
  Q as STORY_CHANGED,
  x as STORY_ERRORED,
  q as STORY_FINISHED,
  m as STORY_INDEX_INVALIDATED,
  V as STORY_MISSING,
  w as STORY_PREPARED,
  X as STORY_RENDERED,
  B as STORY_RENDER_PHASE_CHANGED,
  b as STORY_SPECIFIED,
  K as STORY_THREW_EXCEPTION,
  j as STORY_UNCHANGED,
  EE as TELEMETRY_ERROR,
  DE as TESTING_MODULE_CANCEL_TEST_RUN_REQUEST,
  IE as TESTING_MODULE_CANCEL_TEST_RUN_RESPONSE,
  NE as TESTING_MODULE_CRASH_REPORT,
  OE as TESTING_MODULE_PROGRESS_REPORT,
  rE as TESTING_MODULE_RUN_ALL_REQUEST,
  AE as TESTING_MODULE_RUN_REQUEST,
  v as TOGGLE_WHATS_NEW_NOTIFICATIONS,
  Y as UNHANDLED_ERRORS_WHILE_PLAYING,
  k as UPDATE_GLOBALS,
  n as UPDATE_QUERY_PARAMS,
  z as UPDATE_STORY_ARGS,
  _ as default
};
