import * as storybook_internal_docs_tools from 'storybook/internal/docs-tools';
import { extractComponentDescription } from 'storybook/internal/docs-tools';
import { ArgTypesEnhancer } from 'storybook/internal/types';
import { R as ReactRenderer } from './types-5617c98e.js';
import 'react';

declare const parameters: {
    docs: {
        extractArgTypes: storybook_internal_docs_tools.ArgTypesExtractor;
        extractComponentDescription: typeof extractComponentDescription;
    };
};
declare const argTypesEnhancers: ArgTypesEnhancer<ReactRenderer>[];

export { argTypesEnhancers, parameters };
