"""
Configuration settings for the AI Service.
Uses Pydantic Settings for environment variable management.
"""

import os
from typing import List, Optional, Union
from pydantic import BaseSettings, validator, Field
from pydantic_settings import BaseSettings as PydanticBaseSettings


class Settings(PydanticBaseSettings):
    """Application settings loaded from environment variables."""
    
    # Application
    APP_NAME: str = "PeopleNest AI Service"
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # Server
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8005, env="PORT")
    WORKERS: int = Field(default=4, env="WORKERS")
    
    # Security
    SECRET_KEY: str = Field(..., env="SECRET_KEY")
    ALGORITHM: str = Field(default="HS256", env="ALGORITHM")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # CORS
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:3001"],
        env="ALLOWED_ORIGINS"
    )
    ALLOWED_HOSTS: List[str] = Field(
        default=["localhost", "127.0.0.1"],
        env="ALLOWED_HOSTS"
    )
    
    # Database
    DATABASE_URL: str = Field(..., env="DATABASE_URL")
    DATABASE_POOL_SIZE: int = Field(default=10, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=20, env="DATABASE_MAX_OVERFLOW")
    
    # MongoDB for document storage
    MONGODB_URL: str = Field(..., env="MONGODB_URL")
    MONGODB_DATABASE: str = Field(default="peoplenest_ai", env="MONGODB_DATABASE")
    
    # Redis for caching and task queue
    REDIS_URL: str = Field(..., env="REDIS_URL")
    REDIS_DB: int = Field(default=0, env="REDIS_DB")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    
    # Celery for background tasks
    CELERY_BROKER_URL: str = Field(..., env="CELERY_BROKER_URL")
    CELERY_RESULT_BACKEND: str = Field(..., env="CELERY_RESULT_BACKEND")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(default="json", env="LOG_FORMAT")
    
    # Monitoring
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    SENTRY_DSN: Optional[str] = Field(default=None, env="SENTRY_DSN")
    
    # AI/ML Configuration
    MODEL_CACHE_DIR: str = Field(default="./models", env="MODEL_CACHE_DIR")
    MAX_MODEL_MEMORY: int = Field(default=2048, env="MAX_MODEL_MEMORY")  # MB
    
    # Resume Processing
    RESUME_UPLOAD_MAX_SIZE: int = Field(default=10485760, env="RESUME_UPLOAD_MAX_SIZE")  # 10MB
    RESUME_ALLOWED_TYPES: List[str] = Field(
        default=["application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"],
        env="RESUME_ALLOWED_TYPES"
    )
    
    # NLP Models
    SPACY_MODEL: str = Field(default="en_core_web_sm", env="SPACY_MODEL")
    TRANSFORMERS_MODEL: str = Field(default="bert-base-uncased", env="TRANSFORMERS_MODEL")
    SENTIMENT_MODEL: str = Field(default="cardiffnlp/twitter-roberta-base-sentiment-latest", env="SENTIMENT_MODEL")
    
    # External APIs
    OPENAI_API_KEY: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    HUGGINGFACE_API_KEY: Optional[str] = Field(default=None, env="HUGGINGFACE_API_KEY")
    
    # File Storage
    STORAGE_TYPE: str = Field(default="local", env="STORAGE_TYPE")  # local, s3, gcs
    STORAGE_BUCKET: Optional[str] = Field(default=None, env="STORAGE_BUCKET")
    STORAGE_REGION: Optional[str] = Field(default=None, env="STORAGE_REGION")
    
    # AWS Configuration (if using S3)
    AWS_ACCESS_KEY_ID: Optional[str] = Field(default=None, env="AWS_ACCESS_KEY_ID")
    AWS_SECRET_ACCESS_KEY: Optional[str] = Field(default=None, env="AWS_SECRET_ACCESS_KEY")
    AWS_REGION: Optional[str] = Field(default="us-east-1", env="AWS_REGION")
    
    # Performance
    MAX_CONCURRENT_REQUESTS: int = Field(default=100, env="MAX_CONCURRENT_REQUESTS")
    REQUEST_TIMEOUT: int = Field(default=300, env="REQUEST_TIMEOUT")  # seconds
    
    # Feature Flags
    ENABLE_RESUME_PARSING: bool = Field(default=True, env="ENABLE_RESUME_PARSING")
    ENABLE_SENTIMENT_ANALYSIS: bool = Field(default=True, env="ENABLE_SENTIMENT_ANALYSIS")
    ENABLE_PREDICTIVE_ANALYTICS: bool = Field(default=True, env="ENABLE_PREDICTIVE_ANALYTICS")
    ENABLE_NL_QUERYING: bool = Field(default=True, env="ENABLE_NL_QUERYING")
    ENABLE_ANOMALY_DETECTION: bool = Field(default=True, env="ENABLE_ANOMALY_DETECTION")
    
    @validator("ALLOWED_ORIGINS", pre=True)
    def parse_cors_origins(cls, v):
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v):
        """Parse allowed hosts from string or list."""
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @validator("RESUME_ALLOWED_TYPES", pre=True)
    def parse_allowed_types(cls, v):
        """Parse allowed file types from string or list."""
        if isinstance(v, str):
            return [type_.strip() for type_ in v.split(",")]
        return v
    
    @validator("LOG_LEVEL")
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL must be one of {valid_levels}")
        return v.upper()
    
    @validator("ENVIRONMENT")
    def validate_environment(cls, v):
        """Validate environment."""
        valid_envs = ["development", "staging", "production"]
        if v.lower() not in valid_envs:
            raise ValueError(f"ENVIRONMENT must be one of {valid_envs}")
        return v.lower()
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# Create settings instance
settings = Settings()


# Development settings override
if settings.ENVIRONMENT == "development":
    settings.DEBUG = True
    settings.LOG_LEVEL = "DEBUG"


# Production settings validation
if settings.ENVIRONMENT == "production":
    required_production_vars = [
        "SECRET_KEY",
        "DATABASE_URL",
        "MONGODB_URL",
        "REDIS_URL",
        "CELERY_BROKER_URL",
        "CELERY_RESULT_BACKEND",
    ]
    
    for var in required_production_vars:
        if not getattr(settings, var, None):
            raise ValueError(f"Production environment requires {var} to be set")


# Export settings
__all__ = ["settings"]
