"""
Resume parsing service using NLP and ML techniques.
Extracts structured information from resume documents.
"""

import re
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import asyncio
from pathlib import Path

import spacy
import pandas as pd
from transformers import pipeline, AutoTokenizer, AutoModel
import PyPDF2
import docx
from PIL import Image
import pytesseract
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

from app.core.config import settings
from app.models.resume import ResumeData, ContactInfo, Experience, Education, Skill
from app.core.exceptions import ResumeParsingError

logger = logging.getLogger(__name__)


class ResumeParser:
    """Advanced resume parsing service with ML-powered extraction."""
    
    def __init__(self):
        self.nlp = None
        self.skill_classifier = None
        self.experience_classifier = None
        self.education_classifier = None
        self.skill_vectorizer = None
        self.skill_database = None
        
    async def initialize(self):
        """Initialize NLP models and classifiers."""
        try:
            # Load spaCy model
            self.nlp = spacy.load(settings.SPACY_MODEL)
            logger.info(f"Loaded spaCy model: {settings.SPACY_MODEL}")
            
            # Initialize skill classifier
            self.skill_classifier = pipeline(
                "text-classification",
                model="microsoft/DialoGPT-medium",
                tokenizer="microsoft/DialoGPT-medium"
            )
            
            # Load skill database for matching
            await self._load_skill_database()
            
            # Initialize experience and education classifiers
            await self._initialize_classifiers()
            
            logger.info("Resume parser initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize resume parser: {e}")
            raise ResumeParsingError(f"Initialization failed: {e}")
    
    async def parse_resume(self, file_content: bytes, filename: str, content_type: str) -> ResumeData:
        """Parse resume from file content and extract structured data."""
        try:
            # Extract text from file
            text = await self._extract_text(file_content, filename, content_type)
            
            if not text.strip():
                raise ResumeParsingError("No text content found in resume")
            
            # Clean and preprocess text
            cleaned_text = self._clean_text(text)
            
            # Extract structured information
            contact_info = await self._extract_contact_info(cleaned_text)
            experiences = await self._extract_experience(cleaned_text)
            education = await self._extract_education(cleaned_text)
            skills = await self._extract_skills(cleaned_text)
            
            # Additional extractions
            summary = await self._extract_summary(cleaned_text)
            certifications = await self._extract_certifications(cleaned_text)
            languages = await self._extract_languages(cleaned_text)
            
            # Calculate quality score
            quality_score = self._calculate_quality_score(
                contact_info, experiences, education, skills, summary
            )
            
            # Create resume data object
            resume_data = ResumeData(
                raw_text=text,
                cleaned_text=cleaned_text,
                contact_info=contact_info,
                summary=summary,
                experiences=experiences,
                education=education,
                skills=skills,
                certifications=certifications,
                languages=languages,
                quality_score=quality_score,
                parsed_at=datetime.utcnow(),
                filename=filename,
                content_type=content_type,
            )
            
            logger.info(f"Successfully parsed resume: {filename}")
            return resume_data
            
        except Exception as e:
            logger.error(f"Failed to parse resume {filename}: {e}")
            raise ResumeParsingError(f"Parsing failed: {e}")
    
    async def _extract_text(self, file_content: bytes, filename: str, content_type: str) -> str:
        """Extract text from various file formats."""
        try:
            if content_type == "application/pdf":
                return await self._extract_pdf_text(file_content)
            elif content_type in ["application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"]:
                return await self._extract_docx_text(file_content)
            elif content_type.startswith("image/"):
                return await self._extract_image_text(file_content)
            else:
                # Try to decode as plain text
                return file_content.decode('utf-8', errors='ignore')
                
        except Exception as e:
            logger.error(f"Text extraction failed for {filename}: {e}")
            raise ResumeParsingError(f"Text extraction failed: {e}")
    
    async def _extract_pdf_text(self, file_content: bytes) -> str:
        """Extract text from PDF files."""
        try:
            import io
            pdf_reader = PyPDF2.PdfReader(io.BytesIO(file_content))
            text = ""
            
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            
            return text
            
        except Exception as e:
            logger.error(f"PDF text extraction failed: {e}")
            raise ResumeParsingError(f"PDF extraction failed: {e}")
    
    async def _extract_docx_text(self, file_content: bytes) -> str:
        """Extract text from DOCX files."""
        try:
            import io
            doc = docx.Document(io.BytesIO(file_content))
            text = ""
            
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            return text
            
        except Exception as e:
            logger.error(f"DOCX text extraction failed: {e}")
            raise ResumeParsingError(f"DOCX extraction failed: {e}")
    
    async def _extract_image_text(self, file_content: bytes) -> str:
        """Extract text from images using OCR."""
        try:
            import io
            image = Image.open(io.BytesIO(file_content))
            text = pytesseract.image_to_string(image)
            return text
            
        except Exception as e:
            logger.error(f"OCR text extraction failed: {e}")
            raise ResumeParsingError(f"OCR extraction failed: {e}")
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content."""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep important punctuation
        text = re.sub(r'[^\w\s\.\,\;\:\-\(\)\@\+\#]', '', text)
        
        # Normalize line breaks
        text = re.sub(r'\n+', '\n', text)
        
        return text.strip()
    
    async def _extract_contact_info(self, text: str) -> ContactInfo:
        """Extract contact information using regex and NLP."""
        contact_info = ContactInfo()
        
        # Email extraction
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        if emails:
            contact_info.email = emails[0]
        
        # Phone extraction
        phone_pattern = r'(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'
        phones = re.findall(phone_pattern, text)
        if phones:
            contact_info.phone = ''.join(phones[0]) if isinstance(phones[0], tuple) else phones[0]
        
        # Name extraction using NLP
        doc = self.nlp(text[:500])  # Process first 500 chars for name
        for ent in doc.ents:
            if ent.label_ == "PERSON" and not contact_info.full_name:
                contact_info.full_name = ent.text
                break
        
        # LinkedIn extraction
        linkedin_pattern = r'linkedin\.com/in/([a-zA-Z0-9-]+)'
        linkedin_matches = re.findall(linkedin_pattern, text.lower())
        if linkedin_matches:
            contact_info.linkedin = f"https://linkedin.com/in/{linkedin_matches[0]}"
        
        # Address extraction (basic)
        address_pattern = r'\d+\s+[A-Za-z\s]+,\s*[A-Za-z\s]+,\s*[A-Z]{2}\s*\d{5}'
        addresses = re.findall(address_pattern, text)
        if addresses:
            contact_info.address = addresses[0]
        
        return contact_info
    
    async def _extract_experience(self, text: str) -> List[Experience]:
        """Extract work experience using NLP and pattern matching."""
        experiences = []
        
        # Split text into sections
        sections = self._split_into_sections(text)
        experience_section = self._find_experience_section(sections)
        
        if not experience_section:
            return experiences
        
        # Extract individual experiences
        experience_blocks = self._split_experience_blocks(experience_section)
        
        for block in experience_blocks:
            experience = await self._parse_experience_block(block)
            if experience:
                experiences.append(experience)
        
        return experiences
    
    async def _extract_education(self, text: str) -> List[Education]:
        """Extract education information."""
        education_list = []
        
        # Split text into sections
        sections = self._split_into_sections(text)
        education_section = self._find_education_section(sections)
        
        if not education_section:
            return education_list
        
        # Extract individual education entries
        education_blocks = self._split_education_blocks(education_section)
        
        for block in education_blocks:
            education = await self._parse_education_block(block)
            if education:
                education_list.append(education)
        
        return education_list
    
    async def _extract_skills(self, text: str) -> List[Skill]:
        """Extract skills using ML-based classification and matching."""
        skills = []
        
        # Find skills section
        sections = self._split_into_sections(text)
        skills_section = self._find_skills_section(sections)
        
        if skills_section:
            # Extract from dedicated skills section
            section_skills = await self._extract_skills_from_section(skills_section)
            skills.extend(section_skills)
        
        # Extract skills from entire text using ML
        ml_skills = await self._extract_skills_with_ml(text)
        skills.extend(ml_skills)
        
        # Deduplicate and categorize
        skills = self._deduplicate_skills(skills)
        skills = await self._categorize_skills(skills)
        
        return skills
    
    async def _extract_summary(self, text: str) -> Optional[str]:
        """Extract professional summary or objective."""
        # Look for summary keywords
        summary_keywords = [
            'summary', 'objective', 'profile', 'overview', 
            'about', 'professional summary', 'career objective'
        ]
        
        lines = text.split('\n')
        summary_start = -1
        
        for i, line in enumerate(lines):
            if any(keyword in line.lower() for keyword in summary_keywords):
                summary_start = i
                break
        
        if summary_start >= 0:
            # Extract next few lines as summary
            summary_lines = []
            for i in range(summary_start + 1, min(summary_start + 10, len(lines))):
                line = lines[i].strip()
                if line and not self._is_section_header(line):
                    summary_lines.append(line)
                elif len(summary_lines) > 0:
                    break
            
            if summary_lines:
                return ' '.join(summary_lines)
        
        return None
    
    async def _extract_certifications(self, text: str) -> List[str]:
        """Extract certifications and licenses."""
        certifications = []
        
        # Common certification patterns
        cert_patterns = [
            r'(PMP|CISSP|CISA|CISM|AWS|Azure|Google Cloud|Salesforce|Oracle|Microsoft|Cisco)',
            r'Certified\s+[A-Za-z\s]+',
            r'[A-Za-z\s]+\s+Certification',
        ]
        
        for pattern in cert_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            certifications.extend(matches)
        
        return list(set(certifications))
    
    async def _extract_languages(self, text: str) -> List[str]:
        """Extract language skills."""
        languages = []
        
        # Common language patterns
        language_keywords = ['languages', 'language skills', 'fluent in', 'native', 'bilingual']
        
        lines = text.split('\n')
        for i, line in enumerate(lines):
            if any(keyword in line.lower() for keyword in language_keywords):
                # Extract languages from this line and next few lines
                for j in range(i, min(i + 5, len(lines))):
                    lang_line = lines[j]
                    # Simple language extraction (can be improved)
                    common_languages = [
                        'English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese',
                        'Chinese', 'Japanese', 'Korean', 'Arabic', 'Hindi', 'Russian'
                    ]
                    for lang in common_languages:
                        if lang.lower() in lang_line.lower():
                            languages.append(lang)
                break
        
        return list(set(languages))
    
    def _calculate_quality_score(self, contact_info: ContactInfo, experiences: List[Experience], 
                                education: List[Education], skills: List[Skill], summary: Optional[str]) -> float:
        """Calculate resume quality score based on completeness and structure."""
        score = 0.0
        
        # Contact information (20 points)
        if contact_info.email:
            score += 5
        if contact_info.phone:
            score += 5
        if contact_info.full_name:
            score += 5
        if contact_info.linkedin:
            score += 5
        
        # Experience (30 points)
        if experiences:
            score += min(len(experiences) * 5, 20)
            if any(exp.description for exp in experiences):
                score += 10
        
        # Education (20 points)
        if education:
            score += min(len(education) * 10, 20)
        
        # Skills (20 points)
        if skills:
            score += min(len(skills) * 2, 20)
        
        # Summary (10 points)
        if summary:
            score += 10
        
        return min(score, 100.0)
    
    # Helper methods for section parsing
    def _split_into_sections(self, text: str) -> List[str]:
        """Split text into logical sections."""
        # Simple section splitting based on common headers
        section_headers = [
            'experience', 'education', 'skills', 'summary', 'objective',
            'work experience', 'employment', 'qualifications', 'certifications'
        ]
        
        lines = text.split('\n')
        sections = []
        current_section = []
        
        for line in lines:
            if self._is_section_header(line, section_headers):
                if current_section:
                    sections.append('\n'.join(current_section))
                current_section = [line]
            else:
                current_section.append(line)
        
        if current_section:
            sections.append('\n'.join(current_section))
        
        return sections
    
    def _is_section_header(self, line: str, headers: List[str] = None) -> bool:
        """Check if line is a section header."""
        if headers is None:
            headers = ['experience', 'education', 'skills', 'summary', 'objective']
        
        line_lower = line.lower().strip()
        return any(header in line_lower for header in headers) and len(line.strip()) < 50
    
    def _find_experience_section(self, sections: List[str]) -> Optional[str]:
        """Find the experience section from text sections."""
        experience_keywords = ['experience', 'employment', 'work history', 'professional experience']
        
        for section in sections:
            first_line = section.split('\n')[0].lower()
            if any(keyword in first_line for keyword in experience_keywords):
                return section
        
        return None
    
    def _find_education_section(self, sections: List[str]) -> Optional[str]:
        """Find the education section from text sections."""
        education_keywords = ['education', 'academic', 'qualifications', 'degrees']
        
        for section in sections:
            first_line = section.split('\n')[0].lower()
            if any(keyword in first_line for keyword in education_keywords):
                return section
        
        return None
    
    def _find_skills_section(self, sections: List[str]) -> Optional[str]:
        """Find the skills section from text sections."""
        skills_keywords = ['skills', 'technical skills', 'competencies', 'expertise']
        
        for section in sections:
            first_line = section.split('\n')[0].lower()
            if any(keyword in first_line for keyword in skills_keywords):
                return section
        
        return None
    
    async def _load_skill_database(self):
        """Load skill database for matching and categorization."""
        # This would typically load from a database or file
        # For now, using a simple in-memory structure
        self.skill_database = {
            'programming': [
                'Python', 'Java', 'JavaScript', 'C++', 'C#', 'Ruby', 'PHP', 'Go', 'Rust',
                'TypeScript', 'Kotlin', 'Swift', 'Scala', 'R', 'MATLAB'
            ],
            'web_development': [
                'HTML', 'CSS', 'React', 'Angular', 'Vue.js', 'Node.js', 'Express',
                'Django', 'Flask', 'Spring', 'Laravel', 'Bootstrap', 'jQuery'
            ],
            'databases': [
                'SQL', 'MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'Oracle',
                'SQLite', 'Cassandra', 'DynamoDB', 'Neo4j'
            ],
            'cloud': [
                'AWS', 'Azure', 'Google Cloud', 'Docker', 'Kubernetes',
                'Terraform', 'Jenkins', 'GitLab CI', 'CircleCI'
            ],
            'data_science': [
                'Machine Learning', 'Deep Learning', 'TensorFlow', 'PyTorch',
                'Pandas', 'NumPy', 'Scikit-learn', 'Jupyter', 'Tableau', 'Power BI'
            ],
            'soft_skills': [
                'Leadership', 'Communication', 'Problem Solving', 'Team Work',
                'Project Management', 'Critical Thinking', 'Creativity'
            ]
        }
        
        # Create TF-IDF vectorizer for skill matching
        all_skills = []
        for category_skills in self.skill_database.values():
            all_skills.extend(category_skills)
        
        self.skill_vectorizer = TfidfVectorizer(lowercase=True, stop_words='english')
        self.skill_vectorizer.fit(all_skills)
    
    async def _initialize_classifiers(self):
        """Initialize ML classifiers for experience and education parsing."""
        # This would typically load pre-trained models
        # For now, using simple rule-based approaches
        pass
    
    def _split_experience_blocks(self, experience_section: str) -> List[str]:
        """Split experience section into individual job blocks."""
        # Simple splitting based on date patterns or company names
        lines = experience_section.split('\n')[1:]  # Skip header
        blocks = []
        current_block = []
        
        for line in lines:
            if self._looks_like_job_title_or_company(line) and current_block:
                blocks.append('\n'.join(current_block))
                current_block = [line]
            else:
                current_block.append(line)
        
        if current_block:
            blocks.append('\n'.join(current_block))
        
        return blocks
    
    def _looks_like_job_title_or_company(self, line: str) -> bool:
        """Check if line looks like a job title or company name."""
        # Simple heuristics
        line = line.strip()
        if not line:
            return False
        
        # Check for date patterns
        date_pattern = r'\d{4}|\d{1,2}/\d{4}|\d{1,2}/\d{1,2}/\d{4}'
        if re.search(date_pattern, line):
            return True
        
        # Check for common job title words
        job_words = ['manager', 'developer', 'engineer', 'analyst', 'director', 'specialist']
        if any(word in line.lower() for word in job_words):
            return True
        
        return False
    
    async def _parse_experience_block(self, block: str) -> Optional[Experience]:
        """Parse individual experience block."""
        lines = [line.strip() for line in block.split('\n') if line.strip()]
        if not lines:
            return None
        
        experience = Experience()
        
        # First line is usually job title or company
        experience.title = lines[0]
        
        # Look for company name and dates
        for line in lines[1:]:
            if self._contains_date(line):
                experience.duration = self._extract_duration(line)
            elif not experience.company and len(line) < 100:  # Likely company name
                experience.company = line
            else:
                # Add to description
                if experience.description:
                    experience.description += ' ' + line
                else:
                    experience.description = line
        
        return experience if experience.title else None
    
    def _contains_date(self, text: str) -> bool:
        """Check if text contains date information."""
        date_patterns = [
            r'\d{4}',
            r'\d{1,2}/\d{4}',
            r'\d{1,2}/\d{1,2}/\d{4}',
            r'(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)',
            r'present|current'
        ]
        
        return any(re.search(pattern, text, re.IGNORECASE) for pattern in date_patterns)
    
    def _extract_duration(self, text: str) -> str:
        """Extract duration from text."""
        # Simple extraction - can be improved
        return text.strip()
    
    def _split_education_blocks(self, education_section: str) -> List[str]:
        """Split education section into individual education blocks."""
        # Similar to experience splitting
        lines = education_section.split('\n')[1:]  # Skip header
        blocks = []
        current_block = []
        
        for line in lines:
            if self._looks_like_degree_or_school(line) and current_block:
                blocks.append('\n'.join(current_block))
                current_block = [line]
            else:
                current_block.append(line)
        
        if current_block:
            blocks.append('\n'.join(current_block))
        
        return blocks
    
    def _looks_like_degree_or_school(self, line: str) -> bool:
        """Check if line looks like a degree or school name."""
        degree_words = ['bachelor', 'master', 'phd', 'doctorate', 'diploma', 'certificate']
        school_words = ['university', 'college', 'institute', 'school']
        
        line_lower = line.lower()
        return any(word in line_lower for word in degree_words + school_words)
    
    async def _parse_education_block(self, block: str) -> Optional[Education]:
        """Parse individual education block."""
        lines = [line.strip() for line in block.split('\n') if line.strip()]
        if not lines:
            return None
        
        education = Education()
        
        for line in lines:
            if self._looks_like_degree_or_school(line):
                if 'university' in line.lower() or 'college' in line.lower():
                    education.institution = line
                else:
                    education.degree = line
            elif self._contains_date(line):
                education.graduation_date = self._extract_duration(line)
        
        return education if education.degree or education.institution else None
    
    async def _extract_skills_from_section(self, skills_section: str) -> List[Skill]:
        """Extract skills from dedicated skills section."""
        skills = []
        
        # Remove header line
        lines = skills_section.split('\n')[1:]
        text = ' '.join(lines)
        
        # Split by common delimiters
        skill_text = re.split(r'[,;•\n\t]', text)
        
        for skill_item in skill_text:
            skill_item = skill_item.strip()
            if skill_item and len(skill_item) < 50:  # Reasonable skill length
                skill = Skill(name=skill_item, source='section')
                skills.append(skill)
        
        return skills
    
    async def _extract_skills_with_ml(self, text: str) -> List[Skill]:
        """Extract skills using ML-based matching."""
        skills = []
        
        if not self.skill_database:
            return skills
        
        # Use TF-IDF similarity to match skills
        text_vector = self.skill_vectorizer.transform([text])
        
        for category, category_skills in self.skill_database.items():
            for skill_name in category_skills:
                skill_vector = self.skill_vectorizer.transform([skill_name])
                similarity = cosine_similarity(text_vector, skill_vector)[0][0]
                
                if similarity > 0.1:  # Threshold for skill matching
                    skill = Skill(
                        name=skill_name,
                        category=category,
                        confidence=similarity,
                        source='ml'
                    )
                    skills.append(skill)
        
        return skills
    
    def _deduplicate_skills(self, skills: List[Skill]) -> List[Skill]:
        """Remove duplicate skills."""
        seen = set()
        unique_skills = []
        
        for skill in skills:
            skill_key = skill.name.lower().strip()
            if skill_key not in seen:
                seen.add(skill_key)
                unique_skills.append(skill)
        
        return unique_skills
    
    async def _categorize_skills(self, skills: List[Skill]) -> List[Skill]:
        """Categorize skills that don't have categories."""
        for skill in skills:
            if not skill.category:
                # Simple categorization based on skill database
                for category, category_skills in self.skill_database.items():
                    if any(skill.name.lower() in cat_skill.lower() for cat_skill in category_skills):
                        skill.category = category
                        break
                
                # Default category
                if not skill.category:
                    skill.category = 'other'
        
        return skills
