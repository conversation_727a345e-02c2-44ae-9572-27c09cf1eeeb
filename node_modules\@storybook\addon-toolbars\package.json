{"name": "@storybook/addon-toolbars", "version": "8.6.14", "description": "Create your own toolbar items that control story rendering", "keywords": ["addon", "storybook", "theming", "i18n", "internationalization", "test", "essentials"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/toolbars", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/toolbars"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./manager": "./dist/manager.js", "./register": "./dist/manager.js", "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/addon-bundle.ts"}, "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.7.3"}, "peerDependencies": {"storybook": "^8.6.14"}, "publishConfig": {"access": "public"}, "bundler": {"exportEntries": ["./src/index.ts"], "managerEntries": ["./src/manager.tsx"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae16", "storybook": {"displayName": "<PERSON>l<PERSON><PERSON>", "icon": "https://user-images.githubusercontent.com/263385/101991677-48cdf300-3c7c-11eb-93b4-19b0e3366959.png", "unsupportedFrameworks": ["react-native"]}}