import { chromium, FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global teardown...');

  // Launch browser for teardown
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Clean up test data if needed
    await cleanupTestData(page);

    console.log('✅ Global teardown completed successfully');
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error to avoid failing the test run
  } finally {
    await browser.close();
  }
}

async function cleanupTestData(page: any) {
  console.log('🗑️ Cleaning up test data...');

  try {
    // Login as admin to perform cleanup
    const loginResponse = await page.request.post('http://localhost:3001/api/auth/login', {
      data: {
        email: '<EMAIL>',
        password: 'Password1234',
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (loginResponse.ok()) {
      const loginData = await loginResponse.json();
      const authHeaders = {
        'Authorization': `Bearer ${loginData.accessToken}`,
        'Content-Type': 'application/json',
      };

      // Clean up test employees created during tests
      const employeesResponse = await page.request.get('http://localhost:3001/api/employees?search=test', {
        headers: authHeaders,
      });

      if (employeesResponse.ok()) {
        const employeesData = await employeesResponse.json();
        for (const employee of employeesData.data) {
          if (employee.email.includes('test') || employee.firstName.includes('Test')) {
            await page.request.delete(`http://localhost:3001/api/employees/${employee.id}`, {
              headers: authHeaders,
            });
          }
        }
      }

      // Clean up test departments
      const departmentsResponse = await page.request.get('http://localhost:3001/api/departments', {
        headers: authHeaders,
      });

      if (departmentsResponse.ok()) {
        const departmentsData = await departmentsResponse.json();
        for (const department of departmentsData) {
          if (department.name.includes('Test')) {
            await page.request.delete(`http://localhost:3001/api/departments/${department.id}`, {
              headers: authHeaders,
            });
          }
        }
      }

      // Clean up test positions
      const positionsResponse = await page.request.get('http://localhost:3001/api/positions', {
        headers: authHeaders,
      });

      if (positionsResponse.ok()) {
        const positionsData = await positionsResponse.json();
        for (const position of positionsData) {
          if (position.title.includes('Test')) {
            await page.request.delete(`http://localhost:3001/api/positions/${position.id}`, {
              headers: authHeaders,
            });
          }
        }
      }

      console.log('✅ Test data cleanup completed');
    }
  } catch (error) {
    console.warn('⚠️ Test data cleanup failed:', error.message);
    // Don't throw error as cleanup is best effort
  }
}

export default globalTeardown;
