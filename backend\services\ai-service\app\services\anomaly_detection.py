"""
Anomaly detection service for payroll and HR data.
Detects unusual patterns, potential fraud, and compliance issues.
"""

import logging
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timedelta
import asyncio

import numpy as np
import pandas as pd
from sklearn.ensemble import IsolationForest
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

from app.core.config import settings
from app.core.exceptions import DataProcessingError, ValidationError
from app.models.anomaly import (
    AnomalyDetectionResult, PayrollAnomaly, ComplianceAlert,
    FraudRiskAssessment, AnomalyReport
)

logger = logging.getLogger(__name__)


class AnomalyDetectionService:
    """Advanced anomaly detection for payroll and HR data."""
    
    def __init__(self):
        self.isolation_forest = None
        self.dbscan_model = None
        self.scaler = None
        self.baseline_stats = {}
        self.fraud_patterns = {}
        self.compliance_rules = {}
        self.initialized = False
        
    async def initialize(self):
        """Initialize anomaly detection models."""
        try:
            # Initialize Isolation Forest for outlier detection
            self.isolation_forest = IsolationForest(
                contamination=0.1,  # Expect 10% anomalies
                random_state=42,
                n_estimators=100
            )
            
            # Initialize DBSCAN for clustering-based anomaly detection
            self.dbscan_model = DBSCAN(
                eps=0.5,
                min_samples=5
            )
            
            # Initialize scaler
            self.scaler = StandardScaler()
            
            # Load fraud patterns and compliance rules
            await self._load_fraud_patterns()
            await self._load_compliance_rules()
            
            self.initialized = True
            logger.info("Anomaly detection service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize anomaly detection: {e}")
            raise DataProcessingError(f"Initialization failed: {e}")
    
    async def detect_payroll_anomalies(self, payroll_data: List[Dict[str, Any]]) -> AnomalyDetectionResult:
        """Detect anomalies in payroll data."""
        if not self.initialized:
            await self.initialize()
        
        try:
            df = pd.DataFrame(payroll_data)
            
            if df.empty:
                raise ValidationError("No payroll data provided")
            
            # Detect different types of anomalies
            salary_anomalies = await self._detect_salary_anomalies(df)
            overtime_anomalies = await self._detect_overtime_anomalies(df)
            deduction_anomalies = await self._detect_deduction_anomalies(df)
            frequency_anomalies = await self._detect_frequency_anomalies(df)
            pattern_anomalies = await self._detect_pattern_anomalies(df)
            
            # Combine all anomalies
            all_anomalies = (
                salary_anomalies + overtime_anomalies + deduction_anomalies +
                frequency_anomalies + pattern_anomalies
            )
            
            # Calculate risk scores
            risk_assessment = await self._assess_fraud_risk(df, all_anomalies)
            
            # Generate compliance alerts
            compliance_alerts = await self._check_compliance(df)
            
            # Create summary statistics
            summary = {
                'total_records': len(df),
                'anomalies_detected': len(all_anomalies),
                'anomaly_rate': len(all_anomalies) / len(df) if len(df) > 0 else 0,
                'high_risk_count': len([a for a in all_anomalies if a.severity == 'high']),
                'medium_risk_count': len([a for a in all_anomalies if a.severity == 'medium']),
                'low_risk_count': len([a for a in all_anomalies if a.severity == 'low']),
            }
            
            return AnomalyDetectionResult(
                anomalies=all_anomalies,
                risk_assessment=risk_assessment,
                compliance_alerts=compliance_alerts,
                summary=summary,
                analyzed_at=datetime.utcnow(),
                data_period={
                    'start': df['pay_date'].min() if 'pay_date' in df.columns else None,
                    'end': df['pay_date'].max() if 'pay_date' in df.columns else None,
                },
            )
            
        except Exception as e:
            logger.error(f"Payroll anomaly detection failed: {e}")
            raise DataProcessingError(f"Anomaly detection failed: {e}")
    
    async def _detect_salary_anomalies(self, df: pd.DataFrame) -> List[PayrollAnomaly]:
        """Detect salary-related anomalies."""
        anomalies = []
        
        try:
            if 'salary' not in df.columns:
                return anomalies
            
            # Statistical outlier detection
            salary_data = df['salary'].dropna()
            if len(salary_data) < 10:
                return anomalies
            
            # Z-score method
            z_scores = np.abs(stats.zscore(salary_data))
            outlier_threshold = 3
            
            outlier_indices = np.where(z_scores > outlier_threshold)[0]
            
            for idx in outlier_indices:
                record = df.iloc[idx]
                anomalies.append(PayrollAnomaly(
                    employee_id=record.get('employee_id', 'unknown'),
                    anomaly_type='salary_outlier',
                    description=f'Salary {record["salary"]} is {z_scores[idx]:.2f} standard deviations from mean',
                    severity=self._calculate_severity(z_scores[idx], 'z_score'),
                    confidence=min(z_scores[idx] / 5, 1.0),
                    detected_value=record['salary'],
                    expected_range={
                        'min': float(salary_data.mean() - 2 * salary_data.std()),
                        'max': float(salary_data.mean() + 2 * salary_data.std()),
                    },
                    metadata={
                        'z_score': float(z_scores[idx]),
                        'mean_salary': float(salary_data.mean()),
                        'std_salary': float(salary_data.std()),
                    },
                ))
            
            # Sudden salary changes
            if 'previous_salary' in df.columns:
                df['salary_change_pct'] = ((df['salary'] - df['previous_salary']) / df['previous_salary'] * 100).fillna(0)
                
                # Flag large salary changes (>50% increase or >20% decrease)
                large_increases = df[df['salary_change_pct'] > 50]
                large_decreases = df[df['salary_change_pct'] < -20]
                
                for _, record in large_increases.iterrows():
                    anomalies.append(PayrollAnomaly(
                        employee_id=record.get('employee_id', 'unknown'),
                        anomaly_type='salary_spike',
                        description=f'Salary increased by {record["salary_change_pct"]:.1f}%',
                        severity='high' if record['salary_change_pct'] > 100 else 'medium',
                        confidence=0.8,
                        detected_value=record['salary'],
                        expected_range={'max': record['previous_salary'] * 1.5},
                        metadata={'change_percentage': float(record['salary_change_pct'])},
                    ))
                
                for _, record in large_decreases.iterrows():
                    anomalies.append(PayrollAnomaly(
                        employee_id=record.get('employee_id', 'unknown'),
                        anomaly_type='salary_drop',
                        description=f'Salary decreased by {abs(record["salary_change_pct"]):.1f}%',
                        severity='medium',
                        confidence=0.7,
                        detected_value=record['salary'],
                        expected_range={'min': record['previous_salary'] * 0.8},
                        metadata={'change_percentage': float(record['salary_change_pct'])},
                    ))
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Salary anomaly detection failed: {e}")
            return anomalies
    
    async def _detect_overtime_anomalies(self, df: pd.DataFrame) -> List[PayrollAnomaly]:
        """Detect overtime-related anomalies."""
        anomalies = []
        
        try:
            if 'overtime_hours' not in df.columns:
                return anomalies
            
            overtime_data = df['overtime_hours'].dropna()
            if len(overtime_data) == 0:
                return anomalies
            
            # Excessive overtime detection
            excessive_threshold = 20  # hours per week
            excessive_overtime = df[df['overtime_hours'] > excessive_threshold]
            
            for _, record in excessive_overtime.iterrows():
                anomalies.append(PayrollAnomaly(
                    employee_id=record.get('employee_id', 'unknown'),
                    anomaly_type='excessive_overtime',
                    description=f'Overtime hours ({record["overtime_hours"]}) exceed threshold ({excessive_threshold})',
                    severity='high' if record['overtime_hours'] > 40 else 'medium',
                    confidence=0.9,
                    detected_value=record['overtime_hours'],
                    expected_range={'max': excessive_threshold},
                    metadata={'threshold': excessive_threshold},
                ))
            
            # Unusual overtime patterns
            if len(overtime_data) > 4:  # Need at least 4 data points
                # Check for sudden spikes in overtime
                overtime_mean = overtime_data.mean()
                overtime_std = overtime_data.std()
                
                if overtime_std > 0:
                    z_scores = np.abs((overtime_data - overtime_mean) / overtime_std)
                    outliers = df[z_scores > 2.5]
                    
                    for _, record in outliers.iterrows():
                        if record['overtime_hours'] > overtime_mean + 2 * overtime_std:
                            anomalies.append(PayrollAnomaly(
                                employee_id=record.get('employee_id', 'unknown'),
                                anomaly_type='overtime_spike',
                                description=f'Unusual overtime spike: {record["overtime_hours"]} hours',
                                severity='medium',
                                confidence=0.7,
                                detected_value=record['overtime_hours'],
                                expected_range={'max': overtime_mean + 2 * overtime_std},
                                metadata={'z_score': float(z_scores[record.name])},
                            ))
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Overtime anomaly detection failed: {e}")
            return anomalies
    
    async def _detect_deduction_anomalies(self, df: pd.DataFrame) -> List[PayrollAnomaly]:
        """Detect deduction-related anomalies."""
        anomalies = []
        
        try:
            deduction_columns = [col for col in df.columns if 'deduction' in col.lower()]
            
            for col in deduction_columns:
                deduction_data = df[col].dropna()
                if len(deduction_data) == 0:
                    continue
                
                # Negative deductions (should not happen)
                negative_deductions = df[df[col] < 0]
                for _, record in negative_deductions.iterrows():
                    anomalies.append(PayrollAnomaly(
                        employee_id=record.get('employee_id', 'unknown'),
                        anomaly_type='negative_deduction',
                        description=f'Negative deduction in {col}: {record[col]}',
                        severity='high',
                        confidence=1.0,
                        detected_value=record[col],
                        expected_range={'min': 0},
                        metadata={'deduction_type': col},
                    ))
                
                # Excessive deductions (>50% of salary)
                if 'salary' in df.columns:
                    excessive_deductions = df[df[col] > df['salary'] * 0.5]
                    for _, record in excessive_deductions.iterrows():
                        anomalies.append(PayrollAnomaly(
                            employee_id=record.get('employee_id', 'unknown'),
                            anomaly_type='excessive_deduction',
                            description=f'Deduction {col} ({record[col]}) exceeds 50% of salary',
                            severity='high',
                            confidence=0.9,
                            detected_value=record[col],
                            expected_range={'max': record['salary'] * 0.5},
                            metadata={'deduction_type': col, 'percentage': float(record[col] / record['salary'] * 100)},
                        ))
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Deduction anomaly detection failed: {e}")
            return anomalies
    
    async def _detect_frequency_anomalies(self, df: pd.DataFrame) -> List[PayrollAnomaly]:
        """Detect frequency-based anomalies."""
        anomalies = []
        
        try:
            if 'employee_id' not in df.columns or 'pay_date' not in df.columns:
                return anomalies
            
            # Convert pay_date to datetime if it's not already
            df['pay_date'] = pd.to_datetime(df['pay_date'])
            
            # Check for duplicate payments
            duplicates = df.groupby(['employee_id', 'pay_date']).size()
            duplicate_payments = duplicates[duplicates > 1]
            
            for (employee_id, pay_date), count in duplicate_payments.items():
                anomalies.append(PayrollAnomaly(
                    employee_id=employee_id,
                    anomaly_type='duplicate_payment',
                    description=f'Multiple payments ({count}) on same date: {pay_date.date()}',
                    severity='high',
                    confidence=1.0,
                    detected_value=count,
                    expected_range={'max': 1},
                    metadata={'pay_date': pay_date.isoformat(), 'count': int(count)},
                ))
            
            # Check for unusual payment frequencies
            employee_frequencies = df.groupby('employee_id')['pay_date'].apply(
                lambda x: (x.max() - x.min()).days / len(x) if len(x) > 1 else None
            ).dropna()
            
            # Standard payroll frequencies: weekly (7), bi-weekly (14), monthly (30)
            standard_frequencies = [7, 14, 30]
            
            for employee_id, avg_frequency in employee_frequencies.items():
                # Check if frequency is close to any standard frequency
                min_diff = min(abs(avg_frequency - freq) for freq in standard_frequencies)
                
                if min_diff > 5:  # More than 5 days off from standard
                    anomalies.append(PayrollAnomaly(
                        employee_id=employee_id,
                        anomaly_type='irregular_frequency',
                        description=f'Irregular payment frequency: {avg_frequency:.1f} days average',
                        severity='medium',
                        confidence=0.6,
                        detected_value=avg_frequency,
                        expected_range={'values': standard_frequencies},
                        metadata={'average_frequency': float(avg_frequency)},
                    ))
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Frequency anomaly detection failed: {e}")
            return anomalies
    
    async def _detect_pattern_anomalies(self, df: pd.DataFrame) -> List[PayrollAnomaly]:
        """Detect pattern-based anomalies using machine learning."""
        anomalies = []
        
        try:
            # Prepare features for ML-based detection
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            if len(numeric_columns) < 2:
                return anomalies
            
            # Create feature matrix
            features = df[numeric_columns].fillna(0)
            
            if len(features) < 10:  # Need minimum data for ML
                return anomalies
            
            # Scale features
            scaled_features = self.scaler.fit_transform(features)
            
            # Isolation Forest detection
            outlier_predictions = self.isolation_forest.fit_predict(scaled_features)
            outlier_scores = self.isolation_forest.decision_function(scaled_features)
            
            # Find outliers (prediction = -1)
            outlier_indices = np.where(outlier_predictions == -1)[0]
            
            for idx in outlier_indices:
                record = df.iloc[idx]
                anomalies.append(PayrollAnomaly(
                    employee_id=record.get('employee_id', 'unknown'),
                    anomaly_type='pattern_anomaly',
                    description='Unusual pattern detected in payroll data',
                    severity=self._calculate_severity(abs(outlier_scores[idx]), 'outlier_score'),
                    confidence=min(abs(outlier_scores[idx]) * 2, 1.0),
                    detected_value='pattern',
                    metadata={
                        'outlier_score': float(outlier_scores[idx]),
                        'features_analyzed': list(numeric_columns),
                    },
                ))
            
            return anomalies
            
        except Exception as e:
            logger.error(f"Pattern anomaly detection failed: {e}")
            return anomalies
    
    async def _assess_fraud_risk(self, df: pd.DataFrame, anomalies: List[PayrollAnomaly]) -> FraudRiskAssessment:
        """Assess fraud risk based on detected anomalies."""
        try:
            # Calculate risk scores
            total_anomalies = len(anomalies)
            high_severity_count = len([a for a in anomalies if a.severity == 'high'])
            
            # Base risk score
            if total_anomalies == 0:
                risk_score = 0.0
            else:
                risk_score = min((high_severity_count * 0.3 + total_anomalies * 0.1), 1.0)
            
            # Risk factors
            risk_factors = []
            if high_severity_count > 0:
                risk_factors.append(f'{high_severity_count} high-severity anomalies detected')
            
            # Check for fraud patterns
            fraud_indicators = await self._check_fraud_patterns(df, anomalies)
            risk_factors.extend(fraud_indicators)
            
            # Determine risk level
            if risk_score < 0.3:
                risk_level = 'low'
            elif risk_score < 0.6:
                risk_level = 'medium'
            elif risk_score < 0.8:
                risk_level = 'high'
            else:
                risk_level = 'critical'
            
            # Generate recommendations
            recommendations = await self._generate_fraud_recommendations(risk_level, anomalies)
            
            return FraudRiskAssessment(
                overall_risk_score=risk_score,
                risk_level=risk_level,
                risk_factors=risk_factors,
                fraud_indicators=fraud_indicators,
                recommendations=recommendations,
                assessed_at=datetime.utcnow(),
            )
            
        except Exception as e:
            logger.error(f"Fraud risk assessment failed: {e}")
            return FraudRiskAssessment(
                overall_risk_score=0.0,
                risk_level='unknown',
                risk_factors=[],
                fraud_indicators=[],
                recommendations=[],
                assessed_at=datetime.utcnow(),
            )
    
    async def _check_compliance(self, df: pd.DataFrame) -> List[ComplianceAlert]:
        """Check for compliance violations."""
        alerts = []
        
        try:
            # Minimum wage compliance
            if 'salary' in df.columns and 'hours_worked' in df.columns:
                df['hourly_rate'] = df['salary'] / df['hours_worked']
                minimum_wage = 15.0  # Example minimum wage
                
                below_minimum = df[df['hourly_rate'] < minimum_wage]
                for _, record in below_minimum.iterrows():
                    alerts.append(ComplianceAlert(
                        alert_type='minimum_wage_violation',
                        description=f'Hourly rate ${record["hourly_rate"]:.2f} below minimum wage ${minimum_wage}',
                        severity='high',
                        employee_id=record.get('employee_id', 'unknown'),
                        regulation='Fair Labor Standards Act',
                        recommended_action='Adjust salary to meet minimum wage requirements',
                    ))
            
            # Overtime compliance
            if 'overtime_hours' in df.columns:
                excessive_overtime = df[df['overtime_hours'] > 60]  # Example limit
                for _, record in excessive_overtime.iterrows():
                    alerts.append(ComplianceAlert(
                        alert_type='overtime_limit_violation',
                        description=f'Overtime hours ({record["overtime_hours"]}) exceed regulatory limits',
                        severity='medium',
                        employee_id=record.get('employee_id', 'unknown'),
                        regulation='Labor Standards',
                        recommended_action='Review overtime authorization and limits',
                    ))
            
            return alerts
            
        except Exception as e:
            logger.error(f"Compliance check failed: {e}")
            return alerts
    
    def _calculate_severity(self, value: float, metric_type: str) -> str:
        """Calculate severity based on metric value."""
        if metric_type == 'z_score':
            if value > 4:
                return 'high'
            elif value > 3:
                return 'medium'
            else:
                return 'low'
        elif metric_type == 'outlier_score':
            if value > 0.5:
                return 'high'
            elif value > 0.2:
                return 'medium'
            else:
                return 'low'
        else:
            return 'medium'  # Default
    
    async def _check_fraud_patterns(self, df: pd.DataFrame, anomalies: List[PayrollAnomaly]) -> List[str]:
        """Check for known fraud patterns."""
        indicators = []
        
        # Pattern 1: Round number salaries (potential manual manipulation)
        if 'salary' in df.columns:
            round_salaries = df[df['salary'] % 1000 == 0]
            if len(round_salaries) > len(df) * 0.5:  # More than 50% are round numbers
                indicators.append('High percentage of round-number salaries')
        
        # Pattern 2: Duplicate bank accounts
        if 'bank_account' in df.columns:
            duplicate_accounts = df.groupby('bank_account').size()
            if (duplicate_accounts > 1).any():
                indicators.append('Multiple employees with same bank account')
        
        # Pattern 3: Unusual timing patterns
        if 'pay_date' in df.columns:
            df['pay_date'] = pd.to_datetime(df['pay_date'])
            weekend_payments = df[df['pay_date'].dt.weekday >= 5]  # Saturday/Sunday
            if len(weekend_payments) > 0:
                indicators.append('Payments processed on weekends')
        
        return indicators
    
    async def _generate_fraud_recommendations(self, risk_level: str, anomalies: List[PayrollAnomaly]) -> List[str]:
        """Generate fraud prevention recommendations."""
        recommendations = []
        
        if risk_level in ['high', 'critical']:
            recommendations.extend([
                'Conduct immediate manual review of flagged transactions',
                'Implement additional approval workflows for high-risk payments',
                'Review access controls and segregation of duties',
            ])
        
        if risk_level in ['medium', 'high', 'critical']:
            recommendations.extend([
                'Increase monitoring frequency for anomaly detection',
                'Review and update fraud detection rules',
                'Conduct periodic audit of payroll processes',
            ])
        
        # Specific recommendations based on anomaly types
        anomaly_types = [a.anomaly_type for a in anomalies]
        
        if 'duplicate_payment' in anomaly_types:
            recommendations.append('Implement duplicate payment prevention controls')
        
        if 'excessive_overtime' in anomaly_types:
            recommendations.append('Review overtime approval processes')
        
        if 'salary_spike' in anomaly_types:
            recommendations.append('Require additional approval for salary changes >50%')
        
        return recommendations
    
    async def _load_fraud_patterns(self):
        """Load known fraud patterns."""
        self.fraud_patterns = {
            'round_numbers': {'threshold': 0.5, 'severity': 'medium'},
            'duplicate_accounts': {'threshold': 1, 'severity': 'high'},
            'weekend_processing': {'threshold': 0, 'severity': 'medium'},
        }
    
    async def _load_compliance_rules(self):
        """Load compliance rules."""
        self.compliance_rules = {
            'minimum_wage': {'value': 15.0, 'regulation': 'FLSA'},
            'overtime_limit': {'value': 60, 'regulation': 'Labor Standards'},
            'maximum_deduction': {'value': 0.5, 'regulation': 'Wage Protection'},
        }
