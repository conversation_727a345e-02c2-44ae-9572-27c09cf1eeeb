import {
  Entity,
  Column,
  Index,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { BaseEntity } from './base.entity';
import { UserPermission } from './user-permission.entity';

export enum PermissionType {
  SYSTEM = 'system',
  RESOURCE = 'resource',
  FEATURE = 'feature',
  DATA = 'data',
}

export enum PermissionScope {
  GLOBAL = 'global',
  TENANT = 'tenant',
  DEPARTMENT = 'department',
  TEAM = 'team',
  PERSONAL = 'personal',
}

@Entity('permissions')
@Index(['code'], { unique: true })
@Index(['category', 'type'])
export class Permission extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 100,
    unique: true,
    comment: 'Unique permission code',
  })
  code: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Human-readable permission name',
  })
  name: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Description of what this permission allows',
  })
  description?: string;

  @Column({
    type: 'varchar',
    length: 100,
    comment: 'Permission category (e.g., user_management, payroll)',
  })
  @Index()
  category: string;

  @Column({
    type: 'enum',
    enum: PermissionType,
    default: PermissionType.FEATURE,
    comment: 'Type of permission',
  })
  @Index()
  type: PermissionType;

  @Column({
    type: 'enum',
    enum: PermissionScope,
    default: PermissionScope.TENANT,
    comment: 'Scope of the permission',
  })
  scope: PermissionScope;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Resource this permission applies to',
  })
  resource?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Action this permission allows (create, read, update, delete)',
  })
  action?: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Parent permission for hierarchical permissions',
  })
  parentId?: string;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this permission is active',
  })
  isActive: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this is a system-level permission',
  })
  isSystem: boolean;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Display order for UI',
  })
  sortOrder: number;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Additional permission metadata',
  })
  metadata?: Record<string, any>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Conditions that must be met for this permission',
  })
  conditions?: Record<string, any>;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Icon for UI display',
  })
  icon?: string;

  @Column({
    type: 'varchar',
    length: 7,
    nullable: true,
    comment: 'Color for UI display',
  })
  color?: string;

  // Relationships
  @ManyToOne(() => Permission, { nullable: true })
  @JoinColumn({ name: 'parent_id' })
  parent?: Permission;

  @OneToMany(() => Permission, permission => permission.parent)
  children: Permission[];

  @OneToMany(() => UserPermission, userPermission => userPermission.permission)
  userPermissions: UserPermission[];

  // Virtual properties
  get fullCode(): string {
    return this.parent ? `${this.parent.code}.${this.code}` : this.code;
  }

  get isLeaf(): boolean {
    return !this.children || this.children.length === 0;
  }

  get level(): number {
    let level = 0;
    let current = this.parent;
    while (current) {
      level++;
      current = current.parent;
    }
    return level;
  }
}
