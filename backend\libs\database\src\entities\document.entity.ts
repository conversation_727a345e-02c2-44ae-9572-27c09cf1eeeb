import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';
import { Employee } from './employee.entity';
import { DocumentVersion } from './document-version.entity';
import { DocumentAccess } from './document-access.entity';

export enum DocumentType {
  POLICY = 'policy',
  PROCEDURE = 'procedure',
  FORM = 'form',
  TEMPLATE = 'template',
  HANDBOOK = 'handbook',
  TRAINING_MATERIAL = 'training_material',
  COMPLIANCE = 'compliance',
  CONTRACT = 'contract',
  REPORT = 'report',
  OTHER = 'other',
}

export enum DocumentStatus {
  DRAFT = 'draft',
  UNDER_REVIEW = 'under_review',
  APPROVED = 'approved',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
  EXPIRED = 'expired',
}

@Entity('documents')
@Index(['tenantId', 'documentType'])
@Index(['tenantId', 'status'])
@Index(['tenantId', 'ownerId'])
export class Document {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: DocumentType,
    name: 'document_type',
  })
  documentType: DocumentType;

  @Column({
    type: 'enum',
    enum: DocumentStatus,
    default: DocumentStatus.DRAFT,
  })
  status: DocumentStatus;

  @Column({ name: 'owner_id' })
  ownerId: string;

  @ManyToOne(() => Employee, { eager: true })
  @JoinColumn({ name: 'owner_id' })
  owner: Employee;

  @Column({ length: 500, name: 'file_path' })
  filePath: string;

  @Column({ length: 255, name: 'file_name' })
  fileName: string;

  @Column({ length: 100, name: 'file_type' })
  fileType: string;

  @Column({ type: 'bigint', name: 'file_size' })
  fileSize: number;

  @Column({ length: 64, name: 'file_hash' })
  fileHash: string;

  @Column({ type: 'decimal', precision: 3, scale: 1, default: 1.0 })
  version: number;

  @Column({ type: 'boolean', default: false, name: 'is_confidential' })
  isConfidential: boolean;

  @Column({ type: 'boolean', default: false, name: 'requires_acknowledgment' })
  requiresAcknowledgment: boolean;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @Column({ type: 'json', nullable: true })
  categories: string[];

  @Column({ name: 'published_date', type: 'timestamp', nullable: true })
  publishedDate: Date;

  @Column({ name: 'expiry_date', type: 'date', nullable: true })
  expiryDate: Date;

  @Column({ name: 'review_date', type: 'date', nullable: true })
  reviewDate: Date;

  @Column({ name: 'approved_by', nullable: true })
  approvedBy: string;

  @ManyToOne(() => Employee, { nullable: true })
  @JoinColumn({ name: 'approved_by' })
  approver: Employee;

  @Column({ name: 'approved_at', type: 'timestamp', nullable: true })
  approvedAt: Date;

  @Column({ type: 'json', nullable: true, name: 'approval_workflow' })
  approvalWorkflow: Array<{
    stepId: string;
    stepName: string;
    approverId: string;
    approverName: string;
    status: 'pending' | 'approved' | 'rejected';
    approvedAt?: string;
    comments?: string;
  }>;

  @Column({ type: 'json', nullable: true, name: 'access_permissions' })
  accessPermissions: {
    viewRoles?: string[];
    editRoles?: string[];
    downloadRoles?: string[];
    departments?: string[];
    positions?: string[];
    specificEmployees?: string[];
  };

  @Column({ type: 'json', nullable: true, name: 'compliance_info' })
  complianceInfo: {
    regulatoryRequirement?: string;
    complianceStandard?: string;
    retentionPeriod?: number; // in years
    disposalMethod?: string;
    auditRequired?: boolean;
  };

  @Column({ type: 'json', nullable: true })
  metadata: {
    language?: string;
    keywords?: string[];
    relatedDocuments?: string[];
    customFields?: Record<string, any>;
  };

  @OneToMany(() => DocumentVersion, version => version.document)
  versions: DocumentVersion[];

  @OneToMany(() => DocumentAccess, access => access.document)
  accessLogs: DocumentAccess[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater: User;

  // Computed properties
  get isExpired(): boolean {
    return this.expiryDate && this.expiryDate < new Date();
  }

  get needsReview(): boolean {
    return this.reviewDate && this.reviewDate < new Date();
  }

  get fileSizeFormatted(): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = this.fileSize;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }
}
