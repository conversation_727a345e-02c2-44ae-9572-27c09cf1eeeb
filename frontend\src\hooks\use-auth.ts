import { useEffect, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { 
  loginUser, 
  logoutUser, 
  refreshUserToken, 
  clearError, 
  updateLastActivity,
  setUser,
  clearAuth,
  type User 
} from '@/store/slices/auth-slice';

// Session timeout in milliseconds (30 minutes)
const SESSION_TIMEOUT = 30 * 60 * 1000;

// Check session every 5 minutes
const SESSION_CHECK_INTERVAL = 5 * 60 * 1000;

export const useAuth = () => {
  const dispatch = useAppDispatch();
  const { 
    user, 
    token, 
    refreshToken, 
    isAuthenticated, 
    isLoading, 
    error, 
    lastActivity 
  } = useAppSelector((state) => state.auth);

  // Login function
  const login = useCallback(async (email: string, password: string) => {
    try {
      const result = await dispatch(loginUser({ email, password }));
      return result.type === 'auth/login/fulfilled';
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  }, [dispatch]);

  // Logout function
  const logout = useCallback(async () => {
    try {
      await dispatch(logoutUser());
      // Clear Apollo cache if needed
      if (window.apolloClient) {
        window.apolloClient.clearStore();
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  }, [dispatch]);

  // Refresh token function
  const refreshUserTokens = useCallback(async () => {
    try {
      const result = await dispatch(refreshUserToken());
      return result.type === 'auth/refreshToken/fulfilled';
    } catch (error) {
      console.error('Token refresh error:', error);
      return false;
    }
  }, [dispatch]);

  // Clear error function
  const clearAuthError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Update user activity
  const updateActivity = useCallback(() => {
    dispatch(updateLastActivity());
  }, [dispatch]);

  // Check if user has specific permission
  const hasPermission = useCallback((permission: string) => {
    if (!user || !user.permissions) return false;
    return user.permissions.includes(permission) || user.permissions.includes('*');
  }, [user]);

  // Check if user has any of the specified roles
  const hasRole = useCallback((roles: string | string[]) => {
    if (!user) return false;
    const roleArray = Array.isArray(roles) ? roles : [roles];
    return roleArray.includes(user.role);
  }, [user]);

  // Check if user is admin
  const isAdmin = useCallback(() => {
    return hasRole(['admin', 'superadmin']);
  }, [hasRole]);

  // Check if session is expired
  const isSessionExpired = useCallback(() => {
    if (!lastActivity) return false;
    return Date.now() - lastActivity > SESSION_TIMEOUT;
  }, [lastActivity]);

  // Initialize auth state from localStorage
  useEffect(() => {
    const initializeAuth = async () => {
      const storedToken = localStorage.getItem('token');
      const storedRefreshToken = localStorage.getItem('refreshToken');

      if (storedToken && storedRefreshToken) {
        try {
          // Verify token by fetching user profile
          const response = await fetch('/api/auth/profile', {
            headers: {
              'Authorization': `Bearer ${storedToken}`,
            },
          });

          if (response.ok) {
            const userData = await response.json();
            dispatch(setUser(userData));
          } else if (response.status === 401) {
            // Token expired, try to refresh
            const refreshSuccess = await refreshUserTokens();
            if (!refreshSuccess) {
              dispatch(clearAuth());
            }
          } else {
            // Other error, clear auth
            dispatch(clearAuth());
          }
        } catch (error) {
          console.error('Auth initialization error:', error);
          dispatch(clearAuth());
        }
      }
    };

    initializeAuth();
  }, [dispatch, refreshUserTokens]);

  // Set up session monitoring
  useEffect(() => {
    if (!isAuthenticated) return;

    const checkSession = () => {
      if (isSessionExpired()) {
        console.log('Session expired, logging out...');
        logout();
        return;
      }

      // Try to refresh token if it's close to expiring
      const timeUntilExpiry = SESSION_TIMEOUT - (Date.now() - lastActivity);
      if (timeUntilExpiry < SESSION_CHECK_INTERVAL) {
        refreshUserTokens();
      }
    };

    // Check session immediately and then at intervals
    checkSession();
    const interval = setInterval(checkSession, SESSION_CHECK_INTERVAL);

    return () => clearInterval(interval);
  }, [isAuthenticated, lastActivity, isSessionExpired, logout, refreshUserTokens]);

  // Set up activity tracking
  useEffect(() => {
    if (!isAuthenticated) return;

    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    const handleActivity = () => {
      updateActivity();
    };

    // Throttle activity updates to avoid excessive dispatches
    let lastUpdate = 0;
    const throttledHandleActivity = () => {
      const now = Date.now();
      if (now - lastUpdate > 60000) { // Update at most once per minute
        lastUpdate = now;
        handleActivity();
      }
    };

    activityEvents.forEach(event => {
      document.addEventListener(event, throttledHandleActivity, true);
    });

    return () => {
      activityEvents.forEach(event => {
        document.removeEventListener(event, throttledHandleActivity, true);
      });
    };
  }, [isAuthenticated, updateActivity]);

  return {
    // State
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    
    // Actions
    login,
    logout,
    refreshToken: refreshUserTokens,
    clearError: clearAuthError,
    
    // Utilities
    hasPermission,
    hasRole,
    isAdmin,
    isSessionExpired,
    updateActivity,
  };
};
