import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  ManyToOne,
  Join<PERSON><PERSON><PERSON>n,
  CreateDateColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';
import { Document } from './document.entity';

@Entity('document_versions')
@Index(['tenantId', 'documentId'])
@Index(['tenantId', 'version'])
export class DocumentVersion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ name: 'document_id' })
  documentId: string;

  @ManyToOne(() => Document, document => document.versions)
  @JoinColumn({ name: 'document_id' })
  document: Document;

  @Column({ type: 'decimal', precision: 3, scale: 1 })
  version: number;

  @Column({ length: 500, name: 'file_path' })
  filePath: string;

  @Column({ length: 255, name: 'file_name' })
  fileName: string;

  @Column({ type: 'bigint', name: 'file_size' })
  fileSize: number;

  @Column({ length: 64, name: 'file_hash' })
  fileHash: string;

  @Column({ type: 'text', nullable: true, name: 'change_notes' })
  changeNotes: string;

  @Column({ type: 'json', nullable: true, name: 'changes_summary' })
  changesSummary: Array<{
    type: 'added' | 'modified' | 'deleted';
    section: string;
    description: string;
  }>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;
}
