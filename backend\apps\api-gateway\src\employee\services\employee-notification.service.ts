import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConfigService } from '@nestjs/config';

import { Employee } from '@app/database';

@Injectable()
export class EmployeeNotificationService {
  private readonly logger = new Logger(EmployeeNotificationService.name);

  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Send welcome notification to new employee
   */
  async sendWelcomeNotification(employee: Employee): Promise<void> {
    try {
      this.eventEmitter.emit('notification.send', {
        type: 'employee_welcome',
        recipientId: employee.id,
        recipientEmail: employee.email,
        tenantId: employee.tenantId,
        data: {
          employeeName: `${employee.firstName} ${employee.lastName}`,
          employeeId: employee.employeeId,
          startDate: employee.dateOfJoining,
          department: employee.department?.name,
          position: employee.position?.title,
        },
        priority: 'high',
      });

      this.logger.log(`Welcome notification sent to employee ${employee.employeeId}`);
    } catch (error) {
      this.logger.error(`Failed to send welcome notification to employee ${employee.employeeId}`, error);
    }
  }

  /**
   * Send profile update notification
   */
  async sendProfileUpdateNotification(
    employee: Employee,
    changes: Record<string, any>,
    updatedBy: string,
  ): Promise<void> {
    try {
      this.eventEmitter.emit('notification.send', {
        type: 'employee_profile_updated',
        recipientId: employee.id,
        recipientEmail: employee.email,
        tenantId: employee.tenantId,
        data: {
          employeeName: `${employee.firstName} ${employee.lastName}`,
          employeeId: employee.employeeId,
          changes,
          updatedBy,
          updatedAt: new Date(),
        },
        priority: 'medium',
      });

      this.logger.log(`Profile update notification sent to employee ${employee.employeeId}`);
    } catch (error) {
      this.logger.error(`Failed to send profile update notification to employee ${employee.employeeId}`, error);
    }
  }

  /**
   * Send termination notification
   */
  async sendTerminationNotification(
    employee: Employee,
    reason?: string,
    lastWorkingDay?: Date,
  ): Promise<void> {
    try {
      this.eventEmitter.emit('notification.send', {
        type: 'employee_termination',
        recipientId: employee.id,
        recipientEmail: employee.email,
        tenantId: employee.tenantId,
        data: {
          employeeName: `${employee.firstName} ${employee.lastName}`,
          employeeId: employee.employeeId,
          reason,
          lastWorkingDay,
          terminationDate: new Date(),
        },
        priority: 'high',
      });

      // Also notify HR and manager
      if (employee.managerId) {
        this.eventEmitter.emit('notification.send', {
          type: 'employee_termination_manager',
          recipientId: employee.managerId,
          tenantId: employee.tenantId,
          data: {
            employeeName: `${employee.firstName} ${employee.lastName}`,
            employeeId: employee.employeeId,
            reason,
            lastWorkingDay,
            terminationDate: new Date(),
          },
          priority: 'high',
        });
      }

      this.logger.log(`Termination notification sent for employee ${employee.employeeId}`);
    } catch (error) {
      this.logger.error(`Failed to send termination notification for employee ${employee.employeeId}`, error);
    }
  }

  /**
   * Send probation period reminder
   */
  async sendProbationReminder(employee: Employee, daysRemaining: number): Promise<void> {
    try {
      this.eventEmitter.emit('notification.send', {
        type: 'probation_reminder',
        recipientId: employee.id,
        recipientEmail: employee.email,
        tenantId: employee.tenantId,
        data: {
          employeeName: `${employee.firstName} ${employee.lastName}`,
          employeeId: employee.employeeId,
          daysRemaining,
          probationEndDate: employee.probationEndDate,
        },
        priority: 'medium',
      });

      // Also notify manager and HR
      if (employee.managerId) {
        this.eventEmitter.emit('notification.send', {
          type: 'probation_reminder_manager',
          recipientId: employee.managerId,
          tenantId: employee.tenantId,
          data: {
            employeeName: `${employee.firstName} ${employee.lastName}`,
            employeeId: employee.employeeId,
            daysRemaining,
            probationEndDate: employee.probationEndDate,
          },
          priority: 'medium',
        });
      }

      this.logger.log(`Probation reminder sent for employee ${employee.employeeId}`);
    } catch (error) {
      this.logger.error(`Failed to send probation reminder for employee ${employee.employeeId}`, error);
    }
  }

  /**
   * Send birthday notification
   */
  async sendBirthdayNotification(employee: Employee): Promise<void> {
    try {
      this.eventEmitter.emit('notification.send', {
        type: 'employee_birthday',
        recipientId: employee.id,
        recipientEmail: employee.email,
        tenantId: employee.tenantId,
        data: {
          employeeName: `${employee.firstName} ${employee.lastName}`,
          employeeId: employee.employeeId,
          birthday: employee.dateOfBirth,
        },
        priority: 'low',
      });

      // Also notify team members and manager
      if (employee.managerId) {
        this.eventEmitter.emit('notification.send', {
          type: 'employee_birthday_team',
          recipientId: employee.managerId,
          tenantId: employee.tenantId,
          data: {
            employeeName: `${employee.firstName} ${employee.lastName}`,
            employeeId: employee.employeeId,
            birthday: employee.dateOfBirth,
          },
          priority: 'low',
        });
      }

      this.logger.log(`Birthday notification sent for employee ${employee.employeeId}`);
    } catch (error) {
      this.logger.error(`Failed to send birthday notification for employee ${employee.employeeId}`, error);
    }
  }

  /**
   * Send work anniversary notification
   */
  async sendWorkAnniversaryNotification(employee: Employee, yearsOfService: number): Promise<void> {
    try {
      this.eventEmitter.emit('notification.send', {
        type: 'work_anniversary',
        recipientId: employee.id,
        recipientEmail: employee.email,
        tenantId: employee.tenantId,
        data: {
          employeeName: `${employee.firstName} ${employee.lastName}`,
          employeeId: employee.employeeId,
          yearsOfService,
          joiningDate: employee.dateOfJoining,
          anniversaryDate: new Date(),
        },
        priority: 'medium',
      });

      // Also notify manager and HR
      if (employee.managerId) {
        this.eventEmitter.emit('notification.send', {
          type: 'work_anniversary_manager',
          recipientId: employee.managerId,
          tenantId: employee.tenantId,
          data: {
            employeeName: `${employee.firstName} ${employee.lastName}`,
            employeeId: employee.employeeId,
            yearsOfService,
            joiningDate: employee.dateOfJoining,
            anniversaryDate: new Date(),
          },
          priority: 'medium',
        });
      }

      this.logger.log(`Work anniversary notification sent for employee ${employee.employeeId}`);
    } catch (error) {
      this.logger.error(`Failed to send work anniversary notification for employee ${employee.employeeId}`, error);
    }
  }

  /**
   * Send document expiry reminder
   */
  async sendDocumentExpiryReminder(
    employee: Employee,
    documentType: string,
    expiryDate: Date,
    daysUntilExpiry: number,
  ): Promise<void> {
    try {
      this.eventEmitter.emit('notification.send', {
        type: 'document_expiry_reminder',
        recipientId: employee.id,
        recipientEmail: employee.email,
        tenantId: employee.tenantId,
        data: {
          employeeName: `${employee.firstName} ${employee.lastName}`,
          employeeId: employee.employeeId,
          documentType,
          expiryDate,
          daysUntilExpiry,
        },
        priority: daysUntilExpiry <= 7 ? 'high' : 'medium',
      });

      // Also notify HR
      this.eventEmitter.emit('notification.send', {
        type: 'document_expiry_reminder_hr',
        tenantId: employee.tenantId,
        data: {
          employeeName: `${employee.firstName} ${employee.lastName}`,
          employeeId: employee.employeeId,
          documentType,
          expiryDate,
          daysUntilExpiry,
        },
        priority: daysUntilExpiry <= 7 ? 'high' : 'medium',
      });

      this.logger.log(`Document expiry reminder sent for employee ${employee.employeeId}`);
    } catch (error) {
      this.logger.error(`Failed to send document expiry reminder for employee ${employee.employeeId}`, error);
    }
  }

  /**
   * Send bulk action notification
   */
  async sendBulkActionNotification(
    employees: Employee[],
    action: string,
    performedBy: string,
    reason?: string,
  ): Promise<void> {
    try {
      for (const employee of employees) {
        this.eventEmitter.emit('notification.send', {
          type: `bulk_action_${action}`,
          recipientId: employee.id,
          recipientEmail: employee.email,
          tenantId: employee.tenantId,
          data: {
            employeeName: `${employee.firstName} ${employee.lastName}`,
            employeeId: employee.employeeId,
            action,
            performedBy,
            reason,
            actionDate: new Date(),
          },
          priority: 'medium',
        });
      }

      this.logger.log(`Bulk action notifications sent for ${employees.length} employees`);
    } catch (error) {
      this.logger.error(`Failed to send bulk action notifications`, error);
    }
  }
}
