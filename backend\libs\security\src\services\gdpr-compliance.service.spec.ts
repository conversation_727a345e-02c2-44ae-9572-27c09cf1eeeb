import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { GDPRComplianceService } from './gdpr-compliance.service';
import { EncryptionService } from './encryption.service';
import { AuditService } from './audit.service';
import { User } from '@app/database/entities/user.entity';
import { Employee } from '@app/database/entities/employee.entity';

describe('GDPRComplianceService', () => {
  let service: GDPRComplianceService;
  let userRepository: Repository<User>;
  let employeeRepository: Repository<Employee>;
  let encryptionService: EncryptionService;
  let auditService: AuditService;
  let eventEmitter: EventEmitter2;

  const mockUserRepository = {
    findOne: jest.fn(),
    find: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      getOne: jest.fn(),
    })),
  };

  const mockEmployeeRepository = {
    findOne: jest.fn(),
    find: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
      getOne: jest.fn(),
    })),
  };

  const mockEncryptionService = {
    decryptObject: jest.fn(),
    encryptObject: jest.fn(),
    isPIIField: jest.fn(),
  };

  const mockAuditService = {
    log: jest.fn(),
  };

  const mockEventEmitter = {
    emit: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config = {
        GDPR_ENABLED: 'true',
        AUDIT_LOG_RETENTION_DAYS: '2555',
      };
      return config[key];
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GDPRComplianceService,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: getRepositoryToken(Employee),
          useValue: mockEmployeeRepository,
        },
        {
          provide: EncryptionService,
          useValue: mockEncryptionService,
        },
        {
          provide: AuditService,
          useValue: mockAuditService,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<GDPRComplianceService>(GDPRComplianceService);
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    employeeRepository = module.get<Repository<Employee>>(getRepositoryToken(Employee));
    encryptionService = module.get<EncryptionService>(EncryptionService);
    auditService = module.get<AuditService>(AuditService);
    eventEmitter = module.get<EventEmitter2>(EventEmitter2);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Data Access Request (Article 15)', () => {
    const testSubjectId = 'user-123';
    const testRequesterId = 'admin-456';
    const testTenantId = 'tenant-789';

    const mockUser = {
      id: testSubjectId,
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      tenantId: testTenantId,
    };

    const mockEmployee = {
      id: 'emp-123',
      userId: testSubjectId,
      employeeId: 'EMP001',
      department: 'Engineering',
      position: 'Developer',
      salary: 75000,
    };

    it('should handle data access request successfully', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockEmployeeRepository.find.mockResolvedValue([mockEmployee]);
      mockEncryptionService.decryptObject.mockImplementation((obj) => obj);

      const result = await service.handleAccessRequest(
        testSubjectId,
        testRequesterId,
        testTenantId
      );

      expect(result).toBeDefined();
      expect(result.personalData).toBeDefined();
      expect(result.personalData.user).toEqual(mockUser);
      expect(result.personalData.employee).toEqual([mockEmployee]);
      expect(result.processingPurposes).toBeDefined();
      expect(result.dataCategories).toBeDefined();
      expect(result.retentionPeriods).toBeDefined();
      expect(result.rights).toBeDefined();

      expect(mockAuditService.log).toHaveBeenCalledWith(
        testRequesterId,
        'GDPR_ACCESS_REQUEST',
        'COMPLIANCE',
        expect.any(Object)
      );
    });

    it('should throw error if user not found', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);

      await expect(
        service.handleAccessRequest(testSubjectId, testRequesterId, testTenantId)
      ).rejects.toThrow('User not found');
    });

    it('should throw error if user belongs to different tenant', async () => {
      mockUserRepository.findOne.mockResolvedValue({
        ...mockUser,
        tenantId: 'different-tenant',
      });

      await expect(
        service.handleAccessRequest(testSubjectId, testRequesterId, testTenantId)
      ).rejects.toThrow('User not found in tenant');
    });

    it('should decrypt encrypted personal data', async () => {
      const encryptedUser = { ...mockUser, email: 'encrypted-email' };
      const decryptedUser = { ...mockUser, email: '<EMAIL>' };

      mockUserRepository.findOne.mockResolvedValue(encryptedUser);
      mockEmployeeRepository.find.mockResolvedValue([]);
      mockEncryptionService.decryptObject.mockResolvedValue(decryptedUser);

      const result = await service.handleAccessRequest(
        testSubjectId,
        testRequesterId,
        testTenantId
      );

      expect(mockEncryptionService.decryptObject).toHaveBeenCalledWith(
        encryptedUser,
        { tenantId: testTenantId }
      );
      expect(result.personalData.user).toEqual(decryptedUser);
    });
  });

  describe('Data Rectification Request (Article 16)', () => {
    const testSubjectId = 'user-123';
    const testRequesterId = 'admin-456';
    const testTenantId = 'tenant-789';

    const mockUser = {
      id: testSubjectId,
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      tenantId: testTenantId,
    };

    it('should handle rectification request successfully', async () => {
      const corrections = {
        email: '<EMAIL>',
        firstName: 'Jane',
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockEncryptionService.encryptObject.mockImplementation((obj) => obj);
      mockUserRepository.save.mockResolvedValue({ ...mockUser, ...corrections });

      await service.handleRectificationRequest(
        testSubjectId,
        corrections,
        testRequesterId,
        testTenantId
      );

      expect(mockUserRepository.save).toHaveBeenCalledWith({
        ...mockUser,
        ...corrections,
      });

      expect(mockAuditService.log).toHaveBeenCalledWith(
        testRequesterId,
        'GDPR_RECTIFICATION_REQUEST',
        'COMPLIANCE',
        expect.objectContaining({
          subjectId: testSubjectId,
          corrections,
        })
      );
    });

    it('should encrypt corrected PII data', async () => {
      const corrections = { email: '<EMAIL>' };
      const encryptedCorrections = { email: 'encrypted-new-email' };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockEncryptionService.encryptObject.mockResolvedValue({
        ...mockUser,
        ...encryptedCorrections,
      });
      mockUserRepository.save.mockResolvedValue(mockUser);

      await service.handleRectificationRequest(
        testSubjectId,
        corrections,
        testRequesterId,
        testTenantId
      );

      expect(mockEncryptionService.encryptObject).toHaveBeenCalledWith(
        { ...mockUser, ...corrections },
        testTenantId
      );
    });

    it('should throw error if user not found', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);

      await expect(
        service.handleRectificationRequest(
          testSubjectId,
          { email: '<EMAIL>' },
          testRequesterId,
          testTenantId
        )
      ).rejects.toThrow('User not found');
    });
  });

  describe('Data Erasure Request (Article 17)', () => {
    const testSubjectId = 'user-123';
    const testRequesterId = 'admin-456';
    const testTenantId = 'tenant-789';

    const mockUser = {
      id: testSubjectId,
      email: '<EMAIL>',
      tenantId: testTenantId,
    };

    it('should handle erasure request successfully', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockEmployeeRepository.find.mockResolvedValue([]);

      await service.handleErasureRequest(
        testSubjectId,
        testRequesterId,
        testTenantId,
        'User requested deletion'
      );

      expect(mockUserRepository.remove).toHaveBeenCalledWith(mockUser);
      expect(mockAuditService.log).toHaveBeenCalledWith(
        testRequesterId,
        'GDPR_ERASURE_REQUEST',
        'COMPLIANCE',
        expect.objectContaining({
          subjectId: testSubjectId,
          reason: 'User requested deletion',
        })
      );
    });

    it('should remove related employee records', async () => {
      const mockEmployee = { id: 'emp-123', userId: testSubjectId };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockEmployeeRepository.find.mockResolvedValue([mockEmployee]);

      await service.handleErasureRequest(
        testSubjectId,
        testRequesterId,
        testTenantId
      );

      expect(mockEmployeeRepository.remove).toHaveBeenCalledWith([mockEmployee]);
      expect(mockUserRepository.remove).toHaveBeenCalledWith(mockUser);
    });

    it('should emit data erasure event', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockEmployeeRepository.find.mockResolvedValue([]);

      await service.handleErasureRequest(
        testSubjectId,
        testRequesterId,
        testTenantId
      );

      expect(mockEventEmitter.emit).toHaveBeenCalledWith(
        'gdpr.data.erased',
        expect.objectContaining({
          subjectId: testSubjectId,
          tenantId: testTenantId,
        })
      );
    });
  });

  describe('Data Portability Request (Article 20)', () => {
    const testSubjectId = 'user-123';
    const testRequesterId = 'admin-456';
    const testTenantId = 'tenant-789';

    const mockUser = {
      id: testSubjectId,
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
    };

    it('should export data in JSON format', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockEmployeeRepository.find.mockResolvedValue([]);
      mockEncryptionService.decryptObject.mockImplementation((obj) => obj);

      const result = await service.handlePortabilityRequest(
        testSubjectId,
        testRequesterId,
        testTenantId,
        'json'
      );

      expect(result.format).toBe('json');
      expect(result.data).toBeDefined();
      expect(typeof result.data).toBe('object');
    });

    it('should export data in CSV format', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockEmployeeRepository.find.mockResolvedValue([]);
      mockEncryptionService.decryptObject.mockImplementation((obj) => obj);

      const result = await service.handlePortabilityRequest(
        testSubjectId,
        testRequesterId,
        testTenantId,
        'csv'
      );

      expect(result.format).toBe('csv');
      expect(typeof result.data).toBe('string');
      expect(result.data).toContain('email,firstName,lastName');
    });

    it('should export data in XML format', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockEmployeeRepository.find.mockResolvedValue([]);
      mockEncryptionService.decryptObject.mockImplementation((obj) => obj);

      const result = await service.handlePortabilityRequest(
        testSubjectId,
        testRequesterId,
        testTenantId,
        'xml'
      );

      expect(result.format).toBe('xml');
      expect(typeof result.data).toBe('string');
      expect(result.data).toContain('<?xml version="1.0"');
      expect(result.data).toContain('<personalData>');
    });
  });

  describe('Consent Management', () => {
    const testConsentData = {
      subjectId: 'user-123',
      tenantId: 'tenant-789',
      purpose: 'Employment management',
      legalBasis: 'contract',
      dataCategories: ['identity', 'contact'],
      retentionPeriod: 2555,
      consentGiven: true,
      consentDate: new Date(),
    };

    it('should record consent successfully', async () => {
      const mockSave = jest.fn().mockResolvedValue({ id: 'consent-123' });
      mockUserRepository.save = mockSave;

      const consentId = await service.recordConsent(testConsentData);

      expect(consentId).toBeDefined();
      expect(mockAuditService.log).toHaveBeenCalledWith(
        testConsentData.subjectId,
        'GDPR_CONSENT_RECORDED',
        'COMPLIANCE',
        expect.objectContaining({
          purpose: testConsentData.purpose,
          legalBasis: testConsentData.legalBasis,
        })
      );
    });

    it('should withdraw consent successfully', async () => {
      const consentId = 'consent-123';
      const mockConsent = {
        id: consentId,
        subjectId: 'user-123',
        consentGiven: true,
      };

      mockUserRepository.findOne.mockResolvedValue(mockConsent);
      mockUserRepository.save.mockResolvedValue({
        ...mockConsent,
        consentGiven: false,
        withdrawalDate: expect.any(Date),
      });

      await service.withdrawConsent(consentId, 'user-123');

      expect(mockUserRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          consentGiven: false,
          withdrawalDate: expect.any(Date),
        })
      );
    });
  });

  describe('Automated Data Retention', () => {
    it('should clean up expired data', async () => {
      const expiredDate = new Date();
      expiredDate.setDate(expiredDate.getDate() - 2556); // Older than retention period

      const expiredUsers = [
        { id: 'user-1', createdAt: expiredDate },
        { id: 'user-2', createdAt: expiredDate },
      ];

      mockUserRepository.createQueryBuilder().getMany.mockResolvedValue(expiredUsers);

      await service.cleanupExpiredData();

      expect(mockUserRepository.remove).toHaveBeenCalledWith(expiredUsers);
      expect(mockAuditService.log).toHaveBeenCalledWith(
        'system',
        'GDPR_DATA_CLEANUP',
        'COMPLIANCE',
        expect.objectContaining({
          deletedRecords: 2,
        })
      );
    });

    it('should respect legal hold requirements', async () => {
      const expiredDate = new Date();
      expiredDate.setDate(expiredDate.getDate() - 2556);

      const usersWithLegalHold = [
        { id: 'user-1', createdAt: expiredDate, legalHold: true },
        { id: 'user-2', createdAt: expiredDate, legalHold: false },
      ];

      mockUserRepository.createQueryBuilder().getMany.mockResolvedValue(usersWithLegalHold);

      await service.cleanupExpiredData();

      // Should only remove user without legal hold
      expect(mockUserRepository.remove).toHaveBeenCalledWith([
        { id: 'user-2', createdAt: expiredDate, legalHold: false },
      ]);
    });
  });

  describe('Compliance Reporting', () => {
    const testTenantId = 'tenant-789';

    it('should generate compliance report', async () => {
      // Mock data for report generation
      mockUserRepository.createQueryBuilder().getMany.mockResolvedValue([
        { id: 'user-1' }, { id: 'user-2' }
      ]);

      const report = await service.generateComplianceReport(testTenantId);

      expect(report).toBeDefined();
      expect(report.tenantId).toBe(testTenantId);
      expect(report.generatedAt).toBeInstanceOf(Date);
      expect(report.dataSubjects).toBeDefined();
      expect(report.requests).toBeDefined();
      expect(report.consents).toBeDefined();
      expect(report.retentionCompliance).toBeDefined();
      expect(report.complianceScore).toBeDefined();
    });

    it('should calculate compliance score correctly', async () => {
      mockUserRepository.createQueryBuilder().getMany.mockResolvedValue([]);

      const report = await service.generateComplianceReport(testTenantId);

      expect(report.complianceScore).toBeGreaterThanOrEqual(0);
      expect(report.complianceScore).toBeLessThanOrEqual(100);
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      mockUserRepository.findOne.mockRejectedValue(new Error('Database error'));

      await expect(
        service.handleAccessRequest('user-123', 'admin-456', 'tenant-789')
      ).rejects.toThrow('Database error');
    });

    it('should handle encryption errors gracefully', async () => {
      mockUserRepository.findOne.mockResolvedValue({ id: 'user-123' });
      mockEncryptionService.decryptObject.mockRejectedValue(new Error('Decryption failed'));

      await expect(
        service.handleAccessRequest('user-123', 'admin-456', 'tenant-789')
      ).rejects.toThrow('Decryption failed');
    });
  });
});
