import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  Index,
} from 'typeorm';
import { Tenant } from './tenant.entity';
import { Document } from './document.entity';
import { Employee } from './employee.entity';

export enum AccessType {
  VIEW = 'view',
  DOWNLOAD = 'download',
  EDIT = 'edit',
  DELETE = 'delete',
  SHARE = 'share',
}

@Entity('document_access')
@Index(['tenantId', 'documentId'])
@Index(['tenantId', 'employeeId'])
@Index(['tenantId', 'accessedAt'])
export class DocumentAccess {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ name: 'document_id' })
  documentId: string;

  @ManyToOne(() => Document, document => document.accessLogs)
  @JoinColumn({ name: 'document_id' })
  document: Document;

  @Column({ name: 'employee_id' })
  employeeId: string;

  @ManyToOne(() => Employee, { eager: true })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;

  @Column({
    type: 'enum',
    enum: AccessType,
    name: 'access_type',
  })
  accessType: AccessType;

  @Column({ name: 'accessed_at', type: 'timestamp' })
  accessedAt: Date;

  @Column({ length: 45, nullable: true, name: 'ip_address' })
  ipAddress: string;

  @Column({ length: 500, nullable: true, name: 'user_agent' })
  userAgent: string;

  @Column({ type: 'json', nullable: true })
  metadata: {
    sessionId?: string;
    deviceType?: string;
    location?: string;
    duration?: number; // in seconds
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;
}
