"""
Pydantic models for NLP query processing.
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field


class QueryIntent(BaseModel):
    """Extracted intent from natural language query."""
    intent_type: str = Field(..., description="Type of intent (count, average, list, etc.)")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence in intent classification")
    raw_query: str = Field(..., description="Original query text")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Extracted parameters")


class QueryEntity(BaseModel):
    """Extracted entity from natural language query."""
    text: str = Field(..., description="Entity text")
    label: str = Field(..., description="Entity label/type")
    start: Optional[int] = Field(None, description="Start position in text")
    end: Optional[int] = Field(None, description="End position in text")
    confidence: Optional[float] = Field(None, description="Entity extraction confidence")
    description: Optional[str] = Field(None, description="Entity description")


class DataInsight(BaseModel):
    """Data insight generated from query results."""
    type: str = Field(..., description="Type of insight (summary, trend, anomaly, etc.)")
    title: str = Field(..., description="Insight title")
    description: str = Field(..., description="Insight description")
    value: Optional[Union[int, float, str]] = Field(None, description="Insight value")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence in insight")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class NLPQueryResult(BaseModel):
    """Result of natural language query processing."""
    original_query: str = Field(..., description="Original natural language query")
    cleaned_query: str = Field(..., description="Cleaned and preprocessed query")
    intent: QueryIntent = Field(..., description="Extracted intent")
    entities: List[QueryEntity] = Field(..., description="Extracted entities")
    sql_query: str = Field(..., description="Generated SQL query")
    results: List[Dict[str, Any]] = Field(..., description="Query execution results")
    insights: List[DataInsight] = Field(..., description="Generated insights")
    response: str = Field(..., description="Natural language response")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Overall processing confidence")
    processed_at: datetime = Field(default_factory=datetime.utcnow, description="Processing timestamp")
    execution_time: Optional[float] = Field(None, description="Query execution time in seconds")


class NLPQueryRequest(BaseModel):
    """Request for natural language query processing."""
    query: str = Field(..., min_length=1, description="Natural language query")
    user_context: Optional[Dict[str, Any]] = Field(None, description="User context information")
    include_insights: bool = Field(default=True, description="Whether to generate insights")
    max_results: int = Field(default=100, ge=1, le=1000, description="Maximum number of results")
    response_format: str = Field(default="natural", description="Response format: natural, structured, json")


class QuerySuggestion(BaseModel):
    """Query suggestion for autocomplete."""
    suggestion: str = Field(..., description="Suggested query text")
    category: str = Field(..., description="Query category")
    description: Optional[str] = Field(None, description="Query description")
    popularity: Optional[float] = Field(None, description="Popularity score")


class QueryExplanation(BaseModel):
    """Explanation of how a query will be processed."""
    query_understanding: Dict[str, Any] = Field(..., description="How the query was understood")
    data_sources: List[str] = Field(..., description="Data sources that will be accessed")
    expected_output: str = Field(..., description="Description of expected output")
    processing_steps: List[str] = Field(..., description="Processing steps")
    confidence: float = Field(..., description="Confidence in explanation")


class ConversationContext(BaseModel):
    """Context for conversational queries."""
    conversation_id: str = Field(..., description="Conversation identifier")
    previous_queries: List[str] = Field(default_factory=list, description="Previous queries in conversation")
    context_variables: Dict[str, Any] = Field(default_factory=dict, description="Context variables")
    user_preferences: Optional[Dict[str, Any]] = Field(None, description="User preferences")
    session_start: datetime = Field(default_factory=datetime.utcnow, description="Session start time")


class QueryTemplate(BaseModel):
    """Template for common queries."""
    template_id: str = Field(..., description="Template identifier")
    name: str = Field(..., description="Template name")
    description: str = Field(..., description="Template description")
    pattern: str = Field(..., description="Query pattern")
    sql_template: str = Field(..., description="SQL template")
    parameters: List[str] = Field(default_factory=list, description="Template parameters")
    category: str = Field(..., description="Template category")
    examples: List[str] = Field(default_factory=list, description="Example queries")


class SchemaInfo(BaseModel):
    """Database schema information for query generation."""
    table_name: str = Field(..., description="Table name")
    columns: List[Dict[str, Any]] = Field(..., description="Column information")
    relationships: List[Dict[str, Any]] = Field(default_factory=list, description="Table relationships")
    description: Optional[str] = Field(None, description="Table description")
    sample_data: Optional[List[Dict[str, Any]]] = Field(None, description="Sample data")


class QueryPerformance(BaseModel):
    """Query performance metrics."""
    query_id: str = Field(..., description="Query identifier")
    execution_time: float = Field(..., description="Execution time in seconds")
    rows_returned: int = Field(..., description="Number of rows returned")
    complexity_score: float = Field(..., description="Query complexity score")
    cache_hit: bool = Field(default=False, description="Whether result was cached")
    optimization_suggestions: List[str] = Field(default_factory=list, description="Optimization suggestions")


class NLPAnalytics(BaseModel):
    """Analytics for NLP query usage."""
    total_queries: int = Field(..., description="Total number of queries")
    successful_queries: int = Field(..., description="Number of successful queries")
    failed_queries: int = Field(..., description="Number of failed queries")
    average_confidence: float = Field(..., description="Average confidence score")
    common_intents: Dict[str, int] = Field(..., description="Most common intents")
    popular_entities: Dict[str, int] = Field(..., description="Most popular entities")
    query_categories: Dict[str, int] = Field(..., description="Query categories distribution")
    user_satisfaction: Optional[float] = Field(None, description="User satisfaction score")


class FeedbackRequest(BaseModel):
    """User feedback on query results."""
    query_id: str = Field(..., description="Query identifier")
    rating: int = Field(..., ge=1, le=5, description="Rating from 1-5")
    feedback_text: Optional[str] = Field(None, description="Textual feedback")
    helpful: bool = Field(..., description="Whether the result was helpful")
    accuracy: Optional[int] = Field(None, ge=1, le=5, description="Accuracy rating")
    relevance: Optional[int] = Field(None, ge=1, le=5, description="Relevance rating")


class QueryHistory(BaseModel):
    """User query history."""
    user_id: str = Field(..., description="User identifier")
    queries: List[Dict[str, Any]] = Field(..., description="Historical queries")
    frequent_patterns: List[str] = Field(default_factory=list, description="Frequent query patterns")
    preferred_formats: List[str] = Field(default_factory=list, description="Preferred response formats")
    last_activity: datetime = Field(default_factory=datetime.utcnow, description="Last activity timestamp")


class SmartSuggestion(BaseModel):
    """Smart query suggestion based on context."""
    suggestion_text: str = Field(..., description="Suggested query")
    reason: str = Field(..., description="Reason for suggestion")
    confidence: float = Field(..., description="Confidence in suggestion")
    category: str = Field(..., description="Suggestion category")
    related_data: Optional[Dict[str, Any]] = Field(None, description="Related data preview")


class QueryValidation(BaseModel):
    """Query validation result."""
    is_valid: bool = Field(..., description="Whether query is valid")
    validation_errors: List[str] = Field(default_factory=list, description="Validation errors")
    warnings: List[str] = Field(default_factory=list, description="Validation warnings")
    suggestions: List[str] = Field(default_factory=list, description="Improvement suggestions")
    estimated_complexity: str = Field(..., description="Estimated query complexity")


class DataVisualization(BaseModel):
    """Data visualization recommendation."""
    chart_type: str = Field(..., description="Recommended chart type")
    x_axis: Optional[str] = Field(None, description="X-axis field")
    y_axis: Optional[str] = Field(None, description="Y-axis field")
    grouping: Optional[str] = Field(None, description="Grouping field")
    filters: Optional[Dict[str, Any]] = Field(None, description="Recommended filters")
    title: str = Field(..., description="Suggested chart title")
    description: str = Field(..., description="Chart description")


class QueryOptimization(BaseModel):
    """Query optimization suggestions."""
    original_query: str = Field(..., description="Original SQL query")
    optimized_query: str = Field(..., description="Optimized SQL query")
    optimizations_applied: List[str] = Field(..., description="List of optimizations applied")
    estimated_improvement: float = Field(..., description="Estimated performance improvement")
    complexity_reduction: float = Field(..., description="Complexity reduction percentage")


class NLPModelInfo(BaseModel):
    """Information about NLP models used."""
    model_name: str = Field(..., description="Model name")
    model_type: str = Field(..., description="Model type (intent, entity, etc.)")
    version: str = Field(..., description="Model version")
    accuracy: Optional[float] = Field(None, description="Model accuracy")
    last_trained: Optional[datetime] = Field(None, description="Last training date")
    supported_languages: List[str] = Field(default_factory=list, description="Supported languages")


class BatchQueryRequest(BaseModel):
    """Request for batch query processing."""
    queries: List[str] = Field(..., min_items=1, description="List of queries to process")
    batch_id: Optional[str] = Field(None, description="Batch identifier")
    user_context: Optional[Dict[str, Any]] = Field(None, description="User context")
    parallel_processing: bool = Field(default=True, description="Whether to process in parallel")
    max_concurrent: int = Field(default=5, ge=1, le=20, description="Maximum concurrent queries")


class BatchQueryResult(BaseModel):
    """Result of batch query processing."""
    batch_id: str = Field(..., description="Batch identifier")
    results: List[NLPQueryResult] = Field(..., description="Individual query results")
    summary: Dict[str, Any] = Field(..., description="Batch summary")
    total_queries: int = Field(..., description="Total number of queries")
    successful_queries: int = Field(..., description="Number of successful queries")
    failed_queries: int = Field(..., description="Number of failed queries")
    processing_time: float = Field(..., description="Total processing time")
    processed_at: datetime = Field(default_factory=datetime.utcnow, description="Processing timestamp")
