import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';

// Entities
import {
  Employee,
  Department,
  Position,
  EmployeeDocument,
  EmployeeContact,
  EmployeeAddress,
  EmployeeEmergencyContact,
  EmployeeEducation,
  EmployeeExperience,
  EmployeeSkill,
  EmployeeCertification,
  User,
  Tenant,
  AuditLog,
} from '@app/database';

// Controllers
import { EmployeeController } from './controllers/employee.controller';
import { DepartmentController } from './controllers/department.controller';
import { PositionController } from './controllers/position.controller';
import { EmployeeDocumentController } from './controllers/employee-document.controller';
import { EmployeeOnboardingController } from './controllers/employee-onboarding.controller';
import { EmployeeOffboardingController } from './controllers/employee-offboarding.controller';

// Services
import { EmployeeService } from './services/employee.service';
import { DepartmentService } from './services/department.service';
import { PositionService } from './services/position.service';
import { EmployeeDocumentService } from './services/employee-document.service';
import { EmployeeOnboardingService } from './services/employee-onboarding.service';
import { EmployeeOffboardingService } from './services/employee-offboarding.service';
import { EmployeeSearchService } from './services/employee-search.service';
import { EmployeeValidationService } from './services/employee-validation.service';
import { EmployeeNotificationService } from './services/employee-notification.service';
import { EmployeeAuditService } from './services/employee-audit.service';

// Repositories
import { EmployeeRepository } from './repositories/employee.repository';
import { DepartmentRepository } from './repositories/department.repository';
import { PositionRepository } from './repositories/position.repository';

// Event Listeners
import { EmployeeEventListener } from './listeners/employee-event.listener';

// GraphQL Resolvers
import { EmployeeResolver } from './resolvers/employee.resolver';
import { DepartmentResolver } from './resolvers/department.resolver';
import { PositionResolver } from './resolvers/position.resolver';

// Shared modules
import { SecurityModule } from '@app/security';
import { CommonModule } from '@app/common';

@Module({
  imports: [
    // TypeORM entities
    TypeOrmModule.forFeature([
      Employee,
      Department,
      Position,
      EmployeeDocument,
      EmployeeContact,
      EmployeeAddress,
      EmployeeEmergencyContact,
      EmployeeEducation,
      EmployeeExperience,
      EmployeeSkill,
      EmployeeCertification,
      User,
      Tenant,
      AuditLog,
    ]),

    // File upload configuration
    MulterModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        dest: configService.get('UPLOAD_DESTINATION', './uploads'),
        limits: {
          fileSize: configService.get('MAX_FILE_SIZE', 10 * 1024 * 1024), // 10MB
          files: configService.get('MAX_FILES_PER_REQUEST', 10),
        },
        fileFilter: (req, file, callback) => {
          // Allow common document types
          const allowedMimes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/png',
            'image/gif',
            'text/plain',
            'text/csv',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          ];
          
          if (allowedMimes.includes(file.mimetype)) {
            callback(null, true);
          } else {
            callback(new Error('Invalid file type'), false);
          }
        },
      }),
      inject: [ConfigService],
    }),

    // Event emitter for employee lifecycle events
    EventEmitterModule,

    // Shared modules
    SecurityModule,
    CommonModule,
  ],

  controllers: [
    EmployeeController,
    DepartmentController,
    PositionController,
    EmployeeDocumentController,
    EmployeeOnboardingController,
    EmployeeOffboardingController,
  ],

  providers: [
    // Services
    EmployeeService,
    DepartmentService,
    PositionService,
    EmployeeDocumentService,
    EmployeeOnboardingService,
    EmployeeOffboardingService,
    EmployeeSearchService,
    EmployeeValidationService,
    EmployeeNotificationService,
    EmployeeAuditService,

    // Repositories
    EmployeeRepository,
    DepartmentRepository,
    PositionRepository,

    // Event Listeners
    EmployeeEventListener,

    // GraphQL Resolvers
    EmployeeResolver,
    DepartmentResolver,
    PositionResolver,
  ],

  exports: [
    // Export services for use in other modules
    EmployeeService,
    DepartmentService,
    PositionService,
    EmployeeDocumentService,
    EmployeeOnboardingService,
    EmployeeOffboardingService,
    EmployeeSearchService,
    EmployeeValidationService,
    EmployeeNotificationService,
    EmployeeAuditService,

    // Export repositories
    EmployeeRepository,
    DepartmentRepository,
    PositionRepository,
  ],
})
export class EmployeeModule {}
