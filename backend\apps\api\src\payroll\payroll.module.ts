import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';

// Entities
import { PayrollPeriod } from '@app/database/entities/payroll-period.entity';
import { Payslip } from '@app/database/entities/payslip.entity';
import { PayrollItem } from '@app/database/entities/payroll-item.entity';
import { TaxConfiguration } from '@app/database/entities/tax-configuration.entity';
import { BenefitPlan } from '@app/database/entities/benefit-plan.entity';
import { EmployeeBenefit } from '@app/database/entities/employee-benefit.entity';
import { Deduction } from '@app/database/entities/deduction.entity';
import { EmployeeDeduction } from '@app/database/entities/employee-deduction.entity';
import { Employee } from '@app/database/entities/employee.entity';

// Controllers
import { PayrollController } from './payroll.controller';

// Services
import { PayrollPeriodService } from './services/payroll-period.service';
import { PayrollCalculationService } from './services/payroll-calculation.service';
import { PayrollProcessingService } from './services/payroll-processing.service';
import { TaxCalculationService } from './services/tax-calculation.service';

// Other modules
import { TenantModule } from '../tenant/tenant.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      // Core payroll entities
      PayrollPeriod,
      Payslip,
      PayrollItem,
      
      // Tax and compliance entities
      TaxConfiguration,
      
      // Benefits and deductions entities
      BenefitPlan,
      EmployeeBenefit,
      Deduction,
      EmployeeDeduction,
      
      // Employee entity for relationships
      Employee,
    ]),
    EventEmitterModule,
    TenantModule,
    AuthModule,
  ],
  controllers: [
    PayrollController,
  ],
  providers: [
    PayrollPeriodService,
    PayrollCalculationService,
    PayrollProcessingService,
    TaxCalculationService,
  ],
  exports: [
    PayrollPeriodService,
    PayrollCalculationService,
    PayrollProcessingService,
    TaxCalculationService,
  ],
})
export class PayrollModule {}
