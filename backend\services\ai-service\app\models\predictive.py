"""
Pydantic models for predictive analytics results.
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field


class AttritionPrediction(BaseModel):
    """Employee attrition prediction result."""
    employee_id: str = Field(..., description="Employee identifier")
    probability: float = Field(..., ge=0.0, le=1.0, description="Probability of attrition")
    prediction: bool = Field(..., description="Binary attrition prediction")
    risk_level: str = Field(..., description="Risk level: low, medium, high, critical")
    risk_factors: List[Dict[str, Any]] = Field(..., description="Identified risk factors")
    recommendations: List[str] = Field(..., description="Recommended actions")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Prediction confidence")
    predicted_at: datetime = Field(default_factory=datetime.utcnow, description="Prediction timestamp")
    model_version: str = Field(..., description="Model version used")


class PerformanceForecast(BaseModel):
    """Employee performance forecast result."""
    employee_id: str = Field(..., description="Employee identifier")
    forecasts: List[Dict[str, Any]] = Field(..., description="Performance forecasts by period")
    trend_direction: str = Field(..., description="Performance trend direction")
    trend_strength: float = Field(..., description="Strength of the trend")
    improvement_areas: List[str] = Field(..., description="Areas for improvement")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Forecast confidence")
    forecasted_at: datetime = Field(default_factory=datetime.utcnow, description="Forecast timestamp")
    model_version: str = Field(..., description="Model version used")


class TrendAnalysis(BaseModel):
    """Trend analysis result."""
    trend_type: str = Field(..., description="Type of trend analysis")
    data_points: int = Field(..., description="Number of data points analyzed")
    time_range: Dict[str, Optional[datetime]] = Field(..., description="Time range of analysis")
    trends: Dict[str, Any] = Field(..., description="Identified trends")
    statistical_insights: Dict[str, Any] = Field(..., description="Statistical analysis results")
    seasonal_patterns: Dict[str, Any] = Field(..., description="Seasonal patterns detected")
    future_predictions: List[Dict[str, Any]] = Field(..., description="Future trend predictions")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Analysis confidence")
    analyzed_at: datetime = Field(default_factory=datetime.utcnow, description="Analysis timestamp")


class EmployeeRiskProfile(BaseModel):
    """Comprehensive employee risk profile."""
    employee_id: str = Field(..., description="Employee identifier")
    overall_risk_score: float = Field(..., ge=0.0, le=1.0, description="Overall risk score")
    risk_scores: Dict[str, float] = Field(..., description="Individual risk scores")
    risk_level: str = Field(..., description="Overall risk level")
    priority: str = Field(..., description="Priority level for intervention")
    risk_factors: List[Dict[str, Any]] = Field(..., description="Identified risk factors")
    mitigation_strategies: List[str] = Field(..., description="Recommended mitigation strategies")
    last_updated: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    next_review_date: datetime = Field(..., description="Next review date")


class PredictiveInsights(BaseModel):
    """Organization-wide predictive insights."""
    organization_id: Optional[str] = Field(None, description="Organization identifier")
    key_metrics: Dict[str, Union[int, float]] = Field(..., description="Key organizational metrics")
    workforce_insights: Dict[str, Any] = Field(..., description="Workforce composition insights")
    attrition_insights: Dict[str, Any] = Field(..., description="Attrition analysis insights")
    performance_insights: Dict[str, Any] = Field(..., description="Performance analysis insights")
    compensation_insights: Dict[str, Any] = Field(..., description="Compensation analysis insights")
    diversity_insights: Dict[str, Any] = Field(..., description="Diversity metrics insights")
    recommendations: List[str] = Field(..., description="Strategic recommendations")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Overall insights confidence")
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Generation timestamp")
    data_period: Dict[str, Optional[datetime]] = Field(..., description="Data period analyzed")


class ModelPerformanceMetrics(BaseModel):
    """Model performance metrics."""
    model_name: str = Field(..., description="Name of the model")
    model_type: str = Field(..., description="Type of model (classification/regression)")
    accuracy: Optional[float] = Field(None, description="Model accuracy")
    precision: Optional[float] = Field(None, description="Model precision")
    recall: Optional[float] = Field(None, description="Model recall")
    f1_score: Optional[float] = Field(None, description="F1 score")
    mse: Optional[float] = Field(None, description="Mean squared error")
    r2_score: Optional[float] = Field(None, description="R-squared score")
    cross_val_score: Optional[float] = Field(None, description="Cross-validation score")
    feature_importance: Optional[Dict[str, float]] = Field(None, description="Feature importance scores")
    training_samples: int = Field(..., description="Number of training samples")
    test_samples: int = Field(..., description="Number of test samples")
    training_time: float = Field(..., description="Training time in seconds")
    last_trained: datetime = Field(default_factory=datetime.utcnow, description="Last training timestamp")


class PredictionRequest(BaseModel):
    """Base prediction request model."""
    employee_data: Dict[str, Any] = Field(..., description="Employee data for prediction")
    include_risk_factors: bool = Field(default=True, description="Include risk factor analysis")
    include_recommendations: bool = Field(default=True, description="Include recommendations")


class AttritionPredictionRequest(PredictionRequest):
    """Attrition prediction request."""
    model_version: Optional[str] = Field(None, description="Specific model version to use")
    threshold: float = Field(default=0.5, ge=0.0, le=1.0, description="Prediction threshold")


class PerformanceForecastRequest(PredictionRequest):
    """Performance forecast request."""
    forecast_periods: int = Field(default=4, ge=1, le=12, description="Number of periods to forecast")
    include_confidence_intervals: bool = Field(default=True, description="Include confidence intervals")


class TrendAnalysisRequest(BaseModel):
    """Trend analysis request."""
    data: List[Dict[str, Any]] = Field(..., min_items=1, description="Data for trend analysis")
    trend_type: str = Field(default="general", description="Type of trend analysis")
    time_column: str = Field(default="date", description="Column containing time data")
    value_column: Optional[str] = Field(None, description="Column containing values to analyze")
    include_seasonality: bool = Field(default=True, description="Include seasonal analysis")
    forecast_periods: int = Field(default=6, ge=1, le=24, description="Periods to forecast")


class RiskProfileRequest(BaseModel):
    """Risk profile generation request."""
    employee_data: Dict[str, Any] = Field(..., description="Employee data")
    include_all_risks: bool = Field(default=True, description="Include all risk types")
    risk_types: Optional[List[str]] = Field(None, description="Specific risk types to analyze")
    priority_threshold: float = Field(default=0.7, description="Threshold for high priority")


class OrganizationalInsightsRequest(BaseModel):
    """Organizational insights request."""
    organization_data: List[Dict[str, Any]] = Field(..., min_items=1, description="Organization data")
    include_forecasts: bool = Field(default=True, description="Include future forecasts")
    include_benchmarks: bool = Field(default=False, description="Include industry benchmarks")
    analysis_depth: str = Field(default="standard", description="Analysis depth: basic, standard, comprehensive")


class ModelTrainingRequest(BaseModel):
    """Model training request."""
    training_data: List[Dict[str, Any]] = Field(..., min_items=10, description="Training data")
    model_type: str = Field(..., description="Type of model to train")
    target_column: str = Field(..., description="Target column for prediction")
    feature_columns: Optional[List[str]] = Field(None, description="Specific features to use")
    validation_split: float = Field(default=0.2, ge=0.1, le=0.4, description="Validation data split")
    hyperparameters: Optional[Dict[str, Any]] = Field(None, description="Model hyperparameters")


class BatchPredictionRequest(BaseModel):
    """Batch prediction request."""
    employees_data: List[Dict[str, Any]] = Field(..., min_items=1, description="Multiple employee data")
    prediction_type: str = Field(..., description="Type of prediction: attrition, performance, risk")
    batch_size: int = Field(default=100, ge=1, le=1000, description="Batch processing size")
    include_summary: bool = Field(default=True, description="Include batch summary")


class PredictionResult(BaseModel):
    """Generic prediction result."""
    prediction_id: str = Field(..., description="Unique prediction identifier")
    prediction_type: str = Field(..., description="Type of prediction")
    result: Dict[str, Any] = Field(..., description="Prediction result")
    metadata: Dict[str, Any] = Field(..., description="Additional metadata")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")


class BatchPredictionResult(BaseModel):
    """Batch prediction result."""
    batch_id: str = Field(..., description="Batch identifier")
    prediction_type: str = Field(..., description="Type of predictions")
    results: List[PredictionResult] = Field(..., description="Individual prediction results")
    summary: Dict[str, Any] = Field(..., description="Batch summary statistics")
    total_processed: int = Field(..., description="Total items processed")
    successful_predictions: int = Field(..., description="Number of successful predictions")
    failed_predictions: int = Field(..., description="Number of failed predictions")
    processing_time: float = Field(..., description="Total processing time")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")


class ModelInfo(BaseModel):
    """Model information."""
    model_name: str = Field(..., description="Model name")
    model_type: str = Field(..., description="Model type")
    version: str = Field(..., description="Model version")
    description: str = Field(..., description="Model description")
    features: List[str] = Field(..., description="Model features")
    target: str = Field(..., description="Model target variable")
    performance_metrics: ModelPerformanceMetrics = Field(..., description="Performance metrics")
    created_at: datetime = Field(..., description="Model creation date")
    last_updated: datetime = Field(..., description="Last update date")
    status: str = Field(..., description="Model status: active, deprecated, training")


class FeatureImportance(BaseModel):
    """Feature importance analysis."""
    feature_name: str = Field(..., description="Feature name")
    importance_score: float = Field(..., description="Importance score")
    rank: int = Field(..., description="Feature rank")
    description: Optional[str] = Field(None, description="Feature description")


class ModelExplanation(BaseModel):
    """Model prediction explanation."""
    prediction_id: str = Field(..., description="Prediction identifier")
    model_name: str = Field(..., description="Model used")
    feature_contributions: List[FeatureImportance] = Field(..., description="Feature contributions")
    confidence_factors: Dict[str, float] = Field(..., description="Confidence factors")
    explanation_text: str = Field(..., description="Human-readable explanation")
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Generation timestamp")
