# PeopleNest HRMS - Testing Guide

This document provides comprehensive information about testing in the PeopleNest HRMS application.

## Overview

The PeopleNest HRMS application uses a multi-layered testing strategy to ensure reliability, security, and performance:

- **Unit Tests**: Test individual functions and components in isolation
- **Integration Tests**: Test interactions between services and components
- **End-to-End (E2E) Tests**: Test complete user workflows
- **Security Tests**: Validate security controls and compliance
- **Performance Tests**: Ensure system performance under load

## Coverage Requirements

- **Backend**: Minimum 85% coverage (branches, functions, lines, statements)
- **Frontend**: Minimum 80% coverage (branches, functions, lines, statements)

## Test Structure

### Backend Tests (`backend/`)

```
backend/
├── test/
│   ├── jest-e2e.json          # E2E test configuration
│   └── setup-e2e.ts           # E2E test utilities
├── libs/
│   └── security/src/services/
│       ├── auth.service.spec.ts           # Auth service unit tests
│       ├── encryption.service.spec.ts     # Encryption service unit tests
│       └── gdpr-compliance.service.spec.ts # GDPR compliance tests
└── package.json               # Test scripts and Jest configuration
```

### Frontend Tests (`frontend/`)

```
frontend/
├── src/
│   ├── test/
│   │   └── setup.ts           # Test setup and mocks
│   ├── App.test.tsx           # App component tests
│   └── services/
│       └── employee-service.test.ts # Employee service tests
├── e2e/
│   ├── global-setup.ts        # E2E global setup
│   ├── global-teardown.ts     # E2E global teardown
│   ├── auth.spec.ts           # Authentication E2E tests
│   └── employees.spec.ts      # Employee management E2E tests
├── playwright.config.ts       # Playwright configuration
└── vite.config.ts            # Vitest configuration
```

## Running Tests

### Quick Start

```bash
# Run all tests
./scripts/run-tests.sh

# Run specific test suites
./scripts/run-tests.sh --skip-e2e          # Skip E2E tests
./scripts/run-tests.sh --skip-performance  # Skip performance tests
```

### Backend Tests

```bash
cd backend

# Run all unit tests
npm run test

# Run tests with coverage
npm run test:cov

# Run integration tests
npm run test:e2e

# Run specific test file
npm run test auth.service.spec.ts

# Run tests in watch mode
npm run test:watch
```

### Frontend Tests

```bash
cd frontend

# Run all unit tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm run test App.test.tsx
```

### E2E Tests

```bash
cd frontend

# Install Playwright browsers
npx playwright install

# Run all E2E tests
npm run test:e2e

# Run E2E tests in headed mode
npm run test:e2e -- --headed

# Run specific E2E test
npm run test:e2e auth.spec.ts

# Debug E2E tests
npm run test:e2e -- --debug
```

## Test Configuration

### Jest (Backend)

Configuration in `backend/package.json`:

```json
{
  "jest": {
    "moduleFileExtensions": ["js", "json", "ts"],
    "rootDir": ".",
    "testRegex": ".*\\.spec\\.ts$",
    "transform": {
      "^.+\\.(t|j)s$": "ts-jest"
    },
    "collectCoverageFrom": [
      "src/**/*.(t|j)s",
      "libs/**/*.(t|j)s"
    ],
    "coverageDirectory": "./coverage",
    "testEnvironment": "node",
    "coverageThreshold": {
      "global": {
        "branches": 85,
        "functions": 85,
        "lines": 85,
        "statements": 85
      }
    }
  }
}
```

### Vitest (Frontend)

Configuration in `frontend/vite.config.ts`:

```typescript
export default defineConfig({
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      thresholds: {
        branches: 80,
        functions: 80,
        lines: 80,
        statements: 80
      }
    }
  }
});
```

### Playwright (E2E)

Configuration in `frontend/playwright.config.ts`:

```typescript
export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  retries: process.env.CI ? 2 : 0,
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure'
  },
  projects: [
    { name: 'chromium', use: { ...devices['Desktop Chrome'] } },
    { name: 'firefox', use: { ...devices['Desktop Firefox'] } },
    { name: 'webkit', use: { ...devices['Desktop Safari'] } }
  ]
});
```

## Writing Tests

### Unit Test Example

```typescript
// backend/libs/security/src/services/auth.service.spec.ts
describe('AuthService', () => {
  let service: AuthService;
  
  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [AuthService, /* mocked dependencies */]
    }).compile();
    
    service = module.get<AuthService>(AuthService);
  });
  
  it('should validate user with correct credentials', async () => {
    // Arrange
    const email = '<EMAIL>';
    const password = 'password123';
    
    // Act
    const result = await service.validateUser(email, password);
    
    // Assert
    expect(result).toBeDefined();
    expect(result.email).toBe(email);
  });
});
```

### Frontend Component Test Example

```typescript
// frontend/src/App.test.tsx
describe('App', () => {
  it('should render login page for unauthenticated user', () => {
    // Arrange
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      isLoading: false
    });
    
    // Act
    render(<App />);
    
    // Assert
    expect(screen.getByTestId('login-page')).toBeInTheDocument();
  });
});
```

### E2E Test Example

```typescript
// frontend/e2e/auth.spec.ts
test('should login successfully with valid credentials', async ({ page }) => {
  // Navigate to login page
  await page.goto('/login');
  
  // Fill credentials
  await page.fill('input[type="email"]', '<EMAIL>');
  await page.fill('input[type="password"]', 'Password1234');
  
  // Submit form
  await page.click('button[type="submit"]');
  
  // Verify redirect to dashboard
  await expect(page).toHaveURL('/dashboard');
});
```

## Test Data Management

### Test Database

- E2E tests use a separate test database
- Database is reset before each test run
- Test data is created in `global-setup.ts`
- Cleanup happens in `global-teardown.ts`

### Mock Data

```typescript
// Test utilities for creating mock data
export const createMockUser = (overrides = {}) => ({
  id: 'user-123',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  role: 'EMPLOYEE',
  ...overrides
});
```

## CI/CD Integration

### GitHub Actions

```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: ./scripts/run-tests.sh
      - uses: codecov/codecov-action@v3
```

## Debugging Tests

### Backend Tests

```bash
# Debug with Node.js inspector
node --inspect-brk node_modules/.bin/jest --runInBand

# Debug specific test
npm run test:debug auth.service.spec.ts
```

### Frontend Tests

```bash
# Debug with browser dev tools
npm run test:debug

# Debug specific test
npm run test -- --reporter=verbose App.test.tsx
```

### E2E Tests

```bash
# Run in headed mode
npm run test:e2e -- --headed

# Debug mode with step-by-step execution
npm run test:e2e -- --debug

# Record test execution
npm run test:e2e -- --trace=on
```

## Best Practices

### General

1. **Test Naming**: Use descriptive test names that explain the scenario
2. **AAA Pattern**: Arrange, Act, Assert structure
3. **Isolation**: Each test should be independent
4. **Mocking**: Mock external dependencies appropriately
5. **Coverage**: Aim for high coverage but focus on critical paths

### Unit Tests

1. Test one thing at a time
2. Use meaningful test data
3. Mock external dependencies
4. Test both success and error scenarios
5. Keep tests fast and focused

### Integration Tests

1. Test service interactions
2. Use real database connections
3. Test API endpoints end-to-end
4. Verify data persistence
5. Test authentication and authorization

### E2E Tests

1. Test critical user journeys
2. Use page object pattern for complex pages
3. Handle async operations properly
4. Test across different browsers
5. Keep tests stable and reliable

## Troubleshooting

### Common Issues

1. **Flaky Tests**: Use proper waits and assertions
2. **Slow Tests**: Optimize database operations and mocks
3. **Memory Leaks**: Properly clean up resources
4. **Race Conditions**: Use proper synchronization
5. **Environment Issues**: Ensure consistent test environment

### Performance

1. Run tests in parallel when possible
2. Use test databases optimized for speed
3. Mock heavy operations
4. Cache test data when appropriate
5. Profile slow tests and optimize

## Reporting

### Coverage Reports

- HTML reports: `backend/coverage/lcov-report/index.html`
- JSON reports: `backend/coverage/coverage-final.json`
- LCOV format: `backend/coverage/lcov.info`

### Test Results

- JUnit XML: `test-results/results.xml`
- JSON format: `test-results/results.json`
- HTML reports: `test-results/html-report/index.html`

### Continuous Monitoring

- Coverage trends tracked in CI/CD
- Test execution time monitoring
- Flaky test detection and reporting
- Performance regression detection
