# Icons

<img src="public/cover.jpg" />

This library contains the icons used in Storybook and Chromatic apps and marketing sites. They are 14x14. To view the list of all available icons, please go to https://main--64b56e737c0aeefed9d5e675.chromatic.com/

## Install

npm:

```console
npm install @storybook/icons
```

yarn:

```console
yarn add @storybook/icons
```

pnpm:

```console
pnpm add @storybook/icons
```

## To Do

- [x] Export every single icon instead of a single component
- [x] Add stories for each component

## Generating new icons from Figma

First, set local environment variables:

```console
FIGMA_API_TOKEN=<YOUR_FIGMA_API_TOKEN>
FIGMA_FILE_ID=<FIGMA_FILE_ID>
FIGMA_CANVAS=Icons
```

Then, fetch all icons from Figma locally and create React components + stories automatically:

```console
pnpm generate-icons
```

Inspired by [this article](https://blog.certa.dev/generating-react-icon-components-from-figma).

## Developing

Watch and rebuild code with `tsup` and run Storybook to preview your UI during development:

```console
pnpm storybook
```

## Building

Build package with `tsup` for production:

```console
pnpm build
```
