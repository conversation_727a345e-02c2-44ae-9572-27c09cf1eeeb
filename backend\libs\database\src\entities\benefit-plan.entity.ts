import {
  Entity,
  Column,
  Index,
  OneToMany,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { EmployeeBenefit } from './employee-benefit.entity';
import { BenefitType, Currency } from '@app/common/enums/status.enum';

@Entity('benefit_plans')
@Index(['tenantId', 'code'], { unique: true })
@Index(['tenantId', 'benefitType'])
@Index(['tenantId', 'isActive'])
export class BenefitPlan extends TenantAwareEntity {
  @Column({
    type: 'varchar',
    length: 50,
    comment: 'Benefit plan code',
  })
  @Index()
  code: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Benefit plan name',
  })
  name: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Benefit plan description',
  })
  description?: string;

  @Column({
    type: 'enum',
    enum: BenefitType,
    comment: 'Type of benefit',
  })
  @Index()
  benefitType: BenefitType;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Benefit provider/carrier name',
  })
  provider?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Plan number or identifier',
  })
  planNumber?: string;

  @Column({
    type: 'enum',
    enum: Currency,
    default: Currency.USD,
    comment: 'Currency for benefit amounts',
  })
  currency: Currency;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Employee contribution amount',
  })
  employeeContribution?: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Employer contribution amount',
  })
  employerContribution?: number;

  @Column({
    type: 'varchar',
    length: 50,
    default: 'fixed',
    comment: 'Contribution calculation method',
  })
  contributionMethod: string;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 4,
    nullable: true,
    comment: 'Employee contribution percentage',
  })
  employeeContributionPercentage?: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 4,
    nullable: true,
    comment: 'Employer contribution percentage',
  })
  employerContributionPercentage?: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Maximum employee contribution',
  })
  maxEmployeeContribution?: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Maximum employer contribution',
  })
  maxEmployerContribution?: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Annual contribution limit',
  })
  annualLimit?: number;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether employee contribution is pre-tax',
  })
  isPreTax: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this benefit is taxable',
  })
  isTaxable: boolean;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Eligibility criteria',
  })
  eligibilityCriteria?: {
    minimumHours?: number;
    minimumTenure?: number; // in months
    employmentTypes?: string[];
    departments?: string[];
    positions?: string[];
    salaryRange?: {
      min?: number;
      max?: number;
    };
    ageRange?: {
      min?: number;
      max?: number;
    };
    customRules?: Array<{
      field: string;
      operator: string;
      value: any;
    }>;
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Enrollment periods and rules',
  })
  enrollmentRules?: {
    openEnrollmentStart?: string; // MM-DD format
    openEnrollmentEnd?: string;
    newHireEligibilityDays?: number;
    qualifyingEventDays?: number;
    autoEnroll?: boolean;
    requiresApproval?: boolean;
    waitingPeriodDays?: number;
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Coverage options and tiers',
  })
  coverageOptions?: Array<{
    tier: string;
    name: string;
    description?: string;
    employeeContribution?: number;
    employerContribution?: number;
    coverage?: {
      individual?: boolean;
      spouse?: boolean;
      children?: boolean;
      family?: boolean;
    };
  }>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Vesting schedule for employer contributions',
  })
  vestingSchedule?: Array<{
    years: number;
    percentage: number;
  }>;

  @Column({
    type: 'date',
    comment: 'Plan effective start date',
  })
  @Index()
  effectiveStartDate: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Plan effective end date',
  })
  @Index()
  effectiveEndDate?: Date;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this benefit plan is active',
  })
  @Index()
  isActive: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this plan is mandatory',
  })
  isMandatory: boolean;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether employees can opt out',
  })
  allowOptOut: boolean;

  @Column({
    type: 'varchar',
    length: 50,
    default: 'monthly',
    comment: 'Contribution frequency',
  })
  contributionFrequency: string;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Display order',
  })
  sortOrder: number;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Contact information for plan administrator',
  })
  administratorContact?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Website URL for plan information',
  })
  planWebsite?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Additional plan documents or notes',
  })
  planDocuments?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Integration settings for external benefit providers',
  })
  integrationSettings?: {
    provider?: string;
    apiEndpoint?: string;
    credentials?: Record<string, any>;
    mappings?: Record<string, string>;
    syncFrequency?: string;
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Compliance and reporting requirements',
  })
  complianceSettings?: {
    reportingRequired?: boolean;
    reportingFrequency?: string;
    forms?: string[];
    auditRequirements?: string[];
    retentionPeriod?: number; // in years
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Benefit plan metadata',
  })
  metadata?: Record<string, any>;

  // Relationships
  @OneToMany(() => EmployeeBenefit, employeeBenefit => employeeBenefit.benefitPlan)
  employeeBenefits: EmployeeBenefit[];

  // Virtual properties
  get isCurrentlyEffective(): boolean {
    const now = new Date();
    const startOk = this.effectiveStartDate <= now;
    const endOk = !this.effectiveEndDate || this.effectiveEndDate >= now;
    return startOk && endOk && this.isActive;
  }

  get isExpired(): boolean {
    return this.effectiveEndDate ? this.effectiveEndDate < new Date() : false;
  }

  get isFuture(): boolean {
    return this.effectiveStartDate > new Date();
  }

  get hasVesting(): boolean {
    return this.vestingSchedule && this.vestingSchedule.length > 0;
  }

  get hasCoverageOptions(): boolean {
    return this.coverageOptions && this.coverageOptions.length > 0;
  }

  // Methods
  calculateEmployeeContribution(salary: number, tier?: string): number {
    let contribution = 0;

    if (tier && this.coverageOptions) {
      const option = this.coverageOptions.find(opt => opt.tier === tier);
      if (option && option.employeeContribution) {
        contribution = option.employeeContribution;
      }
    } else if (this.contributionMethod === 'percentage' && this.employeeContributionPercentage) {
      contribution = (salary * this.employeeContributionPercentage) / 100;
    } else if (this.employeeContribution) {
      contribution = this.employeeContribution;
    }

    // Apply maximum contribution limit
    if (this.maxEmployeeContribution) {
      contribution = Math.min(contribution, this.maxEmployeeContribution);
    }

    return contribution;
  }

  calculateEmployerContribution(salary: number, tier?: string): number {
    let contribution = 0;

    if (tier && this.coverageOptions) {
      const option = this.coverageOptions.find(opt => opt.tier === tier);
      if (option && option.employerContribution) {
        contribution = option.employerContribution;
      }
    } else if (this.contributionMethod === 'percentage' && this.employerContributionPercentage) {
      contribution = (salary * this.employerContributionPercentage) / 100;
    } else if (this.employerContribution) {
      contribution = this.employerContribution;
    }

    // Apply maximum contribution limit
    if (this.maxEmployerContribution) {
      contribution = Math.min(contribution, this.maxEmployerContribution);
    }

    return contribution;
  }

  isEmployeeEligible(employee: {
    hoursPerWeek?: number;
    tenureMonths?: number;
    employmentType?: string;
    departmentId?: string;
    positionId?: string;
    salary?: number;
    age?: number;
  }): boolean {
    if (!this.eligibilityCriteria) return true;

    const criteria = this.eligibilityCriteria;

    // Check minimum hours
    if (criteria.minimumHours && (!employee.hoursPerWeek || employee.hoursPerWeek < criteria.minimumHours)) {
      return false;
    }

    // Check minimum tenure
    if (criteria.minimumTenure && (!employee.tenureMonths || employee.tenureMonths < criteria.minimumTenure)) {
      return false;
    }

    // Check employment types
    if (criteria.employmentTypes && employee.employmentType && !criteria.employmentTypes.includes(employee.employmentType)) {
      return false;
    }

    // Check departments
    if (criteria.departments && employee.departmentId && !criteria.departments.includes(employee.departmentId)) {
      return false;
    }

    // Check positions
    if (criteria.positions && employee.positionId && !criteria.positions.includes(employee.positionId)) {
      return false;
    }

    // Check salary range
    if (criteria.salaryRange && employee.salary) {
      if (criteria.salaryRange.min && employee.salary < criteria.salaryRange.min) {
        return false;
      }
      if (criteria.salaryRange.max && employee.salary > criteria.salaryRange.max) {
        return false;
      }
    }

    // Check age range
    if (criteria.ageRange && employee.age) {
      if (criteria.ageRange.min && employee.age < criteria.ageRange.min) {
        return false;
      }
      if (criteria.ageRange.max && employee.age > criteria.ageRange.max) {
        return false;
      }
    }

    return true;
  }

  getVestedPercentage(yearsOfService: number): number {
    if (!this.vestingSchedule || this.vestingSchedule.length === 0) {
      return 100; // Fully vested if no schedule
    }

    // Find the appropriate vesting percentage
    let vestedPercentage = 0;
    for (const schedule of this.vestingSchedule) {
      if (yearsOfService >= schedule.years) {
        vestedPercentage = schedule.percentage;
      }
    }

    return vestedPercentage;
  }

  @BeforeInsert()
  @BeforeUpdate()
  validatePlan(): void {
    if (this.effectiveStartDate && this.effectiveEndDate) {
      if (this.effectiveStartDate > this.effectiveEndDate) {
        throw new Error('Effective start date must be before end date');
      }
    }

    if (this.contributionMethod === 'percentage') {
      if (!this.employeeContributionPercentage && !this.employerContributionPercentage) {
        throw new Error('Percentage contribution method requires contribution percentages');
      }
    }

    if (this.maxEmployeeContribution && this.employeeContribution) {
      if (this.employeeContribution > this.maxEmployeeContribution) {
        throw new Error('Employee contribution cannot exceed maximum');
      }
    }

    if (this.maxEmployerContribution && this.employerContribution) {
      if (this.employerContribution > this.maxEmployerContribution) {
        throw new Error('Employer contribution cannot exceed maximum');
      }
    }
  }
}
