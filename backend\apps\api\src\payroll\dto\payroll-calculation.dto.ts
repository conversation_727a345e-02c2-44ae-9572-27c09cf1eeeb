import { IsString, IsOptional, IsEnum, IsNumber, IsBoolean, IsArray, ValidateNested, IsObject, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PayrollItemType, Currency, PaymentMethod } from '@app/common/enums/status.enum';

export class PayrollItemDto {
  @ApiProperty({
    description: 'Payroll item code',
    example: 'SALARY',
  })
  @IsString()
  code: string;

  @ApiProperty({
    description: 'Payroll item name',
    example: 'Base Salary',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Type of payroll item',
    enum: PayrollItemType,
    example: PayrollItemType.SALARY,
  })
  @IsEnum(PayrollItemType)
  type: PayrollItemType;

  @ApiProperty({
    description: 'Item amount',
    example: 5000.00,
  })
  @IsNumber()
  amount: number;

  @ApiProperty({
    description: 'Item currency',
    enum: Currency,
    example: Currency.USD,
  })
  @IsEnum(Currency)
  currency: Currency;

  @ApiPropertyOptional({
    description: 'Rate for hourly calculations',
    example: 25.00,
  })
  @IsOptional()
  @IsNumber()
  rate?: number;

  @ApiPropertyOptional({
    description: 'Units/hours for rate-based calculations',
    example: 160,
  })
  @IsOptional()
  @IsNumber()
  units?: number;

  @ApiPropertyOptional({
    description: 'Percentage for percentage-based calculations',
    example: 10.5,
  })
  @IsOptional()
  @IsNumber()
  percentage?: number;

  @ApiProperty({
    description: 'Whether this item is taxable',
    example: true,
  })
  @IsBoolean()
  isTaxable: boolean;

  @ApiProperty({
    description: 'Whether this is a pre-tax deduction',
    example: false,
  })
  @IsBoolean()
  isPreTax: boolean;
}

export class CalculatePayrollDto {
  @ApiProperty({
    description: 'Payroll period ID',
    example: 'uuid-string',
  })
  @IsUUID()
  payrollPeriodId: string;

  @ApiPropertyOptional({
    description: 'Specific employee IDs to calculate (if not provided, calculates for all)',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  employeeIds?: string[];

  @ApiPropertyOptional({
    description: 'Whether to recalculate existing payslips',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  recalculate?: boolean;

  @ApiPropertyOptional({
    description: 'Whether to include inactive employees',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  includeInactive?: boolean;

  @ApiPropertyOptional({
    description: 'Whether to include probationary employees',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  includeProbationary?: boolean;

  @ApiPropertyOptional({
    description: 'Custom payroll items to include',
    type: [PayrollItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PayrollItemDto)
  customItems?: PayrollItemDto[];
}

export class PayrollCalculationResultDto {
  @ApiProperty({
    description: 'Employee ID',
    example: 'uuid-string',
  })
  employeeId: string;

  @ApiProperty({
    description: 'Employee name',
    example: 'John Doe',
  })
  employeeName: string;

  @ApiProperty({
    description: 'Total gross pay',
    example: 5000.00,
  })
  grossPay: number;

  @ApiProperty({
    description: 'Total net pay',
    example: 3800.00,
  })
  netPay: number;

  @ApiProperty({
    description: 'Total taxes',
    example: 900.00,
  })
  totalTaxes: number;

  @ApiProperty({
    description: 'Total deductions',
    example: 300.00,
  })
  totalDeductions: number;

  @ApiProperty({
    description: 'Total benefits',
    example: 200.00,
  })
  totalBenefits: number;

  @ApiProperty({
    description: 'Regular hours worked',
    example: 160,
  })
  regularHours: number;

  @ApiProperty({
    description: 'Overtime hours worked',
    example: 8,
  })
  overtimeHours: number;

  @ApiProperty({
    description: 'Payroll items breakdown',
    type: [PayrollItemDto],
  })
  payrollItems: PayrollItemDto[];

  @ApiPropertyOptional({
    description: 'Calculation errors',
    type: [String],
  })
  @IsOptional()
  errors?: string[];

  @ApiPropertyOptional({
    description: 'Calculation warnings',
    type: [String],
  })
  @IsOptional()
  warnings?: string[];
}

export class ProcessPayrollDto {
  @ApiProperty({
    description: 'Payroll period ID',
    example: 'uuid-string',
  })
  @IsUUID()
  payrollPeriodId: string;

  @ApiPropertyOptional({
    description: 'Specific payslip IDs to process (if not provided, processes all approved)',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  payslipIds?: string[];

  @ApiProperty({
    description: 'Payment method',
    enum: PaymentMethod,
    example: PaymentMethod.DIRECT_DEPOSIT,
  })
  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @ApiPropertyOptional({
    description: 'Payment reference number',
    example: 'PAY-2024-001',
  })
  @IsOptional()
  @IsString()
  paymentReference?: string;

  @ApiPropertyOptional({
    description: 'Whether to send notifications to employees',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  sendNotifications?: boolean;
}

export class PayrollProcessingResultDto {
  @ApiProperty({
    description: 'Payroll period ID',
    example: 'uuid-string',
  })
  payrollPeriodId: string;

  @ApiProperty({
    description: 'Total number of payslips processed',
    example: 150,
  })
  totalProcessed: number;

  @ApiProperty({
    description: 'Number of successful payments',
    example: 148,
  })
  successfulPayments: number;

  @ApiProperty({
    description: 'Number of failed payments',
    example: 2,
  })
  failedPayments: number;

  @ApiProperty({
    description: 'Total amount processed',
    example: 750000.00,
  })
  totalAmount: number;

  @ApiProperty({
    description: 'Processing start time',
    example: '2024-01-15T10:00:00Z',
  })
  processingStartedAt: Date;

  @ApiProperty({
    description: 'Processing completion time',
    example: '2024-01-15T10:30:00Z',
  })
  processingCompletedAt: Date;

  @ApiPropertyOptional({
    description: 'Processing errors',
    type: [Object],
  })
  @IsOptional()
  errors?: Array<{
    employeeId: string;
    employeeName: string;
    error: string;
    severity: 'warning' | 'error' | 'critical';
  }>;
}

export class PayrollAnomalyDto {
  @ApiProperty({
    description: 'Anomaly type',
    example: 'SALARY_VARIANCE',
  })
  type: string;

  @ApiProperty({
    description: 'Anomaly severity',
    example: 'HIGH',
  })
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

  @ApiProperty({
    description: 'Employee ID',
    example: 'uuid-string',
  })
  employeeId: string;

  @ApiProperty({
    description: 'Employee name',
    example: 'John Doe',
  })
  employeeName: string;

  @ApiProperty({
    description: 'Anomaly description',
    example: 'Salary variance of 25% detected compared to previous period',
  })
  description: string;

  @ApiProperty({
    description: 'Expected value',
    example: 5000.00,
  })
  expectedValue: number;

  @ApiProperty({
    description: 'Actual value',
    example: 6250.00,
  })
  actualValue: number;

  @ApiProperty({
    description: 'Variance percentage',
    example: 25.0,
  })
  variancePercentage: number;

  @ApiProperty({
    description: 'Detection timestamp',
    example: '2024-01-15T10:00:00Z',
  })
  detectedAt: Date;
}

export class PayrollValidationDto {
  @ApiProperty({
    description: 'Payroll period ID',
    example: 'uuid-string',
  })
  @IsUUID()
  payrollPeriodId: string;

  @ApiPropertyOptional({
    description: 'Specific employee IDs to validate',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  employeeIds?: string[];

  @ApiPropertyOptional({
    description: 'Validation rules to apply',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  validationRules?: string[];
}

export class PayrollValidationResultDto {
  @ApiProperty({
    description: 'Whether validation passed',
    example: true,
  })
  isValid: boolean;

  @ApiProperty({
    description: 'Total number of employees validated',
    example: 150,
  })
  totalValidated: number;

  @ApiProperty({
    description: 'Number of validation errors',
    example: 2,
  })
  errorCount: number;

  @ApiProperty({
    description: 'Number of validation warnings',
    example: 5,
  })
  warningCount: number;

  @ApiPropertyOptional({
    description: 'Validation errors',
    type: [Object],
  })
  @IsOptional()
  errors?: Array<{
    employeeId: string;
    employeeName: string;
    rule: string;
    message: string;
    severity: 'error' | 'warning';
  }>;

  @ApiPropertyOptional({
    description: 'Detected anomalies',
    type: [PayrollAnomalyDto],
  })
  @IsOptional()
  anomalies?: PayrollAnomalyDto[];
}
