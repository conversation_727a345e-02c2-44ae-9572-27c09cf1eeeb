import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder, Brackets } from 'typeorm';

import { Employee } from '@app/database';
import { EmployeeStatus, EmploymentType } from '@app/common/enums/status.enum';

export interface EmployeeSearchFilters {
  search?: string;
  status?: EmployeeStatus;
  employmentType?: EmploymentType;
  departmentId?: string;
  positionId?: string;
  managerId?: string;
  gender?: string;
  maritalStatus?: string;
  workLocation?: string;
  joiningDateFrom?: Date;
  joiningDateTo?: Date;
  minSalary?: number;
  maxSalary?: number;
  skills?: string[];
  activeOnly?: boolean;
  probationOnly?: boolean;
}

export interface EmployeeSearchOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  include?: string[];
}

@Injectable()
export class EmployeeRepository {
  constructor(
    @InjectRepository(Employee)
    private readonly repository: Repository<Employee>,
  ) {}

  /**
   * Find employees with advanced filtering and search
   */
  async findWithFilters(
    tenantId: string,
    filters: EmployeeSearchFilters,
    options: EmployeeSearchOptions,
  ): Promise<{ employees: Employee[]; total: number }> {
    const queryBuilder = this.createBaseQuery(tenantId, options.include);

    // Apply filters
    this.applyFilters(queryBuilder, filters);

    // Apply sorting
    this.applySorting(queryBuilder, options.sortBy, options.sortOrder);

    // Apply pagination
    const { page = 1, limit = 20 } = options;
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [employees, total] = await queryBuilder.getManyAndCount();

    return { employees, total };
  }

  /**
   * Find employees by department with hierarchy
   */
  async findByDepartmentHierarchy(
    tenantId: string,
    departmentId: string,
    includeSubDepartments: boolean = false,
  ): Promise<Employee[]> {
    const queryBuilder = this.createBaseQuery(tenantId)
      .leftJoinAndSelect('employee.department', 'dept');

    if (includeSubDepartments) {
      // Include employees from sub-departments using closure table
      queryBuilder
        .leftJoin('dept.descendants', 'subDept')
        .andWhere(
          new Brackets(qb => {
            qb.where('employee.departmentId = :departmentId', { departmentId })
              .orWhere('subDept.id = :departmentId', { departmentId });
          })
        );
    } else {
      queryBuilder.andWhere('employee.departmentId = :departmentId', { departmentId });
    }

    return queryBuilder.getMany();
  }

  /**
   * Find employees reporting to a manager (direct and indirect)
   */
  async findByManagerHierarchy(
    tenantId: string,
    managerId: string,
    includeIndirectReports: boolean = false,
  ): Promise<Employee[]> {
    if (!includeIndirectReports) {
      return this.repository.find({
        where: { managerId, tenantId },
        relations: ['department', 'position'],
      });
    }

    // For indirect reports, we need to traverse the hierarchy
    const directReports = await this.repository.find({
      where: { managerId, tenantId },
      relations: ['department', 'position'],
    });

    const allReports = [...directReports];

    // Recursively find indirect reports
    for (const report of directReports) {
      const indirectReports = await this.findByManagerHierarchy(
        tenantId,
        report.id,
        true,
      );
      allReports.push(...indirectReports);
    }

    return allReports;
  }

  /**
   * Find employees with expiring documents
   */
  async findWithExpiringDocuments(
    tenantId: string,
    daysUntilExpiry: number,
  ): Promise<Employee[]> {
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + daysUntilExpiry);

    return this.repository
      .createQueryBuilder('employee')
      .leftJoinAndSelect('employee.documents', 'doc')
      .where('employee.tenantId = :tenantId', { tenantId })
      .andWhere('doc.expiryDate IS NOT NULL')
      .andWhere('doc.expiryDate <= :expiryDate', { expiryDate })
      .andWhere('doc.expiryDate > :today', { today: new Date() })
      .getMany();
  }

  /**
   * Find employees with birthdays in date range
   */
  async findWithBirthdays(
    tenantId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<Employee[]> {
    return this.repository
      .createQueryBuilder('employee')
      .where('employee.tenantId = :tenantId', { tenantId })
      .andWhere('employee.dateOfBirth IS NOT NULL')
      .andWhere(
        "EXTRACT(MONTH FROM employee.dateOfBirth) = EXTRACT(MONTH FROM :startDate) AND " +
        "EXTRACT(DAY FROM employee.dateOfBirth) BETWEEN EXTRACT(DAY FROM :startDate) AND EXTRACT(DAY FROM :endDate)",
        { startDate, endDate }
      )
      .getMany();
  }

  /**
   * Find employees with work anniversaries
   */
  async findWithWorkAnniversaries(
    tenantId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<Employee[]> {
    return this.repository
      .createQueryBuilder('employee')
      .where('employee.tenantId = :tenantId', { tenantId })
      .andWhere(
        "EXTRACT(MONTH FROM employee.dateOfJoining) = EXTRACT(MONTH FROM :startDate) AND " +
        "EXTRACT(DAY FROM employee.dateOfJoining) BETWEEN EXTRACT(DAY FROM :startDate) AND EXTRACT(DAY FROM :endDate)",
        { startDate, endDate }
      )
      .getMany();
  }

  /**
   * Find employees in probation period
   */
  async findInProbationPeriod(tenantId: string): Promise<Employee[]> {
    const today = new Date();

    return this.repository
      .createQueryBuilder('employee')
      .where('employee.tenantId = :tenantId', { tenantId })
      .andWhere('employee.probationEndDate IS NOT NULL')
      .andWhere('employee.probationEndDate > :today', { today })
      .andWhere('employee.status = :status', { status: EmployeeStatus.ACTIVE })
      .getMany();
  }

  /**
   * Get employee statistics by department
   */
  async getStatsByDepartment(tenantId: string): Promise<any[]> {
    return this.repository
      .createQueryBuilder('employee')
      .leftJoin('employee.department', 'dept')
      .select([
        'dept.id as departmentId',
        'dept.name as departmentName',
        'COUNT(employee.id) as totalEmployees',
        'COUNT(CASE WHEN employee.status = :activeStatus THEN 1 END) as activeEmployees',
        'COUNT(CASE WHEN employee.employmentType = :fullTime THEN 1 END) as fullTimeEmployees',
        'COUNT(CASE WHEN employee.employmentType = :partTime THEN 1 END) as partTimeEmployees',
        'AVG(employee.baseSalary) as averageSalary',
      ])
      .where('employee.tenantId = :tenantId', { tenantId })
      .groupBy('dept.id, dept.name')
      .setParameters({
        activeStatus: EmployeeStatus.ACTIVE,
        fullTime: EmploymentType.FULL_TIME,
        partTime: EmploymentType.PART_TIME,
      })
      .getRawMany();
  }

  /**
   * Create base query with common joins
   */
  private createBaseQuery(tenantId: string, include?: string[]): SelectQueryBuilder<Employee> {
    const queryBuilder = this.repository
      .createQueryBuilder('employee')
      .leftJoinAndSelect('employee.department', 'department')
      .leftJoinAndSelect('employee.position', 'position')
      .where('employee.tenantId = :tenantId', { tenantId });

    // Add optional joins based on include parameter
    if (include?.includes('manager')) {
      queryBuilder.leftJoinAndSelect('employee.manager', 'manager');
    }

    if (include?.includes('contacts')) {
      queryBuilder.leftJoinAndSelect('employee.contacts', 'contacts');
    }

    if (include?.includes('addresses')) {
      queryBuilder.leftJoinAndSelect('employee.addresses', 'addresses');
    }

    if (include?.includes('emergencyContacts')) {
      queryBuilder.leftJoinAndSelect('employee.emergencyContacts', 'emergencyContacts');
    }

    if (include?.includes('education')) {
      queryBuilder.leftJoinAndSelect('employee.education', 'education');
    }

    if (include?.includes('experience')) {
      queryBuilder.leftJoinAndSelect('employee.experience', 'experience');
    }

    if (include?.includes('skills')) {
      queryBuilder.leftJoinAndSelect('employee.skills', 'skills');
    }

    if (include?.includes('certifications')) {
      queryBuilder.leftJoinAndSelect('employee.certifications', 'certifications');
    }

    return queryBuilder;
  }

  /**
   * Apply filters to query builder
   */
  private applyFilters(
    queryBuilder: SelectQueryBuilder<Employee>,
    filters: EmployeeSearchFilters,
  ): void {
    const {
      search,
      status,
      employmentType,
      departmentId,
      positionId,
      managerId,
      gender,
      maritalStatus,
      workLocation,
      joiningDateFrom,
      joiningDateTo,
      minSalary,
      maxSalary,
      skills,
      activeOnly,
      probationOnly,
    } = filters;

    if (search) {
      queryBuilder.andWhere(
        new Brackets(qb => {
          qb.where('employee.firstName ILIKE :search', { search: `%${search}%` })
            .orWhere('employee.lastName ILIKE :search', { search: `%${search}%` })
            .orWhere('employee.email ILIKE :search', { search: `%${search}%` })
            .orWhere('employee.employeeId ILIKE :search', { search: `%${search}%` });
        })
      );
    }

    if (status) {
      queryBuilder.andWhere('employee.status = :status', { status });
    }

    if (employmentType) {
      queryBuilder.andWhere('employee.employmentType = :employmentType', { employmentType });
    }

    if (departmentId) {
      queryBuilder.andWhere('employee.departmentId = :departmentId', { departmentId });
    }

    if (positionId) {
      queryBuilder.andWhere('employee.positionId = :positionId', { positionId });
    }

    if (managerId) {
      queryBuilder.andWhere('employee.managerId = :managerId', { managerId });
    }

    if (gender) {
      queryBuilder.andWhere('employee.gender = :gender', { gender });
    }

    if (maritalStatus) {
      queryBuilder.andWhere('employee.maritalStatus = :maritalStatus', { maritalStatus });
    }

    if (workLocation) {
      queryBuilder.andWhere('employee.workLocation ILIKE :workLocation', {
        workLocation: `%${workLocation}%`,
      });
    }

    if (joiningDateFrom) {
      queryBuilder.andWhere('employee.dateOfJoining >= :joiningDateFrom', { joiningDateFrom });
    }

    if (joiningDateTo) {
      queryBuilder.andWhere('employee.dateOfJoining <= :joiningDateTo', { joiningDateTo });
    }

    if (minSalary) {
      queryBuilder.andWhere('employee.baseSalary >= :minSalary', { minSalary });
    }

    if (maxSalary) {
      queryBuilder.andWhere('employee.baseSalary <= :maxSalary', { maxSalary });
    }

    if (activeOnly) {
      queryBuilder.andWhere('employee.status = :activeStatus', {
        activeStatus: EmployeeStatus.ACTIVE,
      });
    }

    if (probationOnly) {
      queryBuilder.andWhere('employee.probationEndDate > :now', { now: new Date() });
    }

    if (skills && skills.length > 0) {
      queryBuilder
        .leftJoin('employee.skills', 'skill')
        .andWhere('skill.name IN (:...skills)', { skills });
    }
  }

  /**
   * Apply sorting to query builder
   */
  private applySorting(
    queryBuilder: SelectQueryBuilder<Employee>,
    sortBy?: string,
    sortOrder?: 'ASC' | 'DESC',
  ): void {
    const order = sortOrder || 'ASC';

    switch (sortBy) {
      case 'firstName':
        queryBuilder.orderBy('employee.firstName', order);
        break;
      case 'lastName':
        queryBuilder.orderBy('employee.lastName', order);
        break;
      case 'email':
        queryBuilder.orderBy('employee.email', order);
        break;
      case 'employeeId':
        queryBuilder.orderBy('employee.employeeId', order);
        break;
      case 'dateOfJoining':
        queryBuilder.orderBy('employee.dateOfJoining', order);
        break;
      case 'status':
        queryBuilder.orderBy('employee.status', order);
        break;
      case 'department':
        queryBuilder.orderBy('department.name', order);
        break;
      case 'position':
        queryBuilder.orderBy('position.title', order);
        break;
      default:
        queryBuilder.orderBy('employee.firstName', 'ASC');
    }
  }
}
