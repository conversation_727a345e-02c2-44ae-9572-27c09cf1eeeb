import type { NodePath } from '@babel/traverse';
import type { ArrayPattern, AssignmentPattern, Identifier, ObjectPattern, RestElement, TSParameterProperty } from '@babel/types';
type ParameterNodePath = NodePath<ArrayPattern | AssignmentPattern | Identifier | ObjectPattern | RestElement | TSParameterProperty>;
export default function getParameterName(parameterPath: ParameterNodePath): string;
export {};
