import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  ConflictException,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

import { User } from '@app/database/entities/user.entity';
import { UserRole } from '@app/common/enums/user-role.enum';
import { PasswordService } from './password.service';
import { TokenService } from './token.service';
import { SessionService } from './session.service';
import { MfaService } from './mfa.service';
import { AuditService } from './audit.service';
import {
  LoginRequest,
  LoginResponse,
  UserInfo,
  RefreshTokenRequest,
  RefreshTokenResponse,
  ChangePasswordRequest,
  ResetPasswordRequest,
  ResetPasswordConfirmRequest,
} from '../interfaces/auth.interface';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private configService: ConfigService,
    private passwordService: PasswordService,
    private tokenService: TokenService,
    private sessionService: SessionService,
    private mfaService: MfaService,
    private auditService: AuditService,
  ) {}

  /**
   * Validate user credentials
   */
  async validateUser(email: string, password: string): Promise<User | null> {
    const user = await this.userRepository.findOne({
      where: { email: email.toLowerCase() },
      select: ['id', 'email', 'password', 'role', 'status', 'tenantId', 'firstName', 'lastName'],
    });

    if (!user) {
      return null;
    }

    const isPasswordValid = await this.passwordService.validatePassword(
      password,
      user.password,
    );

    if (!isPasswordValid) {
      return null;
    }

    // Remove password from returned user object
    const { password: _, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  }

  /**
   * Validate user by ID
   */
  async validateUserById(userId: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { id: userId },
      select: ['id', 'email', 'role', 'status', 'tenantId', 'firstName', 'lastName'],
    });
  }

  /**
   * Find user by email
   */
  async findUserByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { email: email.toLowerCase() },
    });
  }

  /**
   * Login user
   */
  async login(
    loginRequest: LoginRequest,
    ip: string,
    userAgent: string,
  ): Promise<LoginResponse> {
    const { email, password, rememberMe = false, mfaCode } = loginRequest;

    // Validate user credentials
    const user = await this.validateUser(email, password);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if MFA is enabled
    const hasMfaEnabled = await this.mfaService.isMfaEnabled(user.id);
    
    if (hasMfaEnabled && !mfaCode) {
      // Return MFA token for second step
      const mfaToken = await this.tokenService.generateMfaToken(user.id);
      return {
        requiresMfa: true,
        mfaToken,
      } as LoginResponse;
    }

    if (hasMfaEnabled && mfaCode) {
      // Verify MFA code
      const isMfaValid = await this.mfaService.verifyMfaCode(user.id, mfaCode);
      if (!isMfaValid) {
        throw new UnauthorizedException('Invalid MFA code');
      }
    }

    // Create session
    const sessionId = await this.sessionService.createSession({
      userId: user.id,
      ip,
      userAgent,
      rememberMe,
    });

    // Generate tokens
    const { accessToken, refreshToken } = await this.tokenService.generateTokens({
      userId: user.id,
      email: user.email,
      role: user.role,
      tenantId: user.tenantId,
      sessionId,
    });

    // Update last login
    await this.userRepository.update(user.id, {
      lastLoginAt: new Date(),
    });

    // Log successful login
    await this.auditService.logAuthAttempt({
      userId: user.id,
      email: user.email,
      ip,
      userAgent,
      success: true,
      action: 'login',
      timestamp: new Date(),
    });

    return {
      accessToken,
      refreshToken,
      expiresIn: this.configService.get<number>('JWT_EXPIRES_IN_SECONDS', 86400),
      user: this.mapToUserInfo(user),
    };
  }

  /**
   * Refresh access token
   */
  async refreshToken(
    refreshRequest: RefreshTokenRequest,
    ip: string,
    userAgent: string,
  ): Promise<RefreshTokenResponse> {
    const { refreshToken } = refreshRequest;

    // Validate and decode refresh token
    const payload = await this.tokenService.validateRefreshToken(refreshToken);

    if (!payload) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    // Get user
    const user = await this.validateUserById(payload.userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Validate session
    const isValidSession = await this.sessionService.validateSession(
      user.id,
      payload.sessionId,
    );

    if (!isValidSession) {
      throw new UnauthorizedException('Invalid session');
    }

    // Generate new tokens
    const tokens = await this.tokenService.generateTokens({
      userId: user.id,
      email: user.email,
      role: user.role,
      tenantId: user.tenantId,
      sessionId: payload.sessionId,
    });

    // Revoke old refresh token
    await this.tokenService.revokeRefreshToken(payload.jti);

    return {
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      expiresIn: this.configService.get<number>('JWT_EXPIRES_IN_SECONDS', 86400),
    };
  }

  /**
   * Logout user
   */
  async logout(userId: string, sessionId: string): Promise<void> {
    // Revoke session
    await this.sessionService.revokeSession(sessionId);

    // Revoke all refresh tokens for this session
    await this.tokenService.revokeRefreshTokensBySession(userId, sessionId);

    // Log logout
    await this.auditService.logAuthAttempt({
      userId,
      action: 'logout',
      success: true,
      timestamp: new Date(),
    });
  }

  /**
   * Change user password
   */
  async changePassword(
    userId: string,
    changePasswordRequest: ChangePasswordRequest,
  ): Promise<void> {
    const { currentPassword, newPassword } = changePasswordRequest;

    // Get user with password
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['id', 'email', 'password'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Validate current password
    const isCurrentPasswordValid = await this.passwordService.validatePassword(
      currentPassword,
      user.password,
    );

    if (!isCurrentPasswordValid) {
      throw new BadRequestException('Current password is incorrect');
    }

    // Hash new password
    const hashedNewPassword = await this.passwordService.hashPassword(newPassword);

    // Update password
    await this.userRepository.update(userId, {
      password: hashedNewPassword,
      passwordChangedAt: new Date(),
    });

    // Revoke all sessions except current one
    await this.sessionService.revokeAllUserSessions(userId);

    // Log password change
    await this.auditService.logAuthAttempt({
      userId,
      action: 'password_change',
      success: true,
      timestamp: new Date(),
    });
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(
    resetRequest: ResetPasswordRequest,
  ): Promise<void> {
    const { email } = resetRequest;

    const user = await this.findUserByEmail(email);
    if (!user) {
      // Don't reveal if email exists
      return;
    }

    // Generate reset token
    const resetToken = await this.tokenService.generatePasswordResetToken(user.id);

    // TODO: Send email with reset token
    // await this.emailService.sendPasswordResetEmail(user.email, resetToken);

    // Log password reset request
    await this.auditService.logAuthAttempt({
      userId: user.id,
      email: user.email,
      action: 'password_reset_request',
      success: true,
      timestamp: new Date(),
    });
  }

  /**
   * Confirm password reset
   */
  async confirmPasswordReset(
    confirmRequest: ResetPasswordConfirmRequest,
  ): Promise<void> {
    const { token, newPassword } = confirmRequest;

    // Validate reset token
    const payload = await this.tokenService.validatePasswordResetToken(token);

    if (!payload) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    // Get user
    const user = await this.validateUserById(payload.userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Hash new password
    const hashedPassword = await this.passwordService.hashPassword(newPassword);

    // Update password
    await this.userRepository.update(user.id, {
      password: hashedPassword,
      passwordChangedAt: new Date(),
    });

    // Revoke all sessions
    await this.sessionService.revokeAllUserSessions(user.id);

    // Revoke reset token
    await this.tokenService.revokePasswordResetToken(token);

    // Log password reset
    await this.auditService.logAuthAttempt({
      userId: user.id,
      action: 'password_reset_confirm',
      success: true,
      timestamp: new Date(),
    });
  }

  /**
   * Check if account is locked
   */
  async isAccountLocked(userId: string): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['failedLoginAttempts', 'lockedUntil'],
    });

    if (!user) {
      return false;
    }

    const maxAttempts = this.configService.get<number>('MAX_LOGIN_ATTEMPTS', 5);
    const lockDuration = this.configService.get<number>('ACCOUNT_LOCK_DURATION', 900000); // 15 minutes

    if (user.failedLoginAttempts >= maxAttempts) {
      if (user.lockedUntil && user.lockedUntil > new Date()) {
        return true;
      }
    }

    return false;
  }

  /**
   * Increment failed login attempts
   */
  async incrementFailedLoginAttempts(userId: string): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      select: ['failedLoginAttempts'],
    });

    if (!user) {
      return;
    }

    const maxAttempts = this.configService.get<number>('MAX_LOGIN_ATTEMPTS', 5);
    const lockDuration = this.configService.get<number>('ACCOUNT_LOCK_DURATION', 900000); // 15 minutes

    const newAttempts = (user.failedLoginAttempts || 0) + 1;
    const updateData: any = { failedLoginAttempts: newAttempts };

    if (newAttempts >= maxAttempts) {
      updateData.lockedUntil = new Date(Date.now() + lockDuration);
    }

    await this.userRepository.update(userId, updateData);
  }

  /**
   * Reset failed login attempts
   */
  async resetFailedLoginAttempts(userId: string): Promise<void> {
    await this.userRepository.update(userId, {
      failedLoginAttempts: 0,
      lockedUntil: null,
    });
  }

  /**
   * Map user entity to user info
   */
  private mapToUserInfo(user: User): UserInfo {
    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      status: user.status,
      tenantId: user.tenantId,
      permissions: user.permissions || [],
      lastLoginAt: user.lastLoginAt,
      profilePicture: user.profilePicture,
    };
  }
}
