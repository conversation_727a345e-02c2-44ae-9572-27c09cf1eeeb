import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Users,
  TrendingUp,
  DollarSign,
  Target,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  <PERSON><PERSON>hart,
  Activity,
  Award,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useAuth } from '@/hooks/use-auth';
import { dashboardService } from '@/services/dashboard-service';

interface DashboardMetrics {
  employees: {
    total: number;
    active: number;
    newHires: number;
    turnover: number;
  };
  performance: {
    averageRating: number;
    completedReviews: number;
    pendingReviews: number;
    goalAchievementRate: number;
  };
  payroll: {
    totalPayroll: number;
    averageSalary: number;
    pendingPayments: number;
    payrollCosts: number;
  };
  attendance: {
    presentToday: number;
    onLeave: number;
    lateArrivals: number;
    averageHours: number;
  };
}

interface RecentActivity {
  id: string;
  type: 'employee' | 'performance' | 'payroll' | 'leave';
  title: string;
  description: string;
  timestamp: string;
  status: 'success' | 'warning' | 'error' | 'info';
}

export default function DashboardPage() {
  const { user } = useAuth();
  const [timeRange, setTimeRange] = useState('30d');
  const [selectedTab, setSelectedTab] = useState('overview');

  const {
    data: metrics,
    isLoading: metricsLoading,
    error: metricsError,
  } = useQuery({
    queryKey: ['dashboard-metrics', timeRange],
    queryFn: () => dashboardService.getMetrics(timeRange),
    refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
  });

  const {
    data: activities,
    isLoading: activitiesLoading,
  } = useQuery({
    queryKey: ['dashboard-activities'],
    queryFn: () => dashboardService.getRecentActivities(),
    refetchInterval: 2 * 60 * 1000, // Refresh every 2 minutes
  });

  const {
    data: chartData,
    isLoading: chartLoading,
  } = useQuery({
    queryKey: ['dashboard-charts', timeRange],
    queryFn: () => dashboardService.getChartData(timeRange),
  });

  if (metricsLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (metricsError) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Failed to load dashboard</h3>
          <p className="text-muted-foreground">Please try refreshing the page</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Welcome back, {user?.firstName}!
          </h1>
          <p className="text-muted-foreground">
            Here's what's happening with your organization today.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.employees.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+{metrics?.employees.newHires || 0}</span> new hires this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Performance Score</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics?.performance.averageRating?.toFixed(1) || '0.0'}
            </div>
            <p className="text-xs text-muted-foreground">
              {metrics?.performance.completedReviews || 0} reviews completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Payroll</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${(metrics?.payroll.totalPayroll || 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {metrics?.payroll.pendingPayments || 0} pending payments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Goal Achievement</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics?.performance.goalAchievementRate?.toFixed(0) || 0}%
            </div>
            <Progress 
              value={metrics?.performance.goalAchievementRate || 0} 
              className="mt-2"
            />
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="attendance">Attendance</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            {/* Recent Activities */}
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Recent Activities</CardTitle>
              </CardHeader>
              <CardContent>
                {activitiesLoading ? (
                  <div className="flex items-center justify-center h-[200px]">
                    <LoadingSpinner />
                  </div>
                ) : (
                  <div className="space-y-4">
                    {activities?.slice(0, 5).map((activity: RecentActivity) => (
                      <div key={activity.id} className="flex items-center space-x-4">
                        <div className={`w-2 h-2 rounded-full ${
                          activity.status === 'success' ? 'bg-green-500' :
                          activity.status === 'warning' ? 'bg-yellow-500' :
                          activity.status === 'error' ? 'bg-red-500' : 'bg-blue-500'
                        }`} />
                        <div className="flex-1 space-y-1">
                          <p className="text-sm font-medium leading-none">
                            {activity.title}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {activity.description}
                          </p>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {new Date(activity.timestamp).toLocaleDateString()}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Today's Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Present Today</span>
                  </div>
                  <Badge variant="secondary">
                    {metrics?.attendance.presentToday || 0}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-blue-500" />
                    <span className="text-sm">On Leave</span>
                  </div>
                  <Badge variant="secondary">
                    {metrics?.attendance.onLeave || 0}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm">Late Arrivals</span>
                  </div>
                  <Badge variant="secondary">
                    {metrics?.attendance.lateArrivals || 0}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Activity className="h-4 w-4 text-purple-500" />
                    <span className="text-sm">Avg. Hours</span>
                  </div>
                  <Badge variant="secondary">
                    {metrics?.attendance.averageHours?.toFixed(1) || '0.0'}h
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Performance Reviews</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Completed Reviews</span>
                    <Badge variant="secondary">
                      {metrics?.performance.completedReviews || 0}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Pending Reviews</span>
                    <Badge variant="destructive">
                      {metrics?.performance.pendingReviews || 0}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Completion Rate</span>
                      <span>
                        {((metrics?.performance.completedReviews || 0) / 
                          ((metrics?.performance.completedReviews || 0) + (metrics?.performance.pendingReviews || 0)) * 100
                        ).toFixed(0)}%
                      </span>
                    </div>
                    <Progress 
                      value={
                        ((metrics?.performance.completedReviews || 0) / 
                          ((metrics?.performance.completedReviews || 0) + (metrics?.performance.pendingReviews || 0)) * 100
                        ) || 0
                      }
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Goal Tracking</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary">
                      {metrics?.performance.goalAchievementRate?.toFixed(0) || 0}%
                    </div>
                    <p className="text-sm text-muted-foreground">Goal Achievement Rate</p>
                  </div>
                  <Progress value={metrics?.performance.goalAchievementRate || 0} />
                  <div className="flex items-center justify-center space-x-2">
                    <Award className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm">Top performing department</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="attendance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Attendance Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Present</span>
                    <span className="font-medium">{metrics?.attendance.presentToday || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">On Leave</span>
                    <span className="font-medium">{metrics?.attendance.onLeave || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Late</span>
                    <span className="font-medium">{metrics?.attendance.lateArrivals || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Working Hours</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {metrics?.attendance.averageHours?.toFixed(1) || '0.0'}
                  </div>
                  <p className="text-sm text-muted-foreground">Average hours today</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Attendance Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {(((metrics?.attendance.presentToday || 0) / (metrics?.employees.active || 1)) * 100).toFixed(0)}%
                  </div>
                  <p className="text-sm text-muted-foreground">Present today</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5" />
                  <span>Employee Growth</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {chartLoading ? (
                  <div className="flex items-center justify-center h-[200px]">
                    <LoadingSpinner />
                  </div>
                ) : (
                  <div className="h-[200px] flex items-center justify-center text-muted-foreground">
                    Chart component would go here
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <PieChart className="h-5 w-5" />
                  <span>Department Distribution</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {chartLoading ? (
                  <div className="flex items-center justify-center h-[200px]">
                    <LoadingSpinner />
                  </div>
                ) : (
                  <div className="h-[200px] flex items-center justify-center text-muted-foreground">
                    Chart component would go here
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
