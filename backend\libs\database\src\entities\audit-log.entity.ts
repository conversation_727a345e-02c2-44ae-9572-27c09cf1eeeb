import {
  Entity,
  Column,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { User } from './user.entity';
import { AuditAction } from '@app/common/enums/status.enum';

export enum AuditLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical',
}

export enum AuditCategory {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  USER_MANAGEMENT = 'user_management',
  EMPLOYEE_MANAGEMENT = 'employee_management',
  PAYROLL = 'payroll',
  PERFORMANCE = 'performance',
  DOCUMENT = 'document',
  SYSTEM = 'system',
  DATA_ACCESS = 'data_access',
  CONFIGURATION = 'configuration',
}

@Entity('audit_logs')
@Index(['userId', 'createdAt'])
@Index(['action', 'createdAt'])
@Index(['category', 'createdAt'])
@Index(['level', 'createdAt'])
@Index(['entityType', 'entityId'])
export class AuditLog extends TenantAwareEntity {
  @Column({
    type: 'enum',
    enum: AuditAction,
    comment: 'Action that was performed',
  })
  @Index()
  action: AuditAction;

  @Column({
    type: 'enum',
    enum: AuditCategory,
    comment: 'Category of the audit event',
  })
  @Index()
  category: AuditCategory;

  @Column({
    type: 'enum',
    enum: AuditLevel,
    default: AuditLevel.INFO,
    comment: 'Severity level of the audit event',
  })
  @Index()
  level: AuditLevel;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Description of the action performed',
  })
  description: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who performed the action',
  })
  userId?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Type of entity that was affected',
  })
  @Index()
  entityType?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'ID of the entity that was affected',
  })
  @Index()
  entityId?: string;

  @Column({
    type: 'varchar',
    length: 45,
    nullable: true,
    comment: 'IP address of the user',
  })
  ipAddress?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'User agent string',
  })
  userAgent?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Session ID if applicable',
  })
  sessionId?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Request ID for tracing',
  })
  requestId?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Previous values before the change',
  })
  oldValues?: Record<string, any>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'New values after the change',
  })
  newValues?: Record<string, any>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Additional metadata about the event',
  })
  metadata?: Record<string, any>;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Source system or service',
  })
  source?: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Geographic location',
  })
  location?: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this event was flagged as suspicious',
  })
  isSuspicious: boolean;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Risk score or assessment',
  })
  riskAssessment?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Compliance tags for regulatory requirements',
  })
  complianceTags?: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Data retention expiry date',
  })
  retentionExpiresAt?: Date;

  // Relationships
  @ManyToOne(() => User, { eager: false, nullable: true })
  @JoinColumn({ name: 'user_id' })
  user?: User;

  // Virtual properties
  get isRetentionExpired(): boolean {
    return this.retentionExpiresAt ? this.retentionExpiresAt < new Date() : false;
  }

  get eventSummary(): string {
    const userInfo = this.userId ? `User ${this.userId}` : 'System';
    const entityInfo = this.entityType && this.entityId ? ` on ${this.entityType} ${this.entityId}` : '';
    return `${userInfo} performed ${this.action}${entityInfo}`;
  }

  get hasDataChanges(): boolean {
    return !!(this.oldValues || this.newValues);
  }
}
