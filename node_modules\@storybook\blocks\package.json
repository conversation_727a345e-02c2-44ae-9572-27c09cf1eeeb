{"name": "@storybook/blocks", "version": "8.6.14", "description": "Storybook Doc Blocks", "keywords": ["storybook"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/lib/blocks", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/lib/blocks"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/bundle.ts"}, "dependencies": {"@storybook/icons": "^1.2.12", "ts-dedent": "^2.0.0"}, "devDependencies": {"@storybook/addon-actions": "8.6.14", "@storybook/react": "8.6.14", "@storybook/test": "8.6.14", "@types/color-convert": "^2.0.0", "color-convert": "^2.0.1", "es-toolkit": "^1.22.0", "github-slugger": "^2.0.0", "markdown-to-jsx": "^7.7.2", "memoizerific": "^1.11.3", "polished": "^4.2.2", "react-colorful": "^5.1.2", "telejson": "^7.2.0", "tocbot": "^4.20.1"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "storybook": "^8.6.14"}, "peerDependenciesMeta": {"react": {"optional": true}, "react-dom": {"optional": true}}, "publishConfig": {"access": "public"}, "bundler": {"entries": ["./src/index.ts"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae16"}