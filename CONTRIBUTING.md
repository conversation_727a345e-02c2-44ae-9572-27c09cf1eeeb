# Contributing to PeopleNest HRMS

Thank you for your interest in contributing to PeopleNest HRMS! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- Node.js 20+ and npm 10+
- Python 3.11+
- <PERSON>er and Docker Compose
- PostgreSQL 15+
- Redis 7+

### Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/peoplenest-hrms.git
   cd peoplenest-hrms
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start development environment**
   ```bash
   npm run infra:up
   npm run dev
   ```

## 📋 Development Guidelines

### Code Style

- **TypeScript**: Use strict TypeScript with proper typing
- **ESLint**: Follow the configured ESLint rules
- **Prettier**: Code formatting is enforced
- **Naming**: Use descriptive names for variables, functions, and components

### Commit Convention

We use [Conventional Commits](https://www.conventionalcommits.org/):

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(auth): add multi-factor authentication
fix(payroll): resolve tax calculation error
docs(api): update authentication endpoints
test(employee): add unit tests for employee service
```

### Branch Naming

- `feature/description` - New features
- `bugfix/description` - Bug fixes
- `hotfix/description` - Critical fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring

## 🧪 Testing

### Running Tests

```bash
# Backend tests
npm run backend:test
npm run backend:test:e2e

# Frontend tests
npm run frontend:test

# AI services tests
cd ai-services && python -m pytest

# All tests
npm run test
```

### Test Coverage

- Backend: Minimum 85% coverage
- Frontend: Minimum 80% coverage
- Write tests for all new features and bug fixes

### Test Types

1. **Unit Tests**: Test individual functions/components
2. **Integration Tests**: Test service interactions
3. **E2E Tests**: Test complete user workflows
4. **Performance Tests**: Test system performance

## 🏗️ Architecture Guidelines

### Backend (NestJS)

- Use dependency injection
- Implement proper error handling
- Follow SOLID principles
- Use DTOs for data validation
- Implement comprehensive logging

### Frontend (React)

- Use functional components with hooks
- Implement proper state management
- Follow component composition patterns
- Use TypeScript strictly
- Implement proper error boundaries

### AI Services (Python)

- Use async/await patterns
- Implement proper model versioning
- Use structured logging
- Follow PEP 8 style guide
- Implement proper error handling

## 🔒 Security Guidelines

- Never commit secrets or credentials
- Use environment variables for configuration
- Implement proper input validation
- Follow OWASP security guidelines
- Use secure coding practices

## 📝 Documentation

- Update README.md for significant changes
- Document new APIs in OpenAPI/Swagger
- Add inline code comments for complex logic
- Update architecture diagrams when needed

## 🐛 Bug Reports

When reporting bugs, include:

1. **Description**: Clear description of the issue
2. **Steps to Reproduce**: Detailed steps
3. **Expected Behavior**: What should happen
4. **Actual Behavior**: What actually happens
5. **Environment**: OS, browser, versions
6. **Screenshots**: If applicable

## 💡 Feature Requests

When requesting features:

1. **Use Case**: Describe the problem you're solving
2. **Proposed Solution**: Your suggested approach
3. **Alternatives**: Other solutions considered
4. **Impact**: Who would benefit from this feature

## 🔄 Pull Request Process

1. **Fork** the repository
2. **Create** a feature branch
3. **Make** your changes
4. **Test** thoroughly
5. **Update** documentation
6. **Submit** pull request

### PR Requirements

- [ ] Tests pass
- [ ] Code coverage maintained
- [ ] Documentation updated
- [ ] No merge conflicts
- [ ] Follows coding standards
- [ ] Security review passed

### PR Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
```

## 🎯 Performance Guidelines

- Optimize database queries
- Implement proper caching
- Use lazy loading where appropriate
- Monitor bundle sizes
- Implement proper pagination

## 🌍 Internationalization

- Use i18n for all user-facing text
- Support RTL languages
- Consider cultural differences
- Test with different locales

## ♿ Accessibility

- Follow WCAG 2.1 AA guidelines
- Use semantic HTML
- Implement proper ARIA labels
- Test with screen readers
- Ensure keyboard navigation

## 📊 Monitoring

- Add proper logging
- Implement metrics collection
- Use structured logging
- Monitor performance
- Set up alerts

## 🤝 Code Review

### As a Reviewer

- Be constructive and respectful
- Focus on code quality and standards
- Check for security issues
- Verify tests are adequate
- Ensure documentation is updated

### As an Author

- Respond to feedback promptly
- Make requested changes
- Explain complex decisions
- Keep PRs focused and small
- Test changes thoroughly

## 📞 Getting Help

- **Discord**: [Community Server](https://discord.gg/peoplenest)
- **Email**: <EMAIL>
- **Documentation**: [docs.peoplenest.com](https://docs.peoplenest.com)
- **Issues**: GitHub Issues

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to PeopleNest HRMS! 🎉
