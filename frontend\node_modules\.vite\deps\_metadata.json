{"hash": "b31a57cf", "configHash": "6c1c09a6", "lockfileHash": "a3d3228f", "browserHash": "f7638c54", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "0fa65ba8", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "dfae8e62", "needsInterop": true}, "react-router-dom": {"src": "../../../../node_modules/react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "58e409a4", "needsInterop": false}, "@reduxjs/toolkit": {"src": "../../../../node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "7e508d46", "needsInterop": false}, "react-redux": {"src": "../../../../node_modules/react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "2b2945c0", "needsInterop": false}, "@apollo/client": {"src": "../../../../node_modules/@apollo/client/index.js", "file": "@apollo_client.js", "fileHash": "30923e96", "needsInterop": false}, "graphql": {"src": "../../../../node_modules/graphql/index.mjs", "file": "graphql.js", "fileHash": "0ff74c59", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "deb749aa", "needsInterop": true}, "@apollo/client/link/context": {"src": "../../../../node_modules/@apollo/client/link/context/index.js", "file": "@apollo_client_link_context.js", "fileHash": "8d455b04", "needsInterop": false}, "@apollo/client/link/error": {"src": "../../../../node_modules/@apollo/client/link/error/index.js", "file": "@apollo_client_link_error.js", "fileHash": "8e2c5805", "needsInterop": false}, "@apollo/client/link/retry": {"src": "../../../../node_modules/@apollo/client/link/retry/index.js", "file": "@apollo_client_link_retry.js", "fileHash": "24f3b021", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../../../node_modules/@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "337dea94", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../../../node_modules/@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "8ec9bf64", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "948f4ed3", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../../../node_modules/@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "1b233972", "needsInterop": false}, "@tanstack/react-query": {"src": "../../../../node_modules/@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "90db8fda", "needsInterop": false}, "@tanstack/react-query-devtools": {"src": "../../../../node_modules/@tanstack/react-query-devtools/build/modern/index.js", "file": "@tanstack_react-query-devtools.js", "fileHash": "ffde7dd3", "needsInterop": false}, "@tanstack/react-table": {"src": "../../../../node_modules/@tanstack/react-table/build/lib/index.mjs", "file": "@tanstack_react-table.js", "fileHash": "90dd64c0", "needsInterop": false}, "class-variance-authority": {"src": "../../../../node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "edeb5763", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "4c44ed1a", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "694cd9a2", "needsInterop": false}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "33f31a2b", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "d8ede162", "needsInterop": true}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "b2634f21", "needsInterop": false}, "axios": {"src": "../../../../node_modules/axios/index.js", "file": "axios.js", "fileHash": "5013d24e", "needsInterop": false}}, "chunks": {"HH7B3BHX-Q4YOC5WZ": {"file": "HH7B3BHX-Q4YOC5WZ.js"}, "JZI2RDCT-DWJ3VECO": {"file": "JZI2RDCT-DWJ3VECO.js"}, "chunk-LBMSAYCB": {"file": "chunk-LBMSAYCB.js"}, "chunk-MKYRKYXK": {"file": "chunk-MKYRKYXK.js"}, "chunk-TA4T2L7H": {"file": "chunk-TA4T2L7H.js"}, "chunk-K7WKLXQ3": {"file": "chunk-K7WKLXQ3.js"}, "chunk-CKPZGGGH": {"file": "chunk-CKPZGGGH.js"}, "chunk-33AWDYQR": {"file": "chunk-33AWDYQR.js"}, "chunk-WLVWOJ7Z": {"file": "chunk-WLVWOJ7Z.js"}, "chunk-VXCDF2R5": {"file": "chunk-VXCDF2R5.js"}, "chunk-6I5JPKR6": {"file": "chunk-6I5JPKR6.js"}, "chunk-G4GORMM2": {"file": "chunk-G4GORMM2.js"}, "chunk-AIZAQUPQ": {"file": "chunk-AIZAQUPQ.js"}, "chunk-BXIS6N7B": {"file": "chunk-BXIS6N7B.js"}, "chunk-76T5642S": {"file": "chunk-76T5642S.js"}, "chunk-UJHYEV4N": {"file": "chunk-UJHYEV4N.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}