import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { LoggerService } from './services/logger.service';
import { UtilsService } from './services/utils.service';
import { ValidationService } from './services/validation.service';
import { CryptoService } from './services/crypto.service';
import { DateService } from './services/date.service';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    LoggerService,
    UtilsService,
    ValidationService,
    CryptoService,
    DateService,
  ],
  exports: [
    LoggerService,
    UtilsService,
    ValidationService,
    CryptoService,
    DateService,
  ],
})
export class CommonModule {}
