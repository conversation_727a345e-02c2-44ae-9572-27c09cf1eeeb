import {
  IsEmail,
  IsString,
  IsOptional,
  IsEnum,
  IsDateString,
  IsUUID,
  IsPhoneNumber,
  IsDecimal,
  IsObject,
  IsArray,
  ValidateNested,
  IsBoolean,
  MinLength,
  MaxLength,
  IsNotEmpty,
  IsNumberString,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  EmployeeStatus,
  EmploymentType,
  Gender,
  MaritalStatus,
} from '@app/common/enums/status.enum';

export class CreateEmployeeContactDto {
  @ApiProperty({ description: 'Contact type', enum: ['phone', 'email', 'emergency', 'work', 'personal'] })
  @IsEnum(['phone', 'email', 'emergency', 'work', 'personal'])
  type: string;

  @ApiProperty({ description: 'Contact value' })
  @IsString()
  @IsNotEmpty()
  value: string;

  @ApiPropertyOptional({ description: 'Contact label' })
  @IsOptional()
  @IsString()
  label?: string;

  @ApiPropertyOptional({ description: 'Is primary contact', default: false })
  @IsOptional()
  @IsBoolean()
  isPrimary?: boolean;
}

export class CreateEmployeeAddressDto {
  @ApiProperty({ description: 'Address type', enum: ['home', 'work', 'mailing', 'emergency'] })
  @IsEnum(['home', 'work', 'mailing', 'emergency'])
  type: string;

  @ApiProperty({ description: 'Street address line 1' })
  @IsString()
  @IsNotEmpty()
  addressLine1: string;

  @ApiPropertyOptional({ description: 'Street address line 2' })
  @IsOptional()
  @IsString()
  addressLine2?: string;

  @ApiProperty({ description: 'City' })
  @IsString()
  @IsNotEmpty()
  city: string;

  @ApiPropertyOptional({ description: 'State/Province' })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiProperty({ description: 'Postal/ZIP code' })
  @IsString()
  @IsNotEmpty()
  postalCode: string;

  @ApiProperty({ description: 'Country' })
  @IsString()
  @IsNotEmpty()
  country: string;

  @ApiPropertyOptional({ description: 'Is primary address', default: false })
  @IsOptional()
  @IsBoolean()
  isPrimary?: boolean;
}

export class CreateEmployeeEmergencyContactDto {
  @ApiProperty({ description: 'Emergency contact name' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Relationship to employee' })
  @IsString()
  @IsNotEmpty()
  relationship: string;

  @ApiProperty({ description: 'Phone number' })
  @IsPhoneNumber()
  phoneNumber: string;

  @ApiPropertyOptional({ description: 'Email address' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ description: 'Is primary emergency contact', default: false })
  @IsOptional()
  @IsBoolean()
  isPrimary?: boolean;
}

export class CreateEmployeeWorkScheduleDto {
  @ApiPropertyOptional({ description: 'Hours per week' })
  @IsOptional()
  @IsNumberString()
  hoursPerWeek?: number;

  @ApiPropertyOptional({ description: 'Work days', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  workDays?: string[];

  @ApiPropertyOptional({ description: 'Start time (HH:mm)' })
  @IsOptional()
  @IsString()
  startTime?: string;

  @ApiPropertyOptional({ description: 'End time (HH:mm)' })
  @IsOptional()
  @IsString()
  endTime?: string;

  @ApiPropertyOptional({ description: 'Is flexible schedule', default: false })
  @IsOptional()
  @IsBoolean()
  isFlexible?: boolean;
}

export class CreateEmployeeDto {
  @ApiProperty({ description: 'Employee ID (auto-generated if not provided)' })
  @IsOptional()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  employeeId?: string;

  @ApiProperty({ description: 'Employee email address' })
  @IsEmail()
  @Transform(({ value }) => value?.toLowerCase()?.trim())
  email: string;

  @ApiProperty({ description: 'First name' })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(100)
  firstName: string;

  @ApiPropertyOptional({ description: 'Middle name' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  middleName?: string;

  @ApiProperty({ description: 'Last name' })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(100)
  lastName: string;

  @ApiPropertyOptional({ description: 'Preferred name' })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  preferredName?: string;

  @ApiPropertyOptional({ description: 'Date of birth (YYYY-MM-DD)' })
  @IsOptional()
  @IsDateString()
  dateOfBirth?: string;

  @ApiPropertyOptional({ description: 'Gender', enum: Gender })
  @IsOptional()
  @IsEnum(Gender)
  gender?: Gender;

  @ApiPropertyOptional({ description: 'Marital status', enum: MaritalStatus })
  @IsOptional()
  @IsEnum(MaritalStatus)
  maritalStatus?: MaritalStatus;

  @ApiPropertyOptional({ description: 'Nationality' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  nationality?: string;

  @ApiPropertyOptional({ description: 'Phone number' })
  @IsOptional()
  @IsPhoneNumber()
  phoneNumber?: string;

  @ApiPropertyOptional({ description: 'Personal phone number' })
  @IsOptional()
  @IsPhoneNumber()
  personalPhoneNumber?: string;

  @ApiProperty({ description: 'Employee status', enum: EmployeeStatus, default: EmployeeStatus.ACTIVE })
  @IsOptional()
  @IsEnum(EmployeeStatus)
  status?: EmployeeStatus;

  @ApiProperty({ description: 'Employment type', enum: EmploymentType, default: EmploymentType.FULL_TIME })
  @IsEnum(EmploymentType)
  employmentType: EmploymentType;

  @ApiProperty({ description: 'Date of joining (YYYY-MM-DD)' })
  @IsDateString()
  dateOfJoining: string;

  @ApiPropertyOptional({ description: 'Probation end date (YYYY-MM-DD)' })
  @IsOptional()
  @IsDateString()
  probationEndDate?: string;

  @ApiProperty({ description: 'Department ID' })
  @IsUUID()
  departmentId: string;

  @ApiProperty({ description: 'Position ID' })
  @IsUUID()
  positionId: string;

  @ApiPropertyOptional({ description: 'Manager employee ID' })
  @IsOptional()
  @IsUUID()
  managerId?: string;

  @ApiPropertyOptional({ description: 'Base salary' })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  baseSalary?: number;

  @ApiPropertyOptional({ description: 'Salary currency', default: 'USD' })
  @IsOptional()
  @IsString()
  @MaxLength(10)
  salaryCurrency?: string;

  @ApiPropertyOptional({ description: 'Salary frequency', default: 'monthly' })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  salaryFrequency?: string;

  @ApiPropertyOptional({ description: 'Work location' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  workLocation?: string;

  @ApiPropertyOptional({ description: 'Time zone' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  timeZone?: string;

  @ApiPropertyOptional({ description: 'Work schedule configuration' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateEmployeeWorkScheduleDto)
  workSchedule?: CreateEmployeeWorkScheduleDto;

  @ApiPropertyOptional({ description: 'Associated user account ID' })
  @IsOptional()
  @IsUUID()
  userId?: string;

  @ApiPropertyOptional({ description: 'Employee contacts', type: [CreateEmployeeContactDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateEmployeeContactDto)
  contacts?: CreateEmployeeContactDto[];

  @ApiPropertyOptional({ description: 'Employee addresses', type: [CreateEmployeeAddressDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateEmployeeAddressDto)
  addresses?: CreateEmployeeAddressDto[];

  @ApiPropertyOptional({ description: 'Emergency contacts', type: [CreateEmployeeEmergencyContactDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateEmployeeEmergencyContactDto)
  emergencyContacts?: CreateEmployeeEmergencyContactDto[];

  @ApiPropertyOptional({ description: 'Additional notes' })
  @IsOptional()
  @IsString()
  notes?: string;
}
