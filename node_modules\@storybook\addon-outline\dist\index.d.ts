import * as core_dist_types from 'storybook/internal/types';

interface OutlineParameters {
    /**
     * Outline configuration
     *
     * @see https://storybook.js.org/docs/essentials/measure-and-outline#parameters
     */
    outline: {
        /** Remove the addon panel and disable the addon's behavior */
        disable?: boolean;
    };
}

declare const _default: () => core_dist_types.ProjectAnnotations<core_dist_types.Renderer>;

export { OutlineParameters, _default as default };
