import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as os from 'os';

@Injectable()
export class AppService {
  private readonly startTime = Date.now();

  constructor(private readonly configService: ConfigService) {}

  getAppInfo() {
    return {
      name: this.configService.get('APP_NAME', 'PeopleNest HRMS API'),
      version: this.configService.get('APP_VERSION', '1.0.0'),
      description: 'AI-Enabled Enterprise Human Resource Management System',
      environment: this.configService.get('NODE_ENV', 'development'),
      timestamp: new Date().toISOString(),
      uptime: Math.floor((Date.now() - this.startTime) / 1000),
      status: 'healthy',
      features: {
        authentication: true,
        multiTenant: this.configService.get('MULTI_TENANT_ENABLED') === 'true',
        aiServices: this.configService.get('FEATURE_AI_RESUME_PARSING') === 'true',
        payrollProcessing: this.configService.get('FEATURE_MULTI_COUNTRY_PAYROLL') === 'true',
        advancedReporting: this.configService.get('FEATURE_ADVANCED_REPORTING') === 'true',
        mobileApp: this.configService.get('FEATURE_MOBILE_APP') === 'true',
        slackIntegration: this.configService.get('FEATURE_SLACK_INTEGRATION') === 'true',
        teamsIntegration: this.configService.get('FEATURE_TEAMS_INTEGRATION') === 'true',
      },
      endpoints: {
        api: `/${this.configService.get('API_PREFIX', 'api/v1')}`,
        docs: `/${this.configService.get('API_PREFIX', 'api/v1')}/docs`,
        graphql: '/graphql',
        health: `/${this.configService.get('API_PREFIX', 'api/v1')}/health`,
      },
    };
  }

  getVersion() {
    return {
      version: this.configService.get('APP_VERSION', '1.0.0'),
      buildDate: new Date().toISOString(),
      gitCommit: process.env.GIT_COMMIT || 'unknown',
      nodeVersion: process.version,
      apiVersion: 'v1',
    };
  }

  getSystemInfo() {
    const memoryUsage = process.memoryUsage();
    const cpus = os.cpus();

    return {
      nodeVersion: process.version,
      platform: os.platform(),
      architecture: os.arch(),
      hostname: os.hostname(),
      uptime: {
        process: Math.floor(process.uptime()),
        system: Math.floor(os.uptime()),
      },
      memory: {
        used: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
        external: memoryUsage.external,
        rss: memoryUsage.rss,
        free: os.freemem(),
        totalSystem: os.totalmem(),
      },
      cpu: {
        model: cpus[0]?.model || 'Unknown',
        cores: cpus.length,
        loadAverage: os.loadavg(),
      },
      environment: {
        nodeEnv: this.configService.get('NODE_ENV'),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        locale: Intl.DateTimeFormat().resolvedOptions().locale,
      },
      database: {
        postgres: {
          host: this.configService.get('DATABASE_HOST'),
          port: this.configService.get('DATABASE_PORT'),
          database: this.configService.get('DATABASE_NAME'),
        },
        mongodb: {
          connected: true, // This would be checked dynamically in a real implementation
        },
        redis: {
          host: this.configService.get('REDIS_HOST'),
          port: this.configService.get('REDIS_PORT'),
        },
      },
      features: {
        caching: true,
        rateLimiting: true,
        compression: true,
        security: true,
        monitoring: true,
        logging: true,
      },
    };
  }
}
