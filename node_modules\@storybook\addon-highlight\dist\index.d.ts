import * as core_dist_types from 'storybook/internal/types';

declare const HIGHLIGHT = "storybook/highlight/add";
declare const RESET_HIGHLIGHT = "storybook/highlight/reset";

interface HighlightParameters {
    /**
     * Highlight configuration
     *
     * @see https://storybook.js.org/docs/essentials/highlight#parameters
     */
    highlight: {
        /** Remove the addon panel and disable the addon's behavior */
        disable?: boolean;
    };
}

declare const _default: () => core_dist_types.ProjectAnnotations<core_dist_types.Renderer>;

export { HIGHLIGHT, HighlightParameters, RESET_HIGHLIGHT, _default as default };
