import {
  Entity,
  Column,
  Index,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { TaxType, Currency, Country } from '@app/common/enums/status.enum';

@Entity('tax_configurations')
@Index(['tenantId', 'country', 'state', 'taxType'])
@Index(['tenantId', 'isActive'])
@Index(['effectiveStartDate', 'effectiveEndDate'])
export class TaxConfiguration extends TenantAwareEntity {
  @Column({
    type: 'varchar',
    length: 50,
    comment: 'Tax configuration code',
  })
  @Index()
  code: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Tax configuration name',
  })
  name: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Tax configuration description',
  })
  description?: string;

  @Column({
    type: 'enum',
    enum: TaxType,
    comment: 'Type of tax',
  })
  @Index()
  taxType: TaxType;

  @Column({
    type: 'enum',
    enum: Country,
    comment: 'Country this tax applies to',
  })
  @Index()
  country: Country;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: 'State/province code',
  })
  @Index()
  state?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'City or local jurisdiction',
  })
  locality?: string;

  @Column({
    type: 'enum',
    enum: Currency,
    default: Currency.USD,
    comment: 'Currency for tax calculations',
  })
  currency: Currency;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 4,
    default: 0,
    comment: 'Tax rate as percentage',
  })
  taxRate: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Minimum taxable income',
  })
  minimumIncome?: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Maximum taxable income',
  })
  maximumIncome?: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Annual tax cap/maximum',
  })
  annualCap?: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Standard deduction amount',
  })
  standardDeduction?: number;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Tax brackets for progressive taxation',
  })
  taxBrackets?: Array<{
    minIncome: number;
    maxIncome: number;
    rate: number;
    flatAmount?: number;
  }>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Exemption amounts by filing status',
  })
  exemptions?: {
    single?: number;
    marriedFilingJointly?: number;
    marriedFilingSeparately?: number;
    headOfHousehold?: number;
    dependents?: number;
  };

  @Column({
    type: 'varchar',
    length: 50,
    default: 'percentage',
    comment: 'Calculation method: percentage, bracket, flat, formula',
  })
  calculationMethod: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Custom calculation formula',
  })
  formula?: string;

  @Column({
    type: 'varchar',
    length: 50,
    default: 'gross',
    comment: 'Tax base: gross, net, taxable_gross',
  })
  taxBase: string;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this tax applies to regular income',
  })
  appliesToRegularIncome: boolean;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this tax applies to overtime',
  })
  appliesToOvertime: boolean;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this tax applies to bonuses',
  })
  appliesToBonuses: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this tax applies to benefits',
  })
  appliesToBenefits: boolean;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Payroll item types this tax applies to',
  })
  applicablePayrollItems?: string[];

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Employee categories this tax applies to',
  })
  applicableEmployeeTypes?: string[];

  @Column({
    type: 'varchar',
    length: 50,
    default: 'monthly',
    comment: 'Frequency of tax calculation',
  })
  frequency: string;

  @Column({
    type: 'date',
    comment: 'Effective start date',
  })
  @Index()
  effectiveStartDate: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Effective end date',
  })
  @Index()
  effectiveEndDate?: Date;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this tax configuration is active',
  })
  @Index()
  isActive: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this is a mandatory tax',
  })
  isMandatory: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether employees can opt out',
  })
  allowOptOut: boolean;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Priority order for tax calculations',
  })
  priority: number;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Tax authority/agency name',
  })
  taxAuthority?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Tax identification number',
  })
  taxId?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Filing requirements or notes',
  })
  filingRequirements?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Reporting configuration',
  })
  reportingConfig?: {
    reportingFrequency?: string; // monthly, quarterly, annually
    dueDate?: string; // day of month or specific date
    forms?: string[]; // required forms
    electronicFiling?: boolean;
    penaltyRate?: number;
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Integration settings for external tax services',
  })
  integrationSettings?: {
    provider?: string; // ADP, Paychex, etc.
    apiEndpoint?: string;
    credentials?: Record<string, any>;
    mappings?: Record<string, string>;
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Compliance rules and validations',
  })
  complianceRules?: {
    minimumWage?: number;
    overtimeThreshold?: number;
    maxHoursPerWeek?: number;
    requiredBreaks?: Array<{
      duration: number;
      afterHours: number;
      isPaid: boolean;
    }>;
    holidayRules?: Array<{
      name: string;
      date: string;
      isPaid: boolean;
      multiplier?: number;
    }>;
  };

  @Column({
    type: 'int',
    default: 0,
    comment: 'Display order',
  })
  sortOrder: number;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Tax configuration metadata',
  })
  metadata?: Record<string, any>;

  // Virtual properties
  get isCurrentlyEffective(): boolean {
    const now = new Date();
    const startOk = this.effectiveStartDate <= now;
    const endOk = !this.effectiveEndDate || this.effectiveEndDate >= now;
    return startOk && endOk && this.isActive;
  }

  get isExpired(): boolean {
    return this.effectiveEndDate ? this.effectiveEndDate < new Date() : false;
  }

  get isFuture(): boolean {
    return this.effectiveStartDate > new Date();
  }

  get hasBrackets(): boolean {
    return this.calculationMethod === 'bracket' && this.taxBrackets && this.taxBrackets.length > 0;
  }

  get isProgressiveTax(): boolean {
    return this.hasBrackets;
  }

  // Methods
  calculateTax(income: number, filingStatus?: string): number {
    if (!this.isCurrentlyEffective || income < (this.minimumIncome || 0)) {
      return 0;
    }

    let taxableIncome = income;
    
    // Apply standard deduction if applicable
    if (this.standardDeduction) {
      taxableIncome = Math.max(0, income - this.standardDeduction);
    }

    // Apply exemptions based on filing status
    if (this.exemptions && filingStatus) {
      const exemption = this.exemptions[filingStatus as keyof typeof this.exemptions] || 0;
      taxableIncome = Math.max(0, taxableIncome - exemption);
    }

    let tax = 0;

    switch (this.calculationMethod) {
      case 'percentage':
        tax = (taxableIncome * this.taxRate) / 100;
        break;
      
      case 'bracket':
        if (this.taxBrackets) {
          tax = this.calculateBracketTax(taxableIncome);
        }
        break;
      
      case 'flat':
        tax = this.taxRate;
        break;
      
      case 'formula':
        // Custom formula calculation would be implemented here
        tax = (taxableIncome * this.taxRate) / 100;
        break;
      
      default:
        tax = (taxableIncome * this.taxRate) / 100;
    }

    // Apply annual cap if specified
    if (this.annualCap && tax > this.annualCap) {
      tax = this.annualCap;
    }

    // Apply maximum income limit
    if (this.maximumIncome && income > this.maximumIncome) {
      const maxTax = this.calculateTax(this.maximumIncome, filingStatus);
      tax = Math.min(tax, maxTax);
    }

    return Math.max(0, tax);
  }

  private calculateBracketTax(income: number): number {
    if (!this.taxBrackets) return 0;

    let tax = 0;
    let remainingIncome = income;

    for (const bracket of this.taxBrackets) {
      if (remainingIncome <= 0) break;

      const bracketMin = bracket.minIncome;
      const bracketMax = bracket.maxIncome || Infinity;
      const bracketRange = Math.min(bracketMax - bracketMin, remainingIncome);

      if (income > bracketMin) {
        const taxableInBracket = Math.min(bracketRange, income - bracketMin);
        tax += (taxableInBracket * bracket.rate) / 100;
        
        if (bracket.flatAmount) {
          tax += bracket.flatAmount;
        }
        
        remainingIncome -= taxableInBracket;
      }
    }

    return tax;
  }

  isApplicableToEmployee(employeeType: string): boolean {
    if (!this.applicableEmployeeTypes) return true;
    return this.applicableEmployeeTypes.includes(employeeType);
  }

  isApplicableToPayrollItem(itemType: string): boolean {
    if (!this.applicablePayrollItems) return true;
    return this.applicablePayrollItems.includes(itemType);
  }

  @BeforeInsert()
  @BeforeUpdate()
  validateConfiguration(): void {
    if (this.effectiveStartDate && this.effectiveEndDate) {
      if (this.effectiveStartDate > this.effectiveEndDate) {
        throw new Error('Effective start date must be before end date');
      }
    }

    if (this.minimumIncome && this.maximumIncome) {
      if (this.minimumIncome > this.maximumIncome) {
        throw new Error('Minimum income must be less than maximum income');
      }
    }

    if (this.calculationMethod === 'bracket' && (!this.taxBrackets || this.taxBrackets.length === 0)) {
      throw new Error('Tax brackets are required for bracket calculation method');
    }
  }
}
