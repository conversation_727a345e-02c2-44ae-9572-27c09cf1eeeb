import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { EncryptionService } from './encryption.service';

describe('EncryptionService', () => {
  let service: EncryptionService;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config = {
        ENCRYPTION_MASTER_KEY: '0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef',
        ENCRYPTION_ALGORITHM: 'aes-256-gcm',
        INDEX_HASH_SALT: 'test-salt-for-indexing',
      };
      return config[key];
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EncryptionService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<EncryptionService>(EncryptionService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
    service.clearKeyCache();
  });

  describe('Configuration Validation', () => {
    it('should validate correct configuration', () => {
      expect(service.validateConfiguration()).toBe(true);
    });

    it('should fail validation with invalid master key', () => {
      mockConfigService.get.mockImplementation((key: string) => {
        if (key === 'ENCRYPTION_MASTER_KEY') return 'invalid-key';
        return mockConfigService.get(key);
      });

      expect(() => new EncryptionService(configService)).toThrow();
    });
  });

  describe('Tenant Key Management', () => {
    const testTenantId = 'test-tenant-123';

    it('should derive consistent tenant keys', async () => {
      const key1 = await service.getTenantKey(testTenantId);
      const key2 = await service.getTenantKey(testTenantId);
      
      expect(key1).toBe(key2);
      expect(key1).toHaveLength(64); // 32 bytes = 64 hex chars
    });

    it('should generate different keys for different tenants', async () => {
      const key1 = await service.getTenantKey('tenant-1');
      const key2 = await service.getTenantKey('tenant-2');
      
      expect(key1).not.toBe(key2);
    });

    it('should cache tenant keys', async () => {
      const key1 = await service.getTenantKey(testTenantId);
      const key2 = await service.getTenantKey(testTenantId);
      
      // Should be the same instance (cached)
      expect(key1).toBe(key2);
    });

    it('should clear key cache', async () => {
      await service.getTenantKey(testTenantId);
      service.clearKeyCache();
      
      // Should work without errors after cache clear
      const key = await service.getTenantKey(testTenantId);
      expect(key).toBeDefined();
    });
  });

  describe('Data Encryption/Decryption', () => {
    const testTenantId = 'test-tenant-123';
    const testData = 'sensitive-information';

    it('should encrypt and decrypt data successfully', async () => {
      const encrypted = await service.encryptData(testData, testTenantId);
      const decrypted = await service.decryptData(encrypted, { tenantId: testTenantId });
      
      expect(decrypted).toBe(testData);
      expect(encrypted).not.toBe(testData);
    });

    it('should produce different ciphertext for same data', async () => {
      const encrypted1 = await service.encryptData(testData, testTenantId);
      const encrypted2 = await service.encryptData(testData, testTenantId);
      
      expect(encrypted1).not.toBe(encrypted2);
      
      // But both should decrypt to the same value
      const decrypted1 = await service.decryptData(encrypted1, { tenantId: testTenantId });
      const decrypted2 = await service.decryptData(encrypted2, { tenantId: testTenantId });
      
      expect(decrypted1).toBe(testData);
      expect(decrypted2).toBe(testData);
    });

    it('should fail decryption with wrong tenant key', async () => {
      const encrypted = await service.encryptData(testData, 'tenant-1');
      
      await expect(
        service.decryptData(encrypted, { tenantId: 'tenant-2' })
      ).rejects.toThrow();
    });

    it('should handle empty data', async () => {
      const encrypted = await service.encryptData('', testTenantId);
      const decrypted = await service.decryptData(encrypted, { tenantId: testTenantId });
      
      expect(decrypted).toBe('');
    });

    it('should handle null data', async () => {
      const encrypted = await service.encryptData(null, testTenantId);
      const decrypted = await service.decryptData(encrypted, { tenantId: testTenantId });
      
      expect(decrypted).toBeNull();
    });
  });

  describe('PII Field Identification', () => {
    it('should identify email fields as PII', () => {
      expect(service.isPIIField('email')).toBe(true);
      expect(service.isPIIField('userEmail')).toBe(true);
      expect(service.isPIIField('contactEmail')).toBe(true);
    });

    it('should identify phone fields as PII', () => {
      expect(service.isPIIField('phone')).toBe(true);
      expect(service.isPIIField('phoneNumber')).toBe(true);
      expect(service.isPIIField('mobilePhone')).toBe(true);
    });

    it('should identify SSN fields as PII', () => {
      expect(service.isPIIField('ssn')).toBe(true);
      expect(service.isPIIField('socialSecurityNumber')).toBe(true);
      expect(service.isPIIField('taxId')).toBe(true);
    });

    it('should not identify non-PII fields', () => {
      expect(service.isPIIField('id')).toBe(false);
      expect(service.isPIIField('createdAt')).toBe(false);
      expect(service.isPIIField('status')).toBe(false);
      expect(service.isPIIField('department')).toBe(false);
    });
  });

  describe('Object Encryption', () => {
    const testTenantId = 'test-tenant-123';
    const testObject = {
      id: '123',
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      phone: '+1234567890',
      department: 'Engineering',
      ssn: '***********',
      createdAt: new Date(),
    };

    it('should encrypt PII fields in object', async () => {
      const result = await service.encryptObject(testObject, testTenantId);
      
      // PII fields should be encrypted
      expect(result.email).not.toBe(testObject.email);
      expect(result.firstName).not.toBe(testObject.firstName);
      expect(result.lastName).not.toBe(testObject.lastName);
      expect(result.phone).not.toBe(testObject.phone);
      expect(result.ssn).not.toBe(testObject.ssn);
      
      // Non-PII fields should remain unchanged
      expect(result.id).toBe(testObject.id);
      expect(result.department).toBe(testObject.department);
      expect(result.createdAt).toBe(testObject.createdAt);
    });

    it('should decrypt PII fields in object', async () => {
      const encrypted = await service.encryptObject(testObject, testTenantId);
      const decrypted = await service.decryptObject(encrypted, { tenantId: testTenantId });
      
      expect(decrypted.email).toBe(testObject.email);
      expect(decrypted.firstName).toBe(testObject.firstName);
      expect(decrypted.lastName).toBe(testObject.lastName);
      expect(decrypted.phone).toBe(testObject.phone);
      expect(decrypted.ssn).toBe(testObject.ssn);
    });

    it('should handle nested objects', async () => {
      const nestedObject = {
        user: {
          email: '<EMAIL>',
          profile: {
            firstName: 'John',
            lastName: 'Doe',
          },
        },
        metadata: {
          department: 'Engineering',
        },
      };

      const encrypted = await service.encryptObject(nestedObject, testTenantId);
      const decrypted = await service.decryptObject(encrypted, { tenantId: testTenantId });
      
      expect(decrypted.user.email).toBe(nestedObject.user.email);
      expect(decrypted.user.profile.firstName).toBe(nestedObject.user.profile.firstName);
      expect(decrypted.user.profile.lastName).toBe(nestedObject.user.profile.lastName);
      expect(decrypted.metadata.department).toBe(nestedObject.metadata.department);
    });
  });

  describe('File Encryption', () => {
    const testTenantId = 'test-tenant-123';
    const testFileContent = Buffer.from('This is test file content');

    it('should encrypt and decrypt file content', async () => {
      const encrypted = await service.encryptFile(testFileContent, testTenantId);
      const decrypted = await service.decryptFile(encrypted, { tenantId: testTenantId });
      
      expect(decrypted.toString()).toBe(testFileContent.toString());
      expect(encrypted).not.toEqual(testFileContent);
    });

    it('should handle large files', async () => {
      const largeContent = Buffer.alloc(1024 * 1024, 'a'); // 1MB of 'a'
      
      const encrypted = await service.encryptFile(largeContent, testTenantId);
      const decrypted = await service.decryptFile(encrypted, { tenantId: testTenantId });
      
      expect(decrypted).toEqual(largeContent);
    });

    it('should handle empty files', async () => {
      const emptyContent = Buffer.alloc(0);
      
      const encrypted = await service.encryptFile(emptyContent, testTenantId);
      const decrypted = await service.decryptFile(encrypted, { tenantId: testTenantId });
      
      expect(decrypted).toEqual(emptyContent);
    });
  });

  describe('Searchable Encryption', () => {
    const testTenantId = 'test-tenant-123';

    it('should generate consistent search hashes', async () => {
      const value = '<EMAIL>';
      
      const hash1 = await service.generateSearchableHash(value, testTenantId);
      const hash2 = await service.generateSearchableHash(value, testTenantId);
      
      expect(hash1).toBe(hash2);
    });

    it('should generate different hashes for different values', async () => {
      const hash1 = await service.generateSearchableHash('value1', testTenantId);
      const hash2 = await service.generateSearchableHash('value2', testTenantId);
      
      expect(hash1).not.toBe(hash2);
    });

    it('should generate different hashes for different tenants', async () => {
      const value = '<EMAIL>';
      
      const hash1 = await service.generateSearchableHash(value, 'tenant-1');
      const hash2 = await service.generateSearchableHash(value, 'tenant-2');
      
      expect(hash1).not.toBe(hash2);
    });
  });

  describe('Token Generation', () => {
    it('should generate secure random tokens', () => {
      const token1 = service.generateSecureToken(32);
      const token2 = service.generateSecureToken(32);
      
      expect(token1).toHaveLength(64); // 32 bytes = 64 hex chars
      expect(token2).toHaveLength(64);
      expect(token1).not.toBe(token2);
    });

    it('should generate tokens of specified length', () => {
      const token16 = service.generateSecureToken(16);
      const token64 = service.generateSecureToken(64);
      
      expect(token16).toHaveLength(32); // 16 bytes = 32 hex chars
      expect(token64).toHaveLength(128); // 64 bytes = 128 hex chars
    });
  });

  describe('Key Rotation', () => {
    const testTenantId = 'test-tenant-123';

    it('should rotate tenant key', async () => {
      const oldKey = await service.getTenantKey(testTenantId);
      await service.rotateTenantKey(testTenantId);
      const newKey = await service.getTenantKey(testTenantId);
      
      expect(newKey).not.toBe(oldKey);
    });

    it('should maintain data integrity after key rotation', async () => {
      const testData = 'sensitive-data';
      
      // Encrypt with old key
      const encrypted = await service.encryptData(testData, testTenantId);
      
      // Rotate key
      await service.rotateTenantKey(testTenantId);
      
      // Should still be able to decrypt with new key
      // Note: In a real implementation, you'd need to re-encrypt existing data
      // This test assumes the service handles key versioning
      const decrypted = await service.decryptData(encrypted, { 
        tenantId: testTenantId,
        keyVersion: 'previous' // Hypothetical parameter
      });
      
      expect(decrypted).toBe(testData);
    });
  });

  describe('Error Handling', () => {
    const testTenantId = 'test-tenant-123';

    it('should handle invalid encrypted data', async () => {
      const invalidData = 'invalid-encrypted-data';
      
      await expect(
        service.decryptData(invalidData, { tenantId: testTenantId })
      ).rejects.toThrow();
    });

    it('should handle corrupted encrypted data', async () => {
      const testData = 'test-data';
      const encrypted = await service.encryptData(testData, testTenantId);
      const corrupted = encrypted.slice(0, -10) + 'corrupted';
      
      await expect(
        service.decryptData(corrupted, { tenantId: testTenantId })
      ).rejects.toThrow();
    });

    it('should handle missing tenant ID', async () => {
      await expect(
        service.encryptData('test', '')
      ).rejects.toThrow();
    });

    it('should handle invalid tenant ID format', async () => {
      await expect(
        service.encryptData('test', 'invalid-tenant-id-format!')
      ).rejects.toThrow();
    });
  });

  describe('Performance', () => {
    const testTenantId = 'test-tenant-123';

    it('should encrypt data within reasonable time', async () => {
      const testData = 'x'.repeat(1000); // 1KB of data
      const start = Date.now();
      
      await service.encryptData(testData, testTenantId);
      
      const duration = Date.now() - start;
      expect(duration).toBeLessThan(100); // Should complete within 100ms
    });

    it('should decrypt data within reasonable time', async () => {
      const testData = 'x'.repeat(1000);
      const encrypted = await service.encryptData(testData, testTenantId);
      
      const start = Date.now();
      await service.decryptData(encrypted, { tenantId: testTenantId });
      
      const duration = Date.now() - start;
      expect(duration).toBeLessThan(100);
    });

    it('should handle concurrent encryption operations', async () => {
      const operations = Array.from({ length: 10 }, (_, i) => 
        service.encryptData(`test-data-${i}`, testTenantId)
      );
      
      const results = await Promise.all(operations);
      
      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(typeof result).toBe('string');
      });
    });
  });
});
