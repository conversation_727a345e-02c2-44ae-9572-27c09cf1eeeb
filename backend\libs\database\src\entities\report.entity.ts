import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';
import { Employee } from './employee.entity';
import { ReportSchedule } from './report-schedule.entity';

export enum ReportType {
  EMPLOYEE = 'employee',
  PAYROLL = 'payroll',
  PERFORMANCE = 'performance',
  ATTENDANCE = 'attendance',
  LEAVE = 'leave',
  RECRUITMENT = 'recruitment',
  TRAINING = 'training',
  COMPLIANCE = 'compliance',
  CUSTOM = 'custom',
}

export enum ReportFormat {
  PDF = 'pdf',
  EXCEL = 'excel',
  CSV = 'csv',
  JSON = 'json',
  HTML = 'html',
}

export enum ReportStatus {
  DRAFT = 'draft',
  GENERATING = 'generating',
  COMPLETED = 'completed',
  FAILED = 'failed',
  SCHEDULED = 'scheduled',
}

@Entity('reports')
@Index(['tenantId', 'reportType'])
@Index(['tenantId', 'status'])
@Index(['tenantId', 'ownerId'])
export class Report {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: ReportType,
    name: 'report_type',
  })
  reportType: ReportType;

  @Column({
    type: 'enum',
    enum: ReportFormat,
    name: 'report_format',
  })
  reportFormat: ReportFormat;

  @Column({
    type: 'enum',
    enum: ReportStatus,
    default: ReportStatus.DRAFT,
  })
  status: ReportStatus;

  @Column({ name: 'owner_id' })
  ownerId: string;

  @ManyToOne(() => Employee, { eager: true })
  @JoinColumn({ name: 'owner_id' })
  owner: Employee;

  @Column({ type: 'json', name: 'report_config' })
  reportConfig: {
    dataSource: string;
    filters?: Array<{
      field: string;
      operator: string;
      value: any;
    }>;
    groupBy?: string[];
    sortBy?: Array<{
      field: string;
      direction: 'asc' | 'desc';
    }>;
    columns?: Array<{
      field: string;
      label: string;
      type: string;
      format?: string;
    }>;
    aggregations?: Array<{
      field: string;
      function: 'sum' | 'avg' | 'count' | 'min' | 'max';
      label: string;
    }>;
  };

  @Column({ type: 'json', nullable: true, name: 'date_range' })
  dateRange: {
    startDate: string;
    endDate: string;
    period?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
    relative?: boolean;
  };

  @Column({ type: 'json', nullable: true, name: 'access_permissions' })
  accessPermissions: {
    viewRoles?: string[];
    editRoles?: string[];
    departments?: string[];
    positions?: string[];
    specificEmployees?: string[];
  };

  @Column({ length: 500, nullable: true, name: 'file_path' })
  filePath: string;

  @Column({ type: 'bigint', nullable: true, name: 'file_size' })
  fileSize: number;

  @Column({ name: 'generated_at', type: 'timestamp', nullable: true })
  generatedAt: Date;

  @Column({ type: 'integer', nullable: true, name: 'generation_time_ms' })
  generationTimeMs: number;

  @Column({ type: 'integer', nullable: true, name: 'record_count' })
  recordCount: number;

  @Column({ type: 'text', nullable: true, name: 'error_message' })
  errorMessage: string;

  @Column({ type: 'json', nullable: true, name: 'execution_log' })
  executionLog: Array<{
    timestamp: string;
    level: 'info' | 'warning' | 'error';
    message: string;
  }>;

  @Column({ type: 'boolean', default: false, name: 'is_template' })
  isTemplate: boolean;

  @Column({ type: 'boolean', default: false, name: 'is_public' })
  isPublic: boolean;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @OneToMany(() => ReportSchedule, schedule => schedule.report)
  schedules: ReportSchedule[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater: User;

  // Computed properties
  get isCompleted(): boolean {
    return this.status === ReportStatus.COMPLETED;
  }

  get fileSizeFormatted(): string {
    if (!this.fileSize) return '0 B';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = this.fileSize;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  get generationTimeFormatted(): string {
    if (!this.generationTimeMs) return '0ms';
    
    if (this.generationTimeMs < 1000) {
      return `${this.generationTimeMs}ms`;
    } else if (this.generationTimeMs < 60000) {
      return `${(this.generationTimeMs / 1000).toFixed(1)}s`;
    } else {
      const minutes = Math.floor(this.generationTimeMs / 60000);
      const seconds = Math.floor((this.generationTimeMs % 60000) / 1000);
      return `${minutes}m ${seconds}s`;
    }
  }
}
