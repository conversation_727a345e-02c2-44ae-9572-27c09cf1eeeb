"""
Sentiment analysis service for HR-related text analysis.
Analyzes employee feedback, reviews, and communications.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import asyncio
import re

import numpy as np
import pandas as pd
from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
from textblob import TextBlob
import spacy
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans

from app.core.config import settings
from app.core.exceptions import SentimentAnalysisError

logger = logging.getLogger(__name__)


class SentimentAnalyzer:
    """Advanced sentiment analysis service for HR applications."""
    
    def __init__(self):
        self.sentiment_pipeline = None
        self.emotion_pipeline = None
        self.nlp = None
        self.topic_vectorizer = None
        self.initialized = False
        
    async def initialize(self):
        """Initialize sentiment analysis models."""
        try:
            # Load sentiment analysis pipeline
            self.sentiment_pipeline = pipeline(
                "sentiment-analysis",
                model=settings.SENTIMENT_MODEL,
                tokenizer=settings.SENTIMENT_MODEL,
                return_all_scores=True
            )
            
            # Load emotion analysis pipeline
            self.emotion_pipeline = pipeline(
                "text-classification",
                model="j-hartmann/emotion-english-distilroberta-base",
                return_all_scores=True
            )
            
            # Load spaCy for text processing
            self.nlp = spacy.load(settings.SPACY_MODEL)
            
            # Initialize topic modeling vectorizer
            self.topic_vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words='english',
                ngram_range=(1, 2)
            )
            
            self.initialized = True
            logger.info("Sentiment analyzer initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize sentiment analyzer: {e}")
            raise SentimentAnalysisError(f"Initialization failed: {e}")
    
    async def analyze_text(self, text: str, include_emotions: bool = True, 
                          include_topics: bool = True) -> SentimentResult:
        """Comprehensive sentiment analysis of text."""
        if not self.initialized:
            await self.initialize()
        
        try:
            # Clean and preprocess text
            cleaned_text = self._preprocess_text(text)
            
            if not cleaned_text.strip():
                raise SentimentAnalysisError("No valid text content for analysis")
            
            # Basic sentiment analysis
            sentiment_scores = await self._analyze_sentiment(cleaned_text)
            
            # Emotion analysis
            emotions = None
            if include_emotions:
                emotions = await self._analyze_emotions(cleaned_text)
            
            # Topic-based sentiment
            topics = None
            if include_topics:
                topics = await self._analyze_topic_sentiment(cleaned_text)
            
            # Extract key phrases and entities
            key_phrases = await self._extract_key_phrases(cleaned_text)
            entities = await self._extract_entities(cleaned_text)
            
            # Calculate confidence and quality metrics
            confidence = self._calculate_confidence(sentiment_scores, emotions)
            
            result = SentimentResult(
                text=text,
                cleaned_text=cleaned_text,
                sentiment=sentiment_scores['label'],
                sentiment_score=sentiment_scores['score'],
                sentiment_distribution=sentiment_scores['distribution'],
                emotions=emotions,
                topics=topics,
                key_phrases=key_phrases,
                entities=entities,
                confidence=confidence,
                analyzed_at=datetime.utcnow(),
                model_version=settings.SENTIMENT_MODEL,
            )
            
            logger.info(f"Sentiment analysis completed: {sentiment_scores['label']} ({sentiment_scores['score']:.3f})")
            return result
            
        except Exception as e:
            logger.error(f"Sentiment analysis failed: {e}")
            raise SentimentAnalysisError(f"Analysis failed: {e}")
    
    async def analyze_batch(self, texts: List[str], batch_size: int = 32) -> List[SentimentResult]:
        """Analyze sentiment for multiple texts efficiently."""
        if not self.initialized:
            await self.initialize()
        
        results = []
        
        # Process in batches to manage memory
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            batch_results = await asyncio.gather(
                *[self.analyze_text(text) for text in batch],
                return_exceptions=True
            )
            
            for result in batch_results:
                if isinstance(result, Exception):
                    logger.error(f"Batch analysis error: {result}")
                    # Create error result
                    error_result = SentimentResult(
                        text="",
                        sentiment="unknown",
                        sentiment_score=0.0,
                        confidence=0.0,
                        analyzed_at=datetime.utcnow(),
                        error=str(result)
                    )
                    results.append(error_result)
                else:
                    results.append(result)
        
        return results
    
    async def analyze_employee_feedback(self, feedback_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze employee feedback with HR-specific insights."""
        try:
            # Extract text from feedback data
            texts = []
            metadata = []
            
            for feedback in feedback_data:
                if 'text' in feedback and feedback['text']:
                    texts.append(feedback['text'])
                    metadata.append({
                        'employee_id': feedback.get('employee_id'),
                        'department': feedback.get('department'),
                        'date': feedback.get('date'),
                        'type': feedback.get('type', 'general'),
                        'rating': feedback.get('rating'),
                    })
            
            if not texts:
                return {'error': 'No valid feedback text found'}
            
            # Analyze all feedback
            results = await self.analyze_batch(texts)
            
            # Aggregate insights
            insights = self._aggregate_feedback_insights(results, metadata)
            
            return insights
            
        except Exception as e:
            logger.error(f"Employee feedback analysis failed: {e}")
            raise SentimentAnalysisError(f"Feedback analysis failed: {e}")
    
    async def analyze_performance_reviews(self, reviews: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze performance review sentiment and themes."""
        try:
            # Process review text
            review_texts = []
            review_metadata = []
            
            for review in reviews:
                # Combine different review sections
                text_parts = []
                if review.get('strengths'):
                    text_parts.append(f"Strengths: {review['strengths']}")
                if review.get('areas_for_improvement'):
                    text_parts.append(f"Areas for improvement: {review['areas_for_improvement']}")
                if review.get('goals'):
                    text_parts.append(f"Goals: {review['goals']}")
                if review.get('comments'):
                    text_parts.append(f"Comments: {review['comments']}")
                
                if text_parts:
                    review_texts.append(' '.join(text_parts))
                    review_metadata.append({
                        'employee_id': review.get('employee_id'),
                        'reviewer_id': review.get('reviewer_id'),
                        'period': review.get('period'),
                        'overall_rating': review.get('overall_rating'),
                        'department': review.get('department'),
                    })
            
            # Analyze sentiment
            results = await self.analyze_batch(review_texts)
            
            # Generate review insights
            insights = self._aggregate_review_insights(results, review_metadata)
            
            return insights
            
        except Exception as e:
            logger.error(f"Performance review analysis failed: {e}")
            raise SentimentAnalysisError(f"Review analysis failed: {e}")
    
    async def detect_workplace_issues(self, communications: List[str]) -> Dict[str, Any]:
        """Detect potential workplace issues from communication sentiment."""
        try:
            # Analyze all communications
            results = await self.analyze_batch(communications)
            
            # Detect patterns indicating issues
            issues = {
                'negative_sentiment_trend': self._detect_negative_trends(results),
                'stress_indicators': self._detect_stress_indicators(results),
                'conflict_indicators': self._detect_conflict_indicators(results),
                'burnout_indicators': self._detect_burnout_indicators(results),
                'engagement_issues': self._detect_engagement_issues(results),
            }
            
            # Calculate overall risk score
            risk_score = self._calculate_workplace_risk_score(issues)
            
            return {
                'risk_score': risk_score,
                'issues_detected': issues,
                'recommendations': self._generate_recommendations(issues),
                'analyzed_communications': len(communications),
                'analysis_date': datetime.utcnow().isoformat(),
            }
            
        except Exception as e:
            logger.error(f"Workplace issue detection failed: {e}")
            raise SentimentAnalysisError(f"Issue detection failed: {e}")
    
    def _preprocess_text(self, text: str) -> str:
        """Clean and preprocess text for analysis."""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove email addresses
        text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '', text)
        
        # Remove excessive punctuation
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)
        text = re.sub(r'[.]{3,}', '...', text)
        
        return text.strip()
    
    async def _analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyze basic sentiment using transformer model."""
        try:
            # Use transformer pipeline
            results = self.sentiment_pipeline(text)
            
            # Process results
            sentiment_dist = {result['label']: result['score'] for result in results[0]}
            
            # Determine primary sentiment
            primary_sentiment = max(sentiment_dist.items(), key=lambda x: x[1])
            
            return {
                'label': primary_sentiment[0].lower(),
                'score': primary_sentiment[1],
                'distribution': sentiment_dist,
            }
            
        except Exception as e:
            logger.error(f"Sentiment analysis error: {e}")
            # Fallback to TextBlob
            blob = TextBlob(text)
            polarity = blob.sentiment.polarity
            
            if polarity > 0.1:
                label = 'positive'
                score = (polarity + 1) / 2
            elif polarity < -0.1:
                label = 'negative'
                score = 1 - ((polarity + 1) / 2)
            else:
                label = 'neutral'
                score = 0.5
            
            return {
                'label': label,
                'score': score,
                'distribution': {label: score},
            }
    
    async def _analyze_emotions(self, text: str) -> EmotionAnalysis:
        """Analyze emotions in text."""
        try:
            results = self.emotion_pipeline(text)
            
            emotions = {}
            for result in results[0]:
                emotions[result['label'].lower()] = result['score']
            
            # Find dominant emotion
            dominant_emotion = max(emotions.items(), key=lambda x: x[1])
            
            return EmotionAnalysis(
                dominant_emotion=dominant_emotion[0],
                emotion_scores=emotions,
                confidence=dominant_emotion[1],
            )
            
        except Exception as e:
            logger.error(f"Emotion analysis error: {e}")
            return EmotionAnalysis(
                dominant_emotion='unknown',
                emotion_scores={},
                confidence=0.0,
            )
    
    async def _analyze_topic_sentiment(self, text: str) -> List[TopicSentiment]:
        """Analyze sentiment for different topics in text."""
        try:
            # Extract sentences
            doc = self.nlp(text)
            sentences = [sent.text for sent in doc.sents]
            
            if len(sentences) < 2:
                return []
            
            # Simple topic extraction based on keywords
            hr_topics = {
                'management': ['manager', 'supervisor', 'boss', 'leadership', 'management'],
                'workload': ['work', 'task', 'project', 'deadline', 'pressure', 'stress'],
                'colleagues': ['team', 'colleague', 'coworker', 'peer', 'collaboration'],
                'compensation': ['salary', 'pay', 'bonus', 'benefits', 'compensation'],
                'growth': ['development', 'training', 'career', 'promotion', 'learning'],
                'culture': ['culture', 'environment', 'atmosphere', 'values', 'company'],
            }
            
            topic_sentiments = []
            
            for topic, keywords in hr_topics.items():
                topic_sentences = []
                for sentence in sentences:
                    if any(keyword in sentence.lower() for keyword in keywords):
                        topic_sentences.append(sentence)
                
                if topic_sentences:
                    # Analyze sentiment for topic-related sentences
                    topic_text = ' '.join(topic_sentences)
                    sentiment = await self._analyze_sentiment(topic_text)
                    
                    topic_sentiments.append(TopicSentiment(
                        topic=topic,
                        sentiment=sentiment['label'],
                        score=sentiment['score'],
                        mentions=len(topic_sentences),
                        sample_text=topic_sentences[0][:100] + '...' if topic_sentences[0] else '',
                    ))
            
            return topic_sentiments
            
        except Exception as e:
            logger.error(f"Topic sentiment analysis error: {e}")
            return []
    
    async def _extract_key_phrases(self, text: str) -> List[str]:
        """Extract key phrases from text."""
        try:
            doc = self.nlp(text)
            
            # Extract noun phrases
            key_phrases = []
            for chunk in doc.noun_chunks:
                if len(chunk.text.split()) >= 2 and len(chunk.text) <= 50:
                    key_phrases.append(chunk.text.strip())
            
            # Remove duplicates and sort by frequency
            phrase_counts = {}
            for phrase in key_phrases:
                phrase_lower = phrase.lower()
                phrase_counts[phrase_lower] = phrase_counts.get(phrase_lower, 0) + 1
            
            # Return top phrases
            sorted_phrases = sorted(phrase_counts.items(), key=lambda x: x[1], reverse=True)
            return [phrase for phrase, count in sorted_phrases[:10]]
            
        except Exception as e:
            logger.error(f"Key phrase extraction error: {e}")
            return []
    
    async def _extract_entities(self, text: str) -> List[Dict[str, str]]:
        """Extract named entities from text."""
        try:
            doc = self.nlp(text)
            
            entities = []
            for ent in doc.ents:
                if ent.label_ in ['PERSON', 'ORG', 'GPE', 'DATE', 'TIME']:
                    entities.append({
                        'text': ent.text,
                        'label': ent.label_,
                        'description': spacy.explain(ent.label_),
                    })
            
            return entities
            
        except Exception as e:
            logger.error(f"Entity extraction error: {e}")
            return []
    
    def _calculate_confidence(self, sentiment_scores: Dict[str, Any], 
                            emotions: Optional[EmotionAnalysis]) -> float:
        """Calculate confidence score for the analysis."""
        confidence = sentiment_scores['score']
        
        if emotions and emotions.confidence:
            # Average with emotion confidence
            confidence = (confidence + emotions.confidence) / 2
        
        return confidence
    
    def _aggregate_feedback_insights(self, results: List[SentimentResult], 
                                   metadata: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Aggregate insights from employee feedback analysis."""
        if not results:
            return {}
        
        # Calculate overall sentiment distribution
        sentiment_counts = {'positive': 0, 'negative': 0, 'neutral': 0}
        total_score = 0
        
        for result in results:
            if result.sentiment in sentiment_counts:
                sentiment_counts[result.sentiment] += 1
            total_score += result.sentiment_score
        
        total_feedback = len(results)
        avg_sentiment_score = total_score / total_feedback if total_feedback > 0 else 0
        
        # Department-wise analysis
        dept_sentiment = {}
        for i, result in enumerate(results):
            if i < len(metadata) and metadata[i].get('department'):
                dept = metadata[i]['department']
                if dept not in dept_sentiment:
                    dept_sentiment[dept] = {'positive': 0, 'negative': 0, 'neutral': 0, 'total': 0}
                
                if result.sentiment in dept_sentiment[dept]:
                    dept_sentiment[dept][result.sentiment] += 1
                dept_sentiment[dept]['total'] += 1
        
        # Extract common themes
        all_key_phrases = []
        for result in results:
            if result.key_phrases:
                all_key_phrases.extend(result.key_phrases)
        
        phrase_counts = {}
        for phrase in all_key_phrases:
            phrase_counts[phrase] = phrase_counts.get(phrase, 0) + 1
        
        common_themes = sorted(phrase_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return {
            'total_feedback': total_feedback,
            'sentiment_distribution': sentiment_counts,
            'average_sentiment_score': avg_sentiment_score,
            'department_sentiment': dept_sentiment,
            'common_themes': common_themes,
            'analysis_summary': self._generate_feedback_summary(sentiment_counts, avg_sentiment_score),
        }
    
    def _aggregate_review_insights(self, results: List[SentimentResult], 
                                 metadata: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Aggregate insights from performance review analysis."""
        # Similar to feedback insights but focused on performance metrics
        return self._aggregate_feedback_insights(results, metadata)
    
    def _detect_negative_trends(self, results: List[SentimentResult]) -> Dict[str, Any]:
        """Detect negative sentiment trends."""
        negative_count = sum(1 for r in results if r.sentiment == 'negative')
        negative_ratio = negative_count / len(results) if results else 0
        
        return {
            'detected': negative_ratio > 0.3,  # More than 30% negative
            'ratio': negative_ratio,
            'count': negative_count,
            'severity': 'high' if negative_ratio > 0.5 else 'medium' if negative_ratio > 0.3 else 'low',
        }
    
    def _detect_stress_indicators(self, results: List[SentimentResult]) -> Dict[str, Any]:
        """Detect stress indicators in communications."""
        stress_keywords = ['stress', 'overwhelmed', 'pressure', 'deadline', 'burnout', 'exhausted']
        
        stress_mentions = 0
        for result in results:
            if any(keyword in result.cleaned_text.lower() for keyword in stress_keywords):
                stress_mentions += 1
        
        stress_ratio = stress_mentions / len(results) if results else 0
        
        return {
            'detected': stress_ratio > 0.2,
            'mentions': stress_mentions,
            'ratio': stress_ratio,
            'keywords_found': stress_keywords,
        }
    
    def _detect_conflict_indicators(self, results: List[SentimentResult]) -> Dict[str, Any]:
        """Detect conflict indicators."""
        conflict_keywords = ['conflict', 'disagree', 'argument', 'tension', 'unfair', 'frustrated']
        
        conflict_mentions = 0
        for result in results:
            if any(keyword in result.cleaned_text.lower() for keyword in conflict_keywords):
                conflict_mentions += 1
        
        return {
            'detected': conflict_mentions > 0,
            'mentions': conflict_mentions,
            'keywords_found': conflict_keywords,
        }
    
    def _detect_burnout_indicators(self, results: List[SentimentResult]) -> Dict[str, Any]:
        """Detect burnout indicators."""
        burnout_keywords = ['burnout', 'tired', 'unmotivated', 'disengaged', 'quit', 'leave']
        
        burnout_mentions = 0
        for result in results:
            if any(keyword in result.cleaned_text.lower() for keyword in burnout_keywords):
                burnout_mentions += 1
        
        return {
            'detected': burnout_mentions > 0,
            'mentions': burnout_mentions,
            'keywords_found': burnout_keywords,
        }
    
    def _detect_engagement_issues(self, results: List[SentimentResult]) -> Dict[str, Any]:
        """Detect employee engagement issues."""
        disengagement_keywords = ['bored', 'uninterested', 'meaningless', 'pointless', 'waste of time']
        
        disengagement_mentions = 0
        for result in results:
            if any(keyword in result.cleaned_text.lower() for keyword in disengagement_keywords):
                disengagement_mentions += 1
        
        return {
            'detected': disengagement_mentions > 0,
            'mentions': disengagement_mentions,
            'keywords_found': disengagement_keywords,
        }
    
    def _calculate_workplace_risk_score(self, issues: Dict[str, Any]) -> float:
        """Calculate overall workplace risk score."""
        risk_score = 0.0
        
        # Weight different issues
        if issues['negative_sentiment_trend']['detected']:
            risk_score += issues['negative_sentiment_trend']['ratio'] * 30
        
        if issues['stress_indicators']['detected']:
            risk_score += issues['stress_indicators']['ratio'] * 25
        
        if issues['conflict_indicators']['detected']:
            risk_score += min(issues['conflict_indicators']['mentions'] * 10, 20)
        
        if issues['burnout_indicators']['detected']:
            risk_score += min(issues['burnout_indicators']['mentions'] * 15, 25)
        
        if issues['engagement_issues']['detected']:
            risk_score += min(issues['engagement_issues']['mentions'] * 10, 20)
        
        return min(risk_score, 100.0)
    
    def _generate_recommendations(self, issues: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on detected issues."""
        recommendations = []
        
        if issues['negative_sentiment_trend']['detected']:
            recommendations.append("Consider conducting team meetings to address concerns and improve communication")
        
        if issues['stress_indicators']['detected']:
            recommendations.append("Implement stress management programs and review workload distribution")
        
        if issues['conflict_indicators']['detected']:
            recommendations.append("Facilitate conflict resolution sessions and improve team collaboration")
        
        if issues['burnout_indicators']['detected']:
            recommendations.append("Review work-life balance policies and consider additional support resources")
        
        if issues['engagement_issues']['detected']:
            recommendations.append("Develop employee engagement initiatives and career development programs")
        
        if not recommendations:
            recommendations.append("Continue monitoring employee sentiment and maintain current positive practices")
        
        return recommendations
    
    def _generate_feedback_summary(self, sentiment_counts: Dict[str, int], avg_score: float) -> str:
        """Generate a summary of feedback analysis."""
        total = sum(sentiment_counts.values())
        if total == 0:
            return "No feedback data available for analysis."
        
        positive_pct = (sentiment_counts.get('positive', 0) / total) * 100
        negative_pct = (sentiment_counts.get('negative', 0) / total) * 100
        
        if positive_pct > 60:
            return f"Overall positive sentiment ({positive_pct:.1f}% positive). Employee satisfaction appears high."
        elif negative_pct > 40:
            return f"Concerning negative sentiment ({negative_pct:.1f}% negative). Immediate attention recommended."
        else:
            return f"Mixed sentiment detected. {positive_pct:.1f}% positive, {negative_pct:.1f}% negative. Monitor trends."
