"use strict";var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})},__copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod)),__toCommonJS=mod=>__copyProps(__defProp({},"__esModule",{value:!0}),mod);var index_exports={};__export(index_exports,{esbuild:()=>esbuild,rollup:()=>rollup,unplugin:()=>unplugin,vite:()=>vite,webpack:()=>webpack});module.exports=__toCommonJS(index_exports);var import_unplugin=require("unplugin");var STORIES_REGEX=/(?<!node_modules.*)\.(story|stories)\.[tj]sx?$/;var import_promises=require("fs/promises"),import_csf_tools=require("storybook/internal/csf-tools");var logger=console;function rollupBasedPlugin(options){return{name:"plugin-csf",async transform(code,id){if(!STORIES_REGEX.test(id))return;let sourceCode=await(0,import_promises.readFile)(id,"utf-8");try{let makeTitle=userTitle=>userTitle||"default",csf=(0,import_csf_tools.loadCsf)(code,{makeTitle}).parse(),csfSource=(0,import_csf_tools.loadCsf)(sourceCode,{makeTitle}).parse();(0,import_csf_tools.enrichCsf)(csf,csfSource,options);let inputSourceMap=this.getCombinedSourcemap();return(0,import_csf_tools.formatCsf)(csf,{sourceMaps:!0,inputSourceMap},code)}catch(err){return err.message?.startsWith("CSF:")||logger.warn(err.message),code}}}}var unplugin=(0,import_unplugin.createUnplugin)(options=>({name:"unplugin-csf",rollup:{...rollupBasedPlugin(options)},vite:{enforce:"pre",...rollupBasedPlugin(options)},webpack(compiler){compiler.options.module.rules.unshift({test:STORIES_REGEX,enforce:"post",use:{options,loader:require.resolve("@storybook/csf-plugin/dist/webpack-loader")}})},rspack(compiler){compiler.options.module.rules.unshift({test:STORIES_REGEX,enforce:"post",use:{options,loader:require.resolve("@storybook/csf-plugin/dist/webpack-loader")}})}})),{esbuild}=unplugin,{webpack}=unplugin,{rollup}=unplugin,{vite}=unplugin;0&&(module.exports={esbuild,rollup,unplugin,vite,webpack});
