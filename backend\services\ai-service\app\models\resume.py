"""
Pydantic models for resume parsing results.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, date
from pydantic import BaseModel, Field, validator


class ContactInfo(BaseModel):
    """Contact information extracted from resume."""
    full_name: Optional[str] = Field(None, description="Full name of the candidate")
    email: Optional[str] = Field(None, description="Email address")
    phone: Optional[str] = Field(None, description="Phone number")
    address: Optional[str] = Field(None, description="Physical address")
    linkedin: Optional[str] = Field(None, description="LinkedIn profile URL")
    github: Optional[str] = Field(None, description="GitHub profile URL")
    website: Optional[str] = Field(None, description="Personal website URL")
    
    @validator('email')
    def validate_email(cls, v):
        if v and '@' not in v:
            return None
        return v


class Experience(BaseModel):
    """Work experience entry."""
    title: Optional[str] = Field(None, description="Job title")
    company: Optional[str] = Field(None, description="Company name")
    location: Optional[str] = Field(None, description="Job location")
    start_date: Optional[str] = Field(None, description="Start date")
    end_date: Optional[str] = Field(None, description="End date")
    duration: Optional[str] = Field(None, description="Duration of employment")
    description: Optional[str] = Field(None, description="Job description and responsibilities")
    achievements: Optional[List[str]] = Field(None, description="Key achievements")
    technologies: Optional[List[str]] = Field(None, description="Technologies used")
    is_current: bool = Field(default=False, description="Whether this is current position")


class Education(BaseModel):
    """Education entry."""
    degree: Optional[str] = Field(None, description="Degree type and name")
    institution: Optional[str] = Field(None, description="Educational institution")
    location: Optional[str] = Field(None, description="Institution location")
    graduation_date: Optional[str] = Field(None, description="Graduation date")
    gpa: Optional[str] = Field(None, description="GPA or grade")
    major: Optional[str] = Field(None, description="Major field of study")
    minor: Optional[str] = Field(None, description="Minor field of study")
    honors: Optional[List[str]] = Field(None, description="Academic honors and awards")
    relevant_coursework: Optional[List[str]] = Field(None, description="Relevant coursework")


class Skill(BaseModel):
    """Skill entry."""
    name: str = Field(..., description="Skill name")
    category: Optional[str] = Field(None, description="Skill category")
    proficiency: Optional[str] = Field(None, description="Proficiency level")
    years_experience: Optional[int] = Field(None, description="Years of experience with skill")
    confidence: Optional[float] = Field(None, ge=0.0, le=1.0, description="Confidence in skill extraction")
    source: Optional[str] = Field(None, description="Source of skill extraction (section, ml, etc.)")
    
    @validator('proficiency')
    def validate_proficiency(cls, v):
        if v and v.lower() not in ['beginner', 'intermediate', 'advanced', 'expert']:
            return None
        return v


class Certification(BaseModel):
    """Certification entry."""
    name: str = Field(..., description="Certification name")
    issuer: Optional[str] = Field(None, description="Issuing organization")
    issue_date: Optional[str] = Field(None, description="Issue date")
    expiry_date: Optional[str] = Field(None, description="Expiry date")
    credential_id: Optional[str] = Field(None, description="Credential ID")
    verification_url: Optional[str] = Field(None, description="Verification URL")


class Project(BaseModel):
    """Project entry."""
    name: str = Field(..., description="Project name")
    description: Optional[str] = Field(None, description="Project description")
    technologies: Optional[List[str]] = Field(None, description="Technologies used")
    start_date: Optional[str] = Field(None, description="Project start date")
    end_date: Optional[str] = Field(None, description="Project end date")
    url: Optional[str] = Field(None, description="Project URL")
    role: Optional[str] = Field(None, description="Role in the project")
    achievements: Optional[List[str]] = Field(None, description="Project achievements")


class Language(BaseModel):
    """Language skill entry."""
    name: str = Field(..., description="Language name")
    proficiency: Optional[str] = Field(None, description="Proficiency level")
    native: bool = Field(default=False, description="Whether this is native language")
    
    @validator('proficiency')
    def validate_proficiency(cls, v):
        valid_levels = ['basic', 'conversational', 'fluent', 'native', 'professional']
        if v and v.lower() not in valid_levels:
            return None
        return v


class ResumeData(BaseModel):
    """Complete resume parsing result."""
    # Raw data
    raw_text: str = Field(..., description="Original text extracted from resume")
    cleaned_text: Optional[str] = Field(None, description="Cleaned and preprocessed text")
    
    # Parsed sections
    contact_info: ContactInfo = Field(default_factory=ContactInfo, description="Contact information")
    summary: Optional[str] = Field(None, description="Professional summary or objective")
    experiences: List[Experience] = Field(default_factory=list, description="Work experience entries")
    education: List[Education] = Field(default_factory=list, description="Education entries")
    skills: List[Skill] = Field(default_factory=list, description="Skills")
    certifications: List[Certification] = Field(default_factory=list, description="Certifications")
    projects: List[Project] = Field(default_factory=list, description="Projects")
    languages: List[Language] = Field(default_factory=list, description="Language skills")
    
    # Metadata
    quality_score: float = Field(default=0.0, ge=0.0, le=100.0, description="Resume quality score")
    parsed_at: datetime = Field(default_factory=datetime.utcnow, description="Parsing timestamp")
    filename: Optional[str] = Field(None, description="Original filename")
    content_type: Optional[str] = Field(None, description="File content type")
    file_size: Optional[int] = Field(None, description="File size in bytes")
    
    # Analysis results
    keyword_matches: Optional[Dict[str, List[str]]] = Field(None, description="Keyword matches by category")
    job_match_score: Optional[float] = Field(None, description="Job matching score")
    missing_sections: Optional[List[str]] = Field(None, description="Missing resume sections")
    recommendations: Optional[List[str]] = Field(None, description="Improvement recommendations")
    
    # Processing metadata
    processing_time: Optional[float] = Field(None, description="Processing time in seconds")
    parser_version: Optional[str] = Field(None, description="Parser version used")
    errors: Optional[List[str]] = Field(None, description="Parsing errors encountered")


class ResumeParsingRequest(BaseModel):
    """Request model for resume parsing."""
    file_content: bytes = Field(..., description="File content as bytes")
    filename: str = Field(..., description="Original filename")
    content_type: str = Field(..., description="MIME type of the file")
    extract_skills: bool = Field(default=True, description="Whether to extract skills")
    extract_experience: bool = Field(default=True, description="Whether to extract experience")
    extract_education: bool = Field(default=True, description="Whether to extract education")
    extract_projects: bool = Field(default=True, description="Whether to extract projects")
    job_description: Optional[str] = Field(None, description="Job description for matching")
    required_skills: Optional[List[str]] = Field(None, description="Required skills for matching")


class BatchResumeParsingRequest(BaseModel):
    """Request model for batch resume parsing."""
    files: List[Dict[str, Any]] = Field(..., min_items=1, description="List of files to parse")
    batch_size: int = Field(default=10, ge=1, le=50, description="Batch processing size")
    extract_skills: bool = Field(default=True, description="Whether to extract skills")
    extract_experience: bool = Field(default=True, description="Whether to extract experience")
    extract_education: bool = Field(default=True, description="Whether to extract education")
    job_description: Optional[str] = Field(None, description="Job description for matching")


class ResumeAnalysisResult(BaseModel):
    """Resume analysis result."""
    resume_id: str = Field(..., description="Resume identifier")
    candidate_name: Optional[str] = Field(None, description="Candidate name")
    overall_score: float = Field(..., ge=0.0, le=100.0, description="Overall candidate score")
    skill_match_score: float = Field(..., ge=0.0, le=100.0, description="Skill matching score")
    experience_score: float = Field(..., ge=0.0, le=100.0, description="Experience relevance score")
    education_score: float = Field(..., ge=0.0, le=100.0, description="Education relevance score")
    
    # Detailed analysis
    matched_skills: List[str] = Field(default_factory=list, description="Skills that match requirements")
    missing_skills: List[str] = Field(default_factory=list, description="Missing required skills")
    experience_highlights: List[str] = Field(default_factory=list, description="Relevant experience highlights")
    red_flags: List[str] = Field(default_factory=list, description="Potential concerns")
    
    # Recommendations
    interview_recommendation: str = Field(..., description="Interview recommendation")
    feedback: List[str] = Field(default_factory=list, description="Feedback for candidate")
    next_steps: List[str] = Field(default_factory=list, description="Recommended next steps")
    
    analyzed_at: datetime = Field(default_factory=datetime.utcnow, description="Analysis timestamp")


class JobMatchingRequest(BaseModel):
    """Request for job matching analysis."""
    resume_data: ResumeData = Field(..., description="Parsed resume data")
    job_description: str = Field(..., description="Job description")
    required_skills: List[str] = Field(default_factory=list, description="Required skills")
    preferred_skills: List[str] = Field(default_factory=list, description="Preferred skills")
    minimum_experience: Optional[int] = Field(None, description="Minimum years of experience")
    education_requirements: Optional[List[str]] = Field(None, description="Education requirements")
    location_preference: Optional[str] = Field(None, description="Location preference")


class SkillGapAnalysis(BaseModel):
    """Skill gap analysis result."""
    candidate_id: str = Field(..., description="Candidate identifier")
    job_title: str = Field(..., description="Target job title")
    
    # Skill analysis
    total_required_skills: int = Field(..., description="Total number of required skills")
    matched_skills: int = Field(..., description="Number of matched skills")
    skill_match_percentage: float = Field(..., description="Skill match percentage")
    
    # Detailed breakdown
    strong_skills: List[str] = Field(default_factory=list, description="Strong matching skills")
    partial_skills: List[str] = Field(default_factory=list, description="Partially matching skills")
    missing_skills: List[str] = Field(default_factory=list, description="Missing skills")
    
    # Development recommendations
    training_recommendations: List[str] = Field(default_factory=list, description="Training recommendations")
    certification_suggestions: List[str] = Field(default_factory=list, description="Certification suggestions")
    experience_gaps: List[str] = Field(default_factory=list, description="Experience gaps to address")
    
    # Timeline
    estimated_development_time: Optional[str] = Field(None, description="Estimated time to close gaps")
    priority_skills: List[str] = Field(default_factory=list, description="Priority skills to develop")
    
    analyzed_at: datetime = Field(default_factory=datetime.utcnow, description="Analysis timestamp")


class ResumeQualityAssessment(BaseModel):
    """Resume quality assessment."""
    resume_id: str = Field(..., description="Resume identifier")
    overall_quality_score: float = Field(..., ge=0.0, le=100.0, description="Overall quality score")
    
    # Section scores
    contact_info_score: float = Field(..., description="Contact information completeness")
    summary_score: float = Field(..., description="Summary quality score")
    experience_score: float = Field(..., description="Experience section quality")
    education_score: float = Field(..., description="Education section quality")
    skills_score: float = Field(..., description="Skills section quality")
    
    # Quality factors
    completeness: float = Field(..., description="Resume completeness score")
    clarity: float = Field(..., description="Clarity and readability score")
    relevance: float = Field(..., description="Content relevance score")
    formatting: float = Field(..., description="Formatting quality score")
    
    # Improvement suggestions
    strengths: List[str] = Field(default_factory=list, description="Resume strengths")
    weaknesses: List[str] = Field(default_factory=list, description="Areas for improvement")
    suggestions: List[str] = Field(default_factory=list, description="Specific improvement suggestions")
    
    assessed_at: datetime = Field(default_factory=datetime.utcnow, description="Assessment timestamp")


class CandidateRanking(BaseModel):
    """Candidate ranking result."""
    job_id: str = Field(..., description="Job identifier")
    total_candidates: int = Field(..., description="Total number of candidates")
    
    # Ranked candidates
    ranked_candidates: List[Dict[str, Any]] = Field(..., description="Candidates ranked by score")
    
    # Ranking criteria
    ranking_criteria: Dict[str, float] = Field(..., description="Ranking criteria weights")
    
    # Summary statistics
    average_score: float = Field(..., description="Average candidate score")
    top_percentile_threshold: float = Field(..., description="Top 10% score threshold")
    recommended_interview_count: int = Field(..., description="Recommended number for interviews")
    
    ranked_at: datetime = Field(default_factory=datetime.utcnow, description="Ranking timestamp")
