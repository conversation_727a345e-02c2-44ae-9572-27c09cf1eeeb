import {
  Entity,
  Column,
  Index,
  ManyToOne,
  Join<PERSON>olumn,
} from 'typeorm';
import { EncryptedEntity } from './base.entity';
import { Employee } from './employee.entity';

export enum ContactType {
  PHONE = 'phone',
  EMAIL = 'email',
  EMERGENCY = 'emergency',
  WORK = 'work',
  PERSONAL = 'personal',
}

@Entity('employee_contacts')
@Index(['employeeId', 'type'])
export class EmployeeContact extends EncryptedEntity {
  @Column({
    type: 'uuid',
    comment: 'Employee ID',
  })
  employeeId: string;

  @Column({
    type: 'enum',
    enum: ContactType,
    comment: 'Type of contact',
  })
  type: ContactType;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Contact value (encrypted)',
  })
  value: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Contact label',
  })
  label?: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this is the primary contact',
  })
  isPrimary: boolean;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this contact is active',
  })
  isActive: boolean;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Display order',
  })
  sortOrder: number;

  // Relationships
  @ManyToOne(() => Employee, employee => employee.contacts, { eager: false })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;
}
