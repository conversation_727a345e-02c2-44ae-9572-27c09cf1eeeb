# Core FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Database and ORM
sqlalchemy==2.0.23
alembic==1.13.0
asyncpg==0.29.0
redis==5.0.1

# AI/ML Libraries
torch==2.1.1
transformers==4.36.0
sentence-transformers==2.2.2
spacy==3.7.2
scikit-learn==1.3.2
pandas==2.1.4
numpy==1.25.2
nltk==3.8.1

# Resume Parsing
pdfplumber==0.10.0
python-docx==1.1.0
textract==1.6.5

# Computer Vision (for document processing)
opencv-python==********
Pillow==10.1.0

# Natural Language Processing
openai==1.3.7
langchain==0.0.350
chromadb==0.4.18

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Monitoring and Logging
prometheus-client==0.19.0
structlog==23.2.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Environment and Configuration
python-dotenv==1.0.0
click==8.1.7

# Data Validation and Serialization
marshmallow==3.20.1
cerberus==1.3.5

# Async utilities
asyncio-mqtt==0.16.1
aiofiles==23.2.1

# Model serving and deployment
mlflow==2.8.1
bentoml==1.1.10

# Caching
aiocache==0.12.2

# Background tasks
celery==5.3.4
flower==2.0.1

# Metrics and monitoring
psutil==5.9.6
py-cpuinfo==9.0.0
