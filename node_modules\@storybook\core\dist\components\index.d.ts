import * as React$1 from 'react';
import React__default, { ComponentProps, MouseEvent, AnchorHTMLAttributes, ReactNode, ReactElement, ButtonHTMLAttributes, SyntheticEvent, Component, RefObject, FC, PropsWithChildren, RefAttributes, DetailedHTMLProps, ElementType } from 'react';
import * as _storybook_core_theming from '@storybook/core/theming';
import { FunctionInterpolation } from '@storybook/core/theming';
import { BuiltInParserName } from 'prettier';
import { Addon_RenderOptions } from '@storybook/core/types';

declare const A: _storybook_core_theming.StyledComponent<React$1.AnchorHTMLAttributes<HTMLAnchorElement> & {
    theme?: _storybook_core_theming.Theme;
}, {}, {}>;

declare const Blockquote: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.BlockquoteHTMLAttributes<HTMLQuoteElement>, HTMLQuoteElement>, {}>;

declare const DefaultCodeBlock: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React__default.ElementType;
}, React__default.DetailedHTMLProps<React__default.HTMLAttributes<HTMLElement>, HTMLElement>, {}>;
declare const Code: ({ className, children, ...props }: ComponentProps<typeof DefaultCodeBlock>) => React__default.JSX.Element;

declare const Div: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, {}>;

declare const DL: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLDListElement>, HTMLDListElement>, {}>;

declare const H1: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, {}>;

declare const H2: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, {}>;

declare const H3: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, {}>;

declare const H4: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, {}>;

declare const H5: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, {}>;

declare const H6: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>, {}>;

declare const HR: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLHRElement>, HTMLHRElement>, {}>;

declare const Img: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement>, {}>;

declare const LI: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.LiHTMLAttributes<HTMLLIElement>, HTMLLIElement>, {}>;

declare const OL: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.OlHTMLAttributes<HTMLOListElement>, HTMLOListElement>, {}>;

declare const P: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLParagraphElement>, HTMLParagraphElement>, {}>;

declare const Pre: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLPreElement>, HTMLPreElement>, {}>;

declare const Span: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, {}>;

declare const Table: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.TableHTMLAttributes<HTMLTableElement>, HTMLTableElement>, {}>;

declare const TT: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLTitleElement>, HTMLTitleElement>, {}>;

declare const UL: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLUListElement>, HTMLUListElement>, {}>;

interface BadgeProps {
    status?: 'positive' | 'negative' | 'neutral' | 'warning' | 'critical';
    children?: React__default.ReactNode;
}
declare const Badge: ({ ...props }: BadgeProps) => React__default.JSX.Element;

interface LinkStylesProps {
    secondary?: boolean;
    tertiary?: boolean;
    nochrome?: boolean;
    inverse?: boolean;
    isButton?: boolean;
}
interface LinkInnerProps {
    withArrow?: boolean;
    containsIcon?: boolean;
}
type AProps = AnchorHTMLAttributes<HTMLAnchorElement>;
interface LinkProps extends LinkInnerProps, LinkStylesProps, AProps {
    cancel?: boolean;
    className?: string;
    style?: object;
    onClick?: (e: MouseEvent) => void;
    href?: string;
}
declare const Link$1: ({ cancel, children, onClick, withArrow, containsIcon, className, style, ...rest }: LinkProps) => React__default.JSX.Element;

declare const DocumentWrapper: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, {}>;

declare const supportedLanguages: {
    jsextra: any;
    jsx: any;
    json: any;
    yml: any;
    md: any;
    bash: any;
    css: any;
    html: any;
    tsx: any;
    typescript: any;
    graphql: any;
};
declare function createCopyToClipboardFunction(): (text: string) => Promise<void>;

interface SyntaxHighlighterRendererProps {
    rows: any[];
    stylesheet: string;
    useInlineStyles: boolean;
}
type SyntaxHighlighterRenderer = (props: SyntaxHighlighterRendererProps) => ReactNode;
interface SyntaxHighlighterCustomProps {
    language: string;
    copyable?: boolean;
    bordered?: boolean;
    padded?: boolean;
    format?: SyntaxHighlighterFormatTypes;
    formatter?: (type: SyntaxHighlighterFormatTypes, source: string) => Promise<string>;
    className?: string;
    renderer?: SyntaxHighlighterRenderer;
}
type SyntaxHighlighterFormatTypes = boolean | 'dedent' | BuiltInParserName;
type LineTagPropsFunction = (lineNumber: number) => React.HTMLProps<HTMLElement>;
type SupportedLanguage = 'text' | keyof typeof supportedLanguages;
interface SyntaxHighlighterBaseProps {
    children?: React.ReactNode;
    codeTagProps?: React.HTMLProps<HTMLElement>;
    customStyle?: any;
    language?: SupportedLanguage;
    lineNumberStyle?: any;
    lineProps?: LineTagPropsFunction | React.HTMLProps<HTMLElement>;
    showLineNumbers?: boolean;
    startingLineNumber?: number;
    wrapLongLines?: boolean;
    style?: any;
    useInlineStyles?: boolean;
}
type SyntaxHighlighterProps = SyntaxHighlighterBaseProps & SyntaxHighlighterCustomProps;

declare const LazySyntaxHighlighter: React__default.LazyExoticComponent<(props: ComponentProps<{
    ({ children, language, copyable, bordered, padded, format, formatter, className, showLineNumbers, ...rest }: SyntaxHighlighterProps): React__default.JSX.Element | null;
    registerLanguage(name: string, func: any): void;
}>) => React__default.JSX.Element>;
declare const LazySyntaxHighlighterWithFormatter: React__default.LazyExoticComponent<(props: ComponentProps<{
    ({ children, language, copyable, bordered, padded, format, formatter, className, showLineNumbers, ...rest }: SyntaxHighlighterProps): React__default.JSX.Element | null;
    registerLanguage(name: string, func: any): void;
}>) => React__default.JSX.Element>;
declare const SyntaxHighlighter: {
    (props: ComponentProps<typeof LazySyntaxHighlighter> | ComponentProps<typeof LazySyntaxHighlighterWithFormatter>): React__default.JSX.Element;
    registerLanguage(name: string, func: any): void;
};

interface ActionItem {
    title: string | ReactElement;
    className?: string;
    onClick: (e: MouseEvent<HTMLButtonElement>) => void;
    disabled?: boolean;
}
interface ActionBarProps {
    actionItems: ActionItem[];
}
declare const ActionBar: ({ actionItems, ...props }: ActionBarProps) => React__default.JSX.Element;

type Scope<C = any> = {
    [scopeName: string]: React$1.Context<C>[];
} | undefined;
type ScopeHook = (scope: Scope) => {
    [__scopeProp: string]: Scope;
};
interface CreateScope {
    scopeName: string;
    (): ScopeHook;
}

declare const NODES$3: readonly ["a", "button", "div", "form", "h2", "h3", "img", "input", "label", "li", "nav", "ol", "p", "select", "span", "svg", "ul"];
type Primitives$3 = {
    [E in (typeof NODES$3)[number]]: PrimitiveForwardRefComponent$3<E>;
};
type PrimitivePropsWithRef$3<E extends React$1.ElementType> = React$1.ComponentPropsWithRef<E> & {
    asChild?: boolean;
};
interface PrimitiveForwardRefComponent$3<E extends React$1.ElementType> extends React$1.ForwardRefExoticComponent<PrimitivePropsWithRef$3<E>> {
}
declare const Primitive$3: Primitives$3;

type PrimitiveDivProps$3 = React$1.ComponentPropsWithoutRef<typeof Primitive$3.div>;
interface DismissableLayerProps$1 extends PrimitiveDivProps$3 {
    /**
     * When `true`, hover/focus/click interactions will be disabled on elements outside
     * the `DismissableLayer`. Users will need to click twice on outside elements to
     * interact with them: once to close the `DismissableLayer`, and again to trigger the element.
     */
    disableOutsidePointerEvents?: boolean;
    /**
     * Event handler called when the escape key is down.
     * Can be prevented.
     */
    onEscapeKeyDown?: (event: KeyboardEvent) => void;
    /**
     * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.
     * Can be prevented.
     */
    onPointerDownOutside?: (event: PointerDownOutsideEvent) => void;
    /**
     * Event handler called when the focus moves outside of the `DismissableLayer`.
     * Can be prevented.
     */
    onFocusOutside?: (event: FocusOutsideEvent) => void;
    /**
     * Event handler called when an interaction happens outside the `DismissableLayer`.
     * Specifically, when a `pointerdown` event happens outside or focus moves outside of it.
     * Can be prevented.
     */
    onInteractOutside?: (event: PointerDownOutsideEvent | FocusOutsideEvent) => void;
    /**
     * Handler called when the `DismissableLayer` should be dismissed
     */
    onDismiss?: () => void;
}
declare const DismissableLayer: React$1.ForwardRefExoticComponent<DismissableLayerProps$1 & React$1.RefAttributes<HTMLDivElement>>;
type PointerDownOutsideEvent = CustomEvent<{
    originalEvent: PointerEvent;
}>;
type FocusOutsideEvent = CustomEvent<{
    originalEvent: FocusEvent;
}>;

declare const NODES$2: readonly ["a", "button", "div", "form", "h2", "h3", "img", "input", "label", "li", "nav", "ol", "p", "select", "span", "svg", "ul"];
type Primitives$2 = {
    [E in (typeof NODES$2)[number]]: PrimitiveForwardRefComponent$2<E>;
};
type PrimitivePropsWithRef$2<E extends React$1.ElementType> = React$1.ComponentPropsWithRef<E> & {
    asChild?: boolean;
};
interface PrimitiveForwardRefComponent$2<E extends React$1.ElementType> extends React$1.ForwardRefExoticComponent<PrimitivePropsWithRef$2<E>> {
}
declare const Primitive$2: Primitives$2;

type PrimitiveDivProps$2 = React$1.ComponentPropsWithoutRef<typeof Primitive$2.div>;
interface FocusScopeProps$1 extends PrimitiveDivProps$2 {
    /**
     * When `true`, tabbing from last item will focus first tabbable
     * and shift+tab from first item will focus last tababble.
     * @defaultValue false
     */
    loop?: boolean;
    /**
     * When `true`, focus cannot escape the focus scope via keyboard,
     * pointer, or a programmatic focus.
     * @defaultValue false
     */
    trapped?: boolean;
    /**
     * Event handler called when auto-focusing on mount.
     * Can be prevented.
     */
    onMountAutoFocus?: (event: Event) => void;
    /**
     * Event handler called when auto-focusing on unmount.
     * Can be prevented.
     */
    onUnmountAutoFocus?: (event: Event) => void;
}
declare const FocusScope: React$1.ForwardRefExoticComponent<FocusScopeProps$1 & React$1.RefAttributes<HTMLDivElement>>;

declare const NODES$1: readonly ["a", "button", "div", "form", "h2", "h3", "img", "input", "label", "li", "nav", "ol", "p", "select", "span", "svg", "ul"];
type Primitives$1 = {
    [E in (typeof NODES$1)[number]]: PrimitiveForwardRefComponent$1<E>;
};
type PrimitivePropsWithRef$1<E extends React$1.ElementType> = React$1.ComponentPropsWithRef<E> & {
    asChild?: boolean;
};
interface PrimitiveForwardRefComponent$1<E extends React$1.ElementType> extends React$1.ForwardRefExoticComponent<PrimitivePropsWithRef$1<E>> {
}
declare const Primitive$1: Primitives$1;

type PrimitiveDivProps$1 = React$1.ComponentPropsWithoutRef<typeof Primitive$1.div>;
interface PortalProps$1 extends PrimitiveDivProps$1 {
    /**
     * An optional container where the portaled content should be appended.
     */
    container?: Element | DocumentFragment | null;
}
declare const Portal$1: React$1.ForwardRefExoticComponent<PortalProps$1 & React$1.RefAttributes<HTMLDivElement>>;

declare const NODES: readonly ["a", "button", "div", "form", "h2", "h3", "img", "input", "label", "li", "nav", "ol", "p", "select", "span", "svg", "ul"];
type Primitives = {
    [E in (typeof NODES)[number]]: PrimitiveForwardRefComponent<E>;
};
type PrimitivePropsWithRef<E extends React$1.ElementType> = React$1.ComponentPropsWithRef<E> & {
    asChild?: boolean;
};
interface PrimitiveForwardRefComponent<E extends React$1.ElementType> extends React$1.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {
}
declare const Primitive: Primitives;

declare const createDialogScope: CreateScope;
interface DialogProps {
    children?: React$1.ReactNode;
    open?: boolean;
    defaultOpen?: boolean;
    onOpenChange?(open: boolean): void;
    modal?: boolean;
}
declare const Dialog: React$1.FC<DialogProps>;
type PrimitiveButtonProps = React$1.ComponentPropsWithoutRef<typeof Primitive.button>;
interface DialogTriggerProps extends PrimitiveButtonProps {
}
declare const DialogTrigger: React$1.ForwardRefExoticComponent<DialogTriggerProps & React$1.RefAttributes<HTMLButtonElement>>;
type PortalProps = React$1.ComponentPropsWithoutRef<typeof Portal$1>;
interface DialogPortalProps {
    children?: React$1.ReactNode;
    /**
     * Specify a container element to portal the content into.
     */
    container?: PortalProps['container'];
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with React animation libraries.
     */
    forceMount?: true;
}
declare const DialogPortal: React$1.FC<DialogPortalProps>;
interface DialogOverlayProps extends DialogOverlayImplProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with React animation libraries.
     */
    forceMount?: true;
}
declare const DialogOverlay: React$1.ForwardRefExoticComponent<DialogOverlayProps & React$1.RefAttributes<HTMLDivElement>>;
type PrimitiveDivProps = React$1.ComponentPropsWithoutRef<typeof Primitive.div>;
interface DialogOverlayImplProps extends PrimitiveDivProps {
}
interface DialogContentProps extends DialogContentTypeProps {
    /**
     * Used to force mounting when more control is needed. Useful when
     * controlling animation with React animation libraries.
     */
    forceMount?: true;
}
declare const DialogContent: React$1.ForwardRefExoticComponent<DialogContentProps & React$1.RefAttributes<HTMLDivElement>>;
interface DialogContentTypeProps extends Omit<DialogContentImplProps, 'trapFocus' | 'disableOutsidePointerEvents'> {
}
type DismissableLayerProps = React$1.ComponentPropsWithoutRef<typeof DismissableLayer>;
type FocusScopeProps = React$1.ComponentPropsWithoutRef<typeof FocusScope>;
interface DialogContentImplProps extends Omit<DismissableLayerProps, 'onDismiss'> {
    /**
     * When `true`, focus cannot escape the `Content` via keyboard,
     * pointer, or a programmatic focus.
     * @defaultValue false
     */
    trapFocus?: FocusScopeProps['trapped'];
    /**
     * Event handler called when auto-focusing on open.
     * Can be prevented.
     */
    onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];
    /**
     * Event handler called when auto-focusing on close.
     * Can be prevented.
     */
    onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];
}
type PrimitiveHeading2Props = React$1.ComponentPropsWithoutRef<typeof Primitive.h2>;
interface DialogTitleProps extends PrimitiveHeading2Props {
}
declare const DialogTitle: React$1.ForwardRefExoticComponent<DialogTitleProps & React$1.RefAttributes<HTMLHeadingElement>>;
type PrimitiveParagraphProps = React$1.ComponentPropsWithoutRef<typeof Primitive.p>;
interface DialogDescriptionProps extends PrimitiveParagraphProps {
}
declare const DialogDescription: React$1.ForwardRefExoticComponent<DialogDescriptionProps & React$1.RefAttributes<HTMLParagraphElement>>;
interface DialogCloseProps extends PrimitiveButtonProps {
}
declare const DialogClose: React$1.ForwardRefExoticComponent<DialogCloseProps & React$1.RefAttributes<HTMLButtonElement>>;
declare const WarningProvider: React$1.FC<{
    contentName: string;
    titleName: string;
    docsSlug: string;
} & {
    children: React$1.ReactNode;
}>;
declare const Root: React$1.FC<DialogProps>;
declare const Trigger: React$1.ForwardRefExoticComponent<DialogTriggerProps & React$1.RefAttributes<HTMLButtonElement>>;
declare const Portal: React$1.FC<DialogPortalProps>;
declare const Overlay$1: React$1.ForwardRefExoticComponent<DialogOverlayProps & React$1.RefAttributes<HTMLDivElement>>;
declare const Content$1: React$1.ForwardRefExoticComponent<DialogContentProps & React$1.RefAttributes<HTMLDivElement>>;
declare const Title$1: React$1.ForwardRefExoticComponent<DialogTitleProps & React$1.RefAttributes<HTMLHeadingElement>>;
declare const Description$1: React$1.ForwardRefExoticComponent<DialogDescriptionProps & React$1.RefAttributes<HTMLParagraphElement>>;
declare const Close: React$1.ForwardRefExoticComponent<DialogCloseProps & React$1.RefAttributes<HTMLButtonElement>>;

declare const Dialog$1_Close: typeof Close;
declare const Dialog$1_Dialog: typeof Dialog;
declare const Dialog$1_DialogClose: typeof DialogClose;
type Dialog$1_DialogCloseProps = DialogCloseProps;
declare const Dialog$1_DialogContent: typeof DialogContent;
type Dialog$1_DialogContentProps = DialogContentProps;
declare const Dialog$1_DialogDescription: typeof DialogDescription;
type Dialog$1_DialogDescriptionProps = DialogDescriptionProps;
declare const Dialog$1_DialogOverlay: typeof DialogOverlay;
type Dialog$1_DialogOverlayProps = DialogOverlayProps;
declare const Dialog$1_DialogPortal: typeof DialogPortal;
type Dialog$1_DialogPortalProps = DialogPortalProps;
type Dialog$1_DialogProps = DialogProps;
declare const Dialog$1_DialogTitle: typeof DialogTitle;
type Dialog$1_DialogTitleProps = DialogTitleProps;
declare const Dialog$1_DialogTrigger: typeof DialogTrigger;
type Dialog$1_DialogTriggerProps = DialogTriggerProps;
declare const Dialog$1_Portal: typeof Portal;
declare const Dialog$1_Root: typeof Root;
declare const Dialog$1_Trigger: typeof Trigger;
declare const Dialog$1_WarningProvider: typeof WarningProvider;
declare const Dialog$1_createDialogScope: typeof createDialogScope;
declare namespace Dialog$1 {
  export { Dialog$1_Close as Close, Content$1 as Content, Description$1 as Description, Dialog$1_Dialog as Dialog, Dialog$1_DialogClose as DialogClose, type Dialog$1_DialogCloseProps as DialogCloseProps, Dialog$1_DialogContent as DialogContent, type Dialog$1_DialogContentProps as DialogContentProps, Dialog$1_DialogDescription as DialogDescription, type Dialog$1_DialogDescriptionProps as DialogDescriptionProps, Dialog$1_DialogOverlay as DialogOverlay, type Dialog$1_DialogOverlayProps as DialogOverlayProps, Dialog$1_DialogPortal as DialogPortal, type Dialog$1_DialogPortalProps as DialogPortalProps, type Dialog$1_DialogProps as DialogProps, Dialog$1_DialogTitle as DialogTitle, type Dialog$1_DialogTitleProps as DialogTitleProps, Dialog$1_DialogTrigger as DialogTrigger, type Dialog$1_DialogTriggerProps as DialogTriggerProps, Overlay$1 as Overlay, Dialog$1_Portal as Portal, Dialog$1_Root as Root, Title$1 as Title, Dialog$1_Trigger as Trigger, Dialog$1_WarningProvider as WarningProvider, Dialog$1_createDialogScope as createDialogScope };
}

interface ButtonProps$1 extends ButtonHTMLAttributes<HTMLButtonElement> {
    asChild?: boolean;
    size?: 'small' | 'medium';
    padding?: 'small' | 'medium' | 'none';
    variant?: 'outline' | 'solid' | 'ghost';
    onClick?: (event: SyntheticEvent) => void;
    disabled?: boolean;
    active?: boolean;
    animation?: 'none' | 'rotate360' | 'glow' | 'jiggle';
    /** @deprecated Use {@link asChild} instead. This will be removed in Storybook 9.0 */
    isLink?: boolean;
    /** @deprecated Use {@link variant} instead. This will be removed in Storybook 9.0 */
    primary?: boolean;
    /** @deprecated Use {@link variant} instead. This will be removed in Storybook 9.0 */
    secondary?: boolean;
    /** @deprecated Use {@link variant} instead. This will be removed in Storybook 9.0 */
    tertiary?: boolean;
    /** @deprecated Use {@link variant} instead. This will be removed in Storybook 9.0 */
    gray?: boolean;
    /** @deprecated Use {@link variant} instead. This will be removed in Storybook 9.0 */
    inForm?: boolean;
    /** @deprecated Use {@link size} instead. This will be removed in Storybook 9.0 */
    small?: boolean;
    /** @deprecated Use {@link variant} instead. This will be removed in Storybook 9.0 */
    outline?: boolean;
    /** @deprecated Add your icon as a child directly. This will be removed in Storybook 9.0 */
    containsIcon?: boolean;
}
declare const Button: React__default.ForwardRefExoticComponent<ButtonProps$1 & React__default.RefAttributes<HTMLButtonElement>>;

declare const IconButton: React__default.ForwardRefExoticComponent<ButtonProps$1 & React__default.RefAttributes<HTMLButtonElement>>;

declare const Overlay: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React__default.ElementType;
}, React__default.DetailedHTMLProps<React__default.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, {}>;
declare const Container: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React__default.ElementType;
} & {
    width?: number;
    height?: number;
}, React__default.DetailedHTMLProps<React__default.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, {}>;
declare const CloseButton: (props: React__default.ComponentProps<typeof IconButton>) => React__default.JSX.Element;
declare const Content: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React__default.ElementType;
}, React__default.DetailedHTMLProps<React__default.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, {}>;
declare const Row: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React__default.ElementType;
}, React__default.DetailedHTMLProps<React__default.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, {}>;
declare const Col: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React__default.ElementType;
}, React__default.DetailedHTMLProps<React__default.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, {}>;
declare const Header: (props: React__default.ComponentProps<typeof Col>) => React__default.JSX.Element;
declare const Title: _storybook_core_theming.StyledComponent<DialogTitleProps & React__default.RefAttributes<HTMLHeadingElement> & {
    theme?: _storybook_core_theming.Theme;
}, {}, {}>;
declare const Description: _storybook_core_theming.StyledComponent<DialogDescriptionProps & React__default.RefAttributes<HTMLParagraphElement> & {
    theme?: _storybook_core_theming.Theme;
}, {}, {}>;
declare const Actions: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React__default.ElementType;
}, React__default.DetailedHTMLProps<React__default.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, {}>;
declare const ErrorWrapper: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React__default.ElementType;
}, React__default.DetailedHTMLProps<React__default.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, {}>;
declare const Error$1: ({ children, ...props }: {
    children: React__default.ReactNode;
} & ComponentProps<typeof ErrorWrapper>) => React__default.JSX.Element;

declare const Components_Actions: typeof Actions;
declare const Components_CloseButton: typeof CloseButton;
declare const Components_Col: typeof Col;
declare const Components_Container: typeof Container;
declare const Components_Content: typeof Content;
declare const Components_Description: typeof Description;
declare const Components_ErrorWrapper: typeof ErrorWrapper;
declare const Components_Header: typeof Header;
declare const Components_Overlay: typeof Overlay;
declare const Components_Row: typeof Row;
declare const Components_Title: typeof Title;
declare namespace Components {
  export { Components_Actions as Actions, Components_CloseButton as CloseButton, Components_Col as Col, Components_Container as Container, Components_Content as Content, Components_Description as Description, Error$1 as Error, Components_ErrorWrapper as ErrorWrapper, Components_Header as Header, Components_Overlay as Overlay, Components_Row as Row, Components_Title as Title };
}

type ContentProps = React__default.ComponentProps<typeof Content$1>;
interface ModalProps extends Omit<React__default.ComponentProps<typeof Root>, 'children'> {
    width?: number;
    height?: number;
    children: React__default.ReactNode;
    onEscapeKeyDown?: ContentProps['onEscapeKeyDown'];
    onInteractOutside?: ContentProps['onInteractOutside'];
    className?: string;
    container?: HTMLElement;
}
declare function BaseModal({ children, width, height, onEscapeKeyDown, onInteractOutside, className, container, ...rootProps }: ModalProps): React__default.JSX.Element;
declare const Modal: typeof BaseModal & typeof Components & {
    Dialog: typeof Dialog$1;
};

interface SpacedProps {
    children?: React__default.ReactNode;
    col?: number;
    row?: number;
    outer?: number | boolean;
}
declare const Spaced: ({ col, row, outer, children, ...rest }: SpacedProps) => React__default.JSX.Element;

interface PlaceholderProps {
    children?: React__default.ReactNode;
}
declare const Placeholder: ({ children, ...props }: PlaceholderProps) => React__default.JSX.Element;

interface ScrollAreaProps {
    children?: React__default.ReactNode;
    horizontal?: boolean;
    vertical?: boolean;
    className?: string;
    offset?: number;
    scrollbarSize?: number;
}
declare const ScrollArea: React__default.ForwardRefExoticComponent<ScrollAreaProps & React__default.RefAttributes<HTMLDivElement>>;

type ZoomProps = {
    scale: number;
    children: ReactElement | ReactElement[];
};
declare function ZoomElement({ scale, children }: ZoomProps): React__default.JSX.Element;

type IZoomIFrameProps = {
    scale: number;
    children: ReactElement<HTMLIFrameElement>;
    iFrameRef: RefObject<HTMLIFrameElement>;
    active?: boolean;
};
declare class ZoomIFrame extends Component<IZoomIFrameProps> {
    iframe: HTMLIFrameElement;
    componentDidMount(): void;
    shouldComponentUpdate(nextProps: IZoomIFrameProps): boolean;
    setIframeInnerZoom(scale: number): void;
    setIframeZoom(scale: number): void;
    render(): React__default.JSX.Element;
}

declare const Zoom: {
    Element: typeof ZoomElement;
    IFrame: typeof ZoomIFrame;
};

interface ErrorFormatterProps {
    error: Error;
}
declare const ErrorFormatter: ({ error }: ErrorFormatterProps) => React__default.JSX.Element;

/**
 * These types are copied from `react-textarea-autosize`. I copied them because of
 * https://github.com/storybookjs/storybook/issues/18734 Maybe there's some bug in `tsup` or
 * `react-textarea-autosize`?
 */
type TextareaPropsRaw = React__default.TextareaHTMLAttributes<HTMLTextAreaElement>;
type Style = Omit<NonNullable<TextareaPropsRaw['style']>, 'maxHeight' | 'minHeight'> & {
    height?: number;
};
type TextareaHeightChangeMeta = {
    rowHeight: number;
};
interface TextareaAutosizeProps extends Omit<TextareaPropsRaw, 'style'> {
    maxRows?: number;
    minRows?: number;
    onHeightChange?: (height: number, meta: TextareaHeightChangeMeta) => void;
    cacheMeasurements?: boolean;
    style?: Style;
}
type Sizes = '100%' | 'flex' | 'auto';
type Alignments = 'end' | 'center' | 'start';
type ValidationStates = 'valid' | 'error' | 'warn';

interface FieldProps {
    children?: ReactNode;
    label?: ReactNode;
}

declare const Form: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.FormHTMLAttributes<HTMLFormElement>, HTMLFormElement>, {}> & {
    Field: ({ label, children, ...props }: FieldProps) => React$1.JSX.Element;
    Input: _storybook_core_theming.StyledComponent<Omit<Omit<React$1.HTMLProps<HTMLInputElement>, "align" | "height" | "size" | "valid"> & {
        size?: Sizes;
        align?: Alignments;
        valid?: ValidationStates;
        height?: number;
    }, "ref"> & React$1.RefAttributes<any> & {
        theme?: _storybook_core_theming.Theme;
    } & Omit<React$1.HTMLProps<HTMLInputElement>, "align" | "height" | "size" | "valid"> & {
        size?: Sizes;
        align?: Alignments;
        valid?: ValidationStates;
        height?: number;
    }, {}, {}> & {
        displayName: string;
    };
    Select: _storybook_core_theming.StyledComponent<Omit<React$1.SelectHTMLAttributes<HTMLSelectElement>, "align" | "height" | "size" | "valid"> & {
        size?: Sizes;
        align?: Alignments;
        valid?: ValidationStates;
        height?: number;
    } & React$1.RefAttributes<any> & {
        theme?: _storybook_core_theming.Theme;
    }, {}, {}> & {
        displayName: string;
    };
    Textarea: _storybook_core_theming.StyledComponent<Omit<Omit<TextareaAutosizeProps, "align" | "height" | "size" | "valid"> & {
        size?: Sizes;
        align?: Alignments;
        valid?: ValidationStates;
        height?: number;
    } & React$1.RefAttributes<HTMLTextAreaElement>, "ref"> & React$1.RefAttributes<any> & {
        theme?: _storybook_core_theming.Theme;
    } & Omit<TextareaAutosizeProps, "align" | "height" | "size" | "valid"> & {
        size?: Sizes;
        align?: Alignments;
        valid?: ValidationStates;
        height?: number;
    } & React$1.RefAttributes<HTMLTextAreaElement>, {}, {}> & {
        displayName: string;
    };
    Button: React$1.ForwardRefExoticComponent<ButtonProps$1 & React$1.RefAttributes<HTMLButtonElement>>;
};

declare const top: "top";
declare const bottom: "bottom";
declare const right: "right";
declare const left: "left";
declare type BasePlacement = typeof top | typeof bottom | typeof right | typeof left;
declare type VariationPlacement = "top-start" | "top-end" | "bottom-start" | "bottom-end" | "right-start" | "right-end" | "left-start" | "left-end";
declare type AutoPlacement = "auto" | "auto-start" | "auto-end";
declare type Placement = AutoPlacement | BasePlacement | VariationPlacement;
declare const beforeRead: "beforeRead";
declare const read: "read";
declare const afterRead: "afterRead";
declare const beforeMain: "beforeMain";
declare const main: "main";
declare const afterMain: "afterMain";
declare const beforeWrite: "beforeWrite";
declare const write: "write";
declare const afterWrite: "afterWrite";
declare type ModifierPhases = typeof beforeRead | typeof read | typeof afterRead | typeof beforeMain | typeof main | typeof afterMain | typeof beforeWrite | typeof write | typeof afterWrite;

declare type Obj = {
    [key: string]: any;
};
declare type VisualViewport = EventTarget & {
    width: number;
    height: number;
    offsetLeft: number;
    offsetTop: number;
    scale: number;
};
declare type Window = {
    innerHeight: number;
    offsetHeight: number;
    innerWidth: number;
    offsetWidth: number;
    pageXOffset: number;
    pageYOffset: number;
    getComputedStyle: typeof getComputedStyle;
    addEventListener(type: any, listener: any, optionsOrUseCapture?: any): void;
    removeEventListener(type: any, listener: any, optionsOrUseCapture?: any): void;
    Element: Element;
    HTMLElement: HTMLElement;
    Node: Node;
    toString(): "[object Window]";
    devicePixelRatio: number;
    visualViewport?: VisualViewport;
    ShadowRoot: ShadowRoot;
};
declare type Rect = {
    width: number;
    height: number;
    x: number;
    y: number;
};
declare type Offsets = {
    y: number;
    x: number;
};
declare type PositioningStrategy = "absolute" | "fixed";
declare type StateRects = {
    reference: Rect;
    popper: Rect;
};
declare type OffsetData = {
    [key in Placement]?: Offsets;
};
declare type State = {
    elements: {
        reference: Element | VirtualElement;
        popper: HTMLElement;
        arrow?: HTMLElement;
    };
    options: OptionsGeneric<any>;
    placement: Placement;
    strategy: PositioningStrategy;
    orderedModifiers: Array<Modifier<any, any>>;
    rects: StateRects;
    scrollParents: {
        reference: Array<Element | Window | VisualViewport>;
        popper: Array<Element | Window | VisualViewport>;
    };
    styles: {
        [key: string]: Partial<CSSStyleDeclaration>;
    };
    attributes: {
        [key: string]: {
            [key: string]: string | boolean;
        };
    };
    modifiersData: {
        arrow?: {
            x?: number;
            y?: number;
            centerOffset: number;
        };
        hide?: {
            isReferenceHidden: boolean;
            hasPopperEscaped: boolean;
            referenceClippingOffsets: SideObject;
            popperEscapeOffsets: SideObject;
        };
        offset?: OffsetData;
        preventOverflow?: Offsets;
        popperOffsets?: Offsets;
        [key: string]: any;
    };
    reset: boolean;
};
declare type SetAction<S> = S | ((prev: S) => S);
declare type Instance = {
    state: State;
    destroy: () => void;
    forceUpdate: () => void;
    update: () => Promise<Partial<State>>;
    setOptions: (setOptionsAction: SetAction<Partial<OptionsGeneric<any>>>) => Promise<Partial<State>>;
};
declare type ModifierArguments<Options extends Obj> = {
    state: State;
    instance: Instance;
    options: Partial<Options>;
    name: string;
};
declare type Modifier<Name, Options extends Obj> = {
    name: Name;
    enabled: boolean;
    phase: ModifierPhases;
    requires?: Array<string>;
    requiresIfExists?: Array<string>;
    fn: (arg0: ModifierArguments<Options>) => State | void;
    effect?: (arg0: ModifierArguments<Options>) => (() => void) | void;
    options?: Partial<Options>;
    data?: Obj;
};
declare type Options = {
    placement: Placement;
    modifiers: Array<Partial<Modifier<any, any>>>;
    strategy: PositioningStrategy;
    onFirstUpdate?: (arg0: Partial<State>) => void;
};
declare type OptionsGeneric<TModifier> = {
    placement: Placement;
    modifiers: Array<TModifier>;
    strategy: PositioningStrategy;
    onFirstUpdate?: (arg0: Partial<State>) => void;
};
declare type SideObject = {
    top: number;
    left: number;
    right: number;
    bottom: number;
};
declare type VirtualElement = {
    getBoundingClientRect: () => ClientRect | DOMRect;
    contextElement?: Element;
};

declare const createPopper: <TModifier extends Partial<Modifier<any, any>>>(reference: Element | VirtualElement, popper: HTMLElement, options?: Partial<OptionsGeneric<TModifier>>) => Instance;

declare type TriggerType = 'click' | 'double-click' | 'right-click' | 'hover' | 'focus';
declare type Config = {
    /**
     * Whether to close the tooltip when its trigger is out of boundary
     * @default false
     */
    closeOnTriggerHidden?: boolean;
    /**
     * Event or events that trigger the tooltip
     * @default hover
     */
    trigger?: TriggerType | TriggerType[] | null;
    /**
     * Delay in hiding the tooltip (ms)
     * @default 0
     */
    delayHide?: number;
    /**
     * Delay in showing the tooltip (ms)
     * @default 0
     */
    delayShow?: number;
    /**
     * Whether to make the tooltip spawn at cursor position
     * @default false
     */
    followCursor?: boolean;
    /**
     * Options to MutationObserver, used internally for updating
     * tooltip position based on its DOM changes
     * @default  { attributes: true, childList: true, subtree: true }
     */
    mutationObserverOptions?: MutationObserverInit | null;
    /**
     * Whether tooltip is shown by default
     * @default false
     */
    defaultVisible?: boolean;
    /**
     * Used to create controlled tooltip
     */
    visible?: boolean;
    /**
     * Called when the visibility of the tooltip changes
     */
    onVisibleChange?: (state: boolean) => void;
    /**
     * If `true`, a click outside the trigger element closes the tooltip
     * @default true
     */
    closeOnOutsideClick?: boolean;
    /**
     * If `true`, hovering the tooltip will keep it open. Normally tooltip closes when the mouse cursor moves out of
     * the trigger element. If it moves to the tooltip element, the tooltip stays open.
     * @default false
     */
    interactive?: boolean;
    /**
     * Alias for popper.js placement, see https://popper.js.org/docs/v2/constructors/#placement
     */
    placement?: Placement;
    /**
     * Shorthand for popper.js offset modifier, see https://popper.js.org/docs/v2/modifiers/offset/
     * @default [0, 6]
     */
    offset?: [number, number];
};
declare type PopperOptions = Partial<Options> & {
    createPopper?: typeof createPopper;
};

declare const TargetContainer: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React__default.ElementType;
} & {
    trigger: Config["trigger"];
}, React__default.DetailedHTMLProps<React__default.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, {}>;
interface WithHideFn {
    onHide: () => void;
}
interface WithTooltipPureProps extends Omit<Config, 'closeOnOutsideClick'>, Omit<ComponentProps<typeof TargetContainer>, 'trigger'>, PopperOptions {
    svg?: boolean;
    withArrows?: boolean;
    hasChrome?: boolean;
    tooltip: ReactNode | ((p: WithHideFn) => ReactNode);
    children: ReactNode;
    onDoubleClick?: () => void;
    /**
     * If `true`, a click outside the trigger element closes the tooltip
     *
     * @default false
     */
    closeOnOutsideClick?: boolean;
}
interface WithTooltipStateProps extends Omit<WithTooltipPureProps, 'onVisibleChange'> {
    startOpen?: boolean;
    onVisibleChange?: (visible: boolean) => void | boolean;
}

declare const LazyWithTooltip: React__default.LazyExoticComponent<({ startOpen, onVisibleChange: onChange, ...rest }: WithTooltipStateProps) => React__default.JSX.Element>;
declare const WithTooltip: (props: ComponentProps<typeof LazyWithTooltip>) => React__default.JSX.Element;
declare const LazyWithTooltipPure: React__default.LazyExoticComponent<({ svg, trigger, closeOnOutsideClick, placement, modifiers, hasChrome, defaultVisible, withArrows, offset, tooltip, children, closeOnTriggerHidden, mutationObserverOptions, delayHide, visible, interactive, delayShow, strategy, followCursor, onVisibleChange, ...props }: WithTooltipPureProps) => React__default.JSX.Element>;
declare const WithTooltipPure: (props: ComponentProps<typeof LazyWithTooltipPure>) => React__default.JSX.Element;

interface TooltipMessageProps {
    title?: ReactNode;
    desc?: ReactNode;
    links?: {
        title: string;
        href?: string;
        onClick?: () => void;
    }[];
}
declare const TooltipMessage: ({ title, desc, links }: TooltipMessageProps) => React__default.JSX.Element;

interface TooltipNoteProps {
    note: string;
}
declare const TooltipNote: ({ note, ...props }: TooltipNoteProps) => React__default.JSX.Element;

interface ItemProps {
    disabled?: boolean;
    href?: string;
    onClick?: (event: SyntheticEvent, ...args: any[]) => any;
}
declare const Item: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React__default.ElementType;
} & ItemProps, React__default.DetailedHTMLProps<React__default.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, {}>;
type LinkWrapperType = (props: any) => ReactNode;
interface ListItemProps extends Omit<ComponentProps<typeof Item>, 'title'> {
    loading?: boolean;
    title?: ReactNode;
    center?: ReactNode;
    right?: ReactNode;
    icon?: ReactNode;
    active?: boolean;
    disabled?: boolean;
    href?: string;
    LinkWrapper?: LinkWrapperType;
    isIndented?: boolean;
}
declare const ListItem: ({ loading, title, center, right, active, disabled, isIndented, href, onClick, icon, LinkWrapper, ...rest }: ListItemProps) => React__default.JSX.Element;

declare const List: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React__default.ElementType;
}, React__default.DetailedHTMLProps<React__default.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, {}>;
interface NormalLink extends Omit<ListItemProps, 'onClick'> {
    id: string;
    onClick?: (event: SyntheticEvent, item: Pick<ListItemProps, 'id' | 'active' | 'disabled' | 'title' | 'href'>) => void;
}
type Link = CustomLink | NormalLink;
/**
 * This is a custom link that can be used in the `TooltipLinkList` component. It allows for custom
 * content to be rendered in the list; it does not have to be a link.
 */
interface CustomLink {
    id: string;
    content: ReactNode;
}
interface TooltipLinkListProps extends ComponentProps<typeof List> {
    links: Link[] | Link[][];
    LinkWrapper?: LinkWrapperType;
}
declare const TooltipLinkList: ({ links, LinkWrapper, ...props }: TooltipLinkListProps) => React__default.JSX.Element;

declare const TabBar: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React__default.ElementType;
}, React__default.DetailedHTMLProps<React__default.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, {}>;
interface TabWrapperProps {
    active: boolean;
    render?: () => ReactElement;
    children?: ReactNode;
}
declare const TabWrapper: FC<TabWrapperProps>;
interface TabsProps {
    children?: ReactElement<{
        children: FC<Addon_RenderOptions & PropsWithChildren>;
        title: ReactNode | FC<PropsWithChildren>;
    }>[];
    id?: string;
    tools?: ReactNode;
    showToolsWhenEmpty?: boolean;
    emptyState?: ReactNode;
    selected?: string;
    actions?: {
        onSelect: (id: string) => void;
    } & Record<string, any>;
    backgroundColor?: string;
    absolute?: boolean;
    bordered?: boolean;
    menuName?: string;
}
declare const Tabs: FC<TabsProps>;
interface TabsStateProps {
    children: TabsProps['children'];
    initial: string;
    absolute: boolean;
    bordered: boolean;
    backgroundColor: string;
    menuName: string;
}
interface TabsStateState {
    selected: string;
}
declare class TabsState extends Component<TabsStateProps, TabsStateState> {
    static defaultProps: TabsStateProps;
    constructor(props: TabsStateProps);
    handlers: {
        onSelect: (id: string) => void;
    };
    render(): React__default.JSX.Element;
}

interface Props {
    title: React__default.ReactNode;
    description?: React__default.ReactNode;
    footer?: React__default.ReactNode;
}
declare const EmptyTabContent: ({ title, description, footer }: Props) => React__default.JSX.Element;

interface ButtonProps extends DetailedHTMLProps<ButtonHTMLAttributes<HTMLButtonElement>, HTMLButtonElement> {
    href?: never;
    target?: never;
}
interface TabButtonProps {
    active?: boolean;
    textColor?: string;
}
declare const TabButton: _storybook_core_theming.StyledComponent<Omit<ButtonProps, "ref"> & RefAttributes<HTMLButtonElement> & {
    theme?: _storybook_core_theming.Theme;
} & TabButtonProps, {}, {}>;
/** @deprecated This component will be removed in Storybook 9.0 */
declare const IconButtonSkeleton: () => React__default.JSX.Element;

interface SeparatorProps {
    force?: boolean;
}
declare const Separator: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React__default.ElementType;
} & SeparatorProps, React__default.DetailedHTMLProps<React__default.HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, {}>;
declare const interleaveSeparators: (list: any[]) => any;

interface UnstyledBarProps extends ScrollAreaProps {
    scrollable?: boolean;
}
interface BarProps extends UnstyledBarProps {
    border?: boolean;
}
declare const Bar: _storybook_core_theming.StyledComponent<UnstyledBarProps & {
    theme?: _storybook_core_theming.Theme;
} & BarProps, {}, {}>;
interface FlexBarProps extends ComponentProps<typeof Bar> {
    border?: boolean;
    backgroundColor?: string;
}
declare const FlexBar: {
    ({ children, backgroundColor, className, ...rest }: FlexBarProps): React__default.JSX.Element;
    displayName: string;
};

interface AddonPanelProps {
    active: boolean;
    children: ReactElement;
}
declare const AddonPanel: ({ active, children }: AddonPanelProps) => React__default.JSX.Element;

type IconType = keyof typeof icons;
declare const Svg: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React__default.ElementType;
}, React__default.SVGProps<SVGSVGElement>, {}>;
interface IconsProps extends ComponentProps<typeof Svg> {
    icon: IconType;
    useSymbol?: boolean;
    onClick?: () => void;
    __suppressDeprecationWarning?: boolean;
}
/**
 * @deprecated No longer used, will be removed in Storybook 9.0 Please use the `@storybook/icons`
 *   package instead.
 */
declare const Icons: ({ icon, useSymbol, __suppressDeprecationWarning, ...props }: IconsProps) => React__default.JSX.Element | null;
interface SymbolsProps {
    icons?: IconType[];
}
/**
 * @deprecated No longer used, will be removed in Storybook 9.0 Please use the `@storybook/icons`
 *   package instead.
 */
declare const Symbols: React__default.NamedExoticComponent<SymbolsProps>;
declare const icons: {
    readonly user: "UserIcon";
    readonly useralt: "UserAltIcon";
    readonly useradd: "UserAddIcon";
    readonly users: "UsersIcon";
    readonly profile: "ProfileIcon";
    readonly facehappy: "FaceHappyIcon";
    readonly faceneutral: "FaceNeutralIcon";
    readonly facesad: "FaceSadIcon";
    readonly accessibility: "AccessibilityIcon";
    readonly accessibilityalt: "AccessibilityAltIcon";
    readonly arrowup: "ChevronUpIcon";
    readonly arrowdown: "ChevronDownIcon";
    readonly arrowleft: "ChevronLeftIcon";
    readonly arrowright: "ChevronRightIcon";
    readonly arrowupalt: "ArrowUpIcon";
    readonly arrowdownalt: "ArrowDownIcon";
    readonly arrowleftalt: "ArrowLeftIcon";
    readonly arrowrightalt: "ArrowRightIcon";
    readonly expandalt: "ExpandAltIcon";
    readonly collapse: "CollapseIcon";
    readonly expand: "ExpandIcon";
    readonly unfold: "UnfoldIcon";
    readonly transfer: "TransferIcon";
    readonly redirect: "RedirectIcon";
    readonly undo: "UndoIcon";
    readonly reply: "ReplyIcon";
    readonly sync: "SyncIcon";
    readonly upload: "UploadIcon";
    readonly download: "DownloadIcon";
    readonly back: "BackIcon";
    readonly proceed: "ProceedIcon";
    readonly refresh: "RefreshIcon";
    readonly globe: "GlobeIcon";
    readonly compass: "CompassIcon";
    readonly location: "LocationIcon";
    readonly pin: "PinIcon";
    readonly time: "TimeIcon";
    readonly dashboard: "DashboardIcon";
    readonly timer: "TimerIcon";
    readonly home: "HomeIcon";
    readonly admin: "AdminIcon";
    readonly info: "InfoIcon";
    readonly question: "QuestionIcon";
    readonly support: "SupportIcon";
    readonly alert: "AlertIcon";
    readonly email: "EmailIcon";
    readonly phone: "PhoneIcon";
    readonly link: "LinkIcon";
    readonly unlink: "LinkBrokenIcon";
    readonly bell: "BellIcon";
    readonly rss: "RSSIcon";
    readonly sharealt: "ShareAltIcon";
    readonly share: "ShareIcon";
    readonly circle: "CircleIcon";
    readonly circlehollow: "CircleHollowIcon";
    readonly bookmarkhollow: "BookmarkHollowIcon";
    readonly bookmark: "BookmarkIcon";
    readonly hearthollow: "HeartHollowIcon";
    readonly heart: "HeartIcon";
    readonly starhollow: "StarHollowIcon";
    readonly star: "StarIcon";
    readonly certificate: "CertificateIcon";
    readonly verified: "VerifiedIcon";
    readonly thumbsup: "ThumbsUpIcon";
    readonly shield: "ShieldIcon";
    readonly basket: "BasketIcon";
    readonly beaker: "BeakerIcon";
    readonly hourglass: "HourglassIcon";
    readonly flag: "FlagIcon";
    readonly cloudhollow: "CloudHollowIcon";
    readonly edit: "EditIcon";
    readonly cog: "CogIcon";
    readonly nut: "NutIcon";
    readonly wrench: "WrenchIcon";
    readonly ellipsis: "EllipsisIcon";
    readonly check: "CheckIcon";
    readonly form: "FormIcon";
    readonly batchdeny: "BatchDenyIcon";
    readonly batchaccept: "BatchAcceptIcon";
    readonly controls: "ControlsIcon";
    readonly plus: "PlusIcon";
    readonly closeAlt: "CloseAltIcon";
    readonly cross: "CrossIcon";
    readonly trash: "TrashIcon";
    readonly pinalt: "PinAltIcon";
    readonly unpin: "UnpinIcon";
    readonly add: "AddIcon";
    readonly subtract: "SubtractIcon";
    readonly close: "CloseIcon";
    readonly delete: "DeleteIcon";
    readonly passed: "PassedIcon";
    readonly changed: "ChangedIcon";
    readonly failed: "FailedIcon";
    readonly clear: "ClearIcon";
    readonly comment: "CommentIcon";
    readonly commentadd: "CommentAddIcon";
    readonly requestchange: "RequestChangeIcon";
    readonly comments: "CommentsIcon";
    readonly lock: "LockIcon";
    readonly unlock: "UnlockIcon";
    readonly key: "KeyIcon";
    readonly outbox: "OutboxIcon";
    readonly credit: "CreditIcon";
    readonly button: "ButtonIcon";
    readonly type: "TypeIcon";
    readonly pointerdefault: "PointerDefaultIcon";
    readonly pointerhand: "PointerHandIcon";
    readonly browser: "BrowserIcon";
    readonly tablet: "TabletIcon";
    readonly mobile: "MobileIcon";
    readonly watch: "WatchIcon";
    readonly sidebar: "SidebarIcon";
    readonly sidebaralt: "SidebarAltIcon";
    readonly sidebaralttoggle: "SidebarAltToggleIcon";
    readonly sidebartoggle: "SidebarToggleIcon";
    readonly bottombar: "BottomBarIcon";
    readonly bottombartoggle: "BottomBarToggleIcon";
    readonly cpu: "CPUIcon";
    readonly database: "DatabaseIcon";
    readonly memory: "MemoryIcon";
    readonly structure: "StructureIcon";
    readonly box: "BoxIcon";
    readonly power: "PowerIcon";
    readonly photo: "PhotoIcon";
    readonly component: "ComponentIcon";
    readonly grid: "GridIcon";
    readonly outline: "OutlineIcon";
    readonly photodrag: "PhotoDragIcon";
    readonly search: "SearchIcon";
    readonly zoom: "ZoomIcon";
    readonly zoomout: "ZoomOutIcon";
    readonly zoomreset: "ZoomResetIcon";
    readonly eye: "EyeIcon";
    readonly eyeclose: "EyeCloseIcon";
    readonly lightning: "LightningIcon";
    readonly lightningoff: "LightningOffIcon";
    readonly contrast: "ContrastIcon";
    readonly switchalt: "SwitchAltIcon";
    readonly mirror: "MirrorIcon";
    readonly grow: "GrowIcon";
    readonly paintbrush: "PaintBrushIcon";
    readonly ruler: "RulerIcon";
    readonly stop: "StopIcon";
    readonly camera: "CameraIcon";
    readonly video: "VideoIcon";
    readonly speaker: "SpeakerIcon";
    readonly play: "PlayIcon";
    readonly playback: "PlayBackIcon";
    readonly playnext: "PlayNextIcon";
    readonly rewind: "RewindIcon";
    readonly fastforward: "FastForwardIcon";
    readonly stopalt: "StopAltIcon";
    readonly sidebyside: "SideBySideIcon";
    readonly stacked: "StackedIcon";
    readonly sun: "SunIcon";
    readonly moon: "MoonIcon";
    readonly book: "BookIcon";
    readonly document: "DocumentIcon";
    readonly copy: "CopyIcon";
    readonly category: "CategoryIcon";
    readonly folder: "FolderIcon";
    readonly print: "PrintIcon";
    readonly graphline: "GraphLineIcon";
    readonly calendar: "CalendarIcon";
    readonly graphbar: "GraphBarIcon";
    readonly menu: "MenuIcon";
    readonly menualt: "MenuIcon";
    readonly filter: "FilterIcon";
    readonly docchart: "DocChartIcon";
    readonly doclist: "DocListIcon";
    readonly markup: "MarkupIcon";
    readonly bold: "BoldIcon";
    readonly paperclip: "PaperClipIcon";
    readonly listordered: "ListOrderedIcon";
    readonly listunordered: "ListUnorderedIcon";
    readonly paragraph: "ParagraphIcon";
    readonly markdown: "MarkdownIcon";
    readonly repository: "RepoIcon";
    readonly commit: "CommitIcon";
    readonly branch: "BranchIcon";
    readonly pullrequest: "PullRequestIcon";
    readonly merge: "MergeIcon";
    readonly apple: "AppleIcon";
    readonly linux: "LinuxIcon";
    readonly ubuntu: "UbuntuIcon";
    readonly windows: "WindowsIcon";
    readonly storybook: "StorybookIcon";
    readonly azuredevops: "AzureDevOpsIcon";
    readonly bitbucket: "BitbucketIcon";
    readonly chrome: "ChromeIcon";
    readonly chromatic: "ChromaticIcon";
    readonly componentdriven: "ComponentDrivenIcon";
    readonly discord: "DiscordIcon";
    readonly facebook: "FacebookIcon";
    readonly figma: "FigmaIcon";
    readonly gdrive: "GDriveIcon";
    readonly github: "GithubIcon";
    readonly gitlab: "GitlabIcon";
    readonly google: "GoogleIcon";
    readonly graphql: "GraphqlIcon";
    readonly medium: "MediumIcon";
    readonly redux: "ReduxIcon";
    readonly twitter: "TwitterIcon";
    readonly youtube: "YoutubeIcon";
    readonly vscode: "VSCodeIcon";
};

type StorybookLogoProps = {
    alt: string;
} & React__default.SVGAttributes<SVGSVGElement>;
declare const StorybookLogo: ({ alt, ...props }: StorybookLogoProps) => React__default.JSX.Element;

declare const StorybookIcon: (props: React__default.SVGAttributes<SVGElement>) => React__default.JSX.Element;

interface Progress {
    value: number;
    message: string;
    modules?: {
        complete: number;
        total: number;
    };
}
interface LoaderProps extends React__default.HTMLAttributes<HTMLDivElement> {
    progress?: Progress;
    error?: Error;
    size?: number;
}
declare const Loader: ({ progress, error, size, ...props }: LoaderProps) => React__default.JSX.Element;

declare const Wrapper: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React__default.ElementType;
} & {
    size: number;
}, React__default.DetailedHTMLProps<React__default.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, {}>;
interface ProgressSpinnerProps extends Omit<ComponentProps<typeof Wrapper>, 'size'> {
    percentage?: number;
    running?: boolean;
    size?: number;
    width?: number;
    children?: React__default.ReactNode;
}
declare const ProgressSpinner: ({ percentage, running, size, width, children, ...props }: ProgressSpinnerProps) => React__default.JSX.Element;

declare const getStoryHref: (baseUrl: string, storyId: string, additionalParams?: Record<string, string>) => string;

declare const nameSpaceClassNames: ({ ...props }: {
    [x: string]: any;
}, key: string) => {
    [x: string]: any;
};

/**
 * This is a "local" reset to style subtrees with Storybook styles
 *
 * We can't style individual elements (e.g. h1, h2, etc.) in here because the CSS specificity is too
 * high, so those styles can too easily override child elements that are not expecting it.
 */
declare const ResetWrapper: _storybook_core_theming.StyledComponent<{
    theme?: _storybook_core_theming.Theme;
    as?: React.ElementType;
}, React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, {}>;

declare const codeCommon: FunctionInterpolation;
declare const withReset: FunctionInterpolation;

interface ClipboardCodeProps {
    code: string;
}
declare const ClipboardCode: ({ code, ...props }: ClipboardCodeProps) => React__default.JSX.Element;

declare const components: {
    h1: (props: React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>) => React$1.JSX.Element;
    h2: (props: React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>) => React$1.JSX.Element;
    h3: (props: React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>) => React$1.JSX.Element;
    h4: (props: React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>) => React$1.JSX.Element;
    h5: (props: React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>) => React$1.JSX.Element;
    h6: (props: React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLHeadingElement>, HTMLHeadingElement>) => React$1.JSX.Element;
    pre: (props: React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLPreElement>, HTMLPreElement>) => React$1.JSX.Element;
    a: (props: React$1.DetailedHTMLProps<React$1.AnchorHTMLAttributes<HTMLAnchorElement>, HTMLAnchorElement>) => React$1.JSX.Element;
    hr: (props: React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLHRElement>, HTMLHRElement>) => React$1.JSX.Element;
    dl: (props: React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLDListElement>, HTMLDListElement>) => React$1.JSX.Element;
    blockquote: (props: React$1.DetailedHTMLProps<React$1.BlockquoteHTMLAttributes<HTMLElement>, HTMLElement>) => React$1.JSX.Element;
    table: (props: React$1.DetailedHTMLProps<React$1.TableHTMLAttributes<HTMLTableElement>, HTMLTableElement>) => React$1.JSX.Element;
    img: (props: React$1.DetailedHTMLProps<React$1.ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement>) => React$1.JSX.Element;
    div: (props: React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLDivElement>, HTMLDivElement>) => React$1.JSX.Element;
    span: (props: React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>) => React$1.JSX.Element;
    li: (props: React$1.DetailedHTMLProps<React$1.LiHTMLAttributes<HTMLLIElement>, HTMLLIElement>) => React$1.JSX.Element;
    ul: (props: React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLUListElement>, HTMLUListElement>) => React$1.JSX.Element;
    ol: (props: React$1.DetailedHTMLProps<React$1.OlHTMLAttributes<HTMLOListElement>, HTMLOListElement>) => React$1.JSX.Element;
    p: (props: React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLParagraphElement>, HTMLParagraphElement>) => React$1.JSX.Element;
    code: (props: React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLElement>, HTMLElement>) => React$1.JSX.Element;
    tt: (props: React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLTitleElement>, HTMLTitleElement>) => React$1.JSX.Element;
    resetwrapper: (props: React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLDivElement>, HTMLDivElement>) => React$1.JSX.Element;
};
declare const resetComponents: Record<string, ElementType>;

export { A, ActionBar, type ActionItem, AddonPanel, Badge, Bar, Blockquote, Button, ClipboardCode, Code, DL, Div, DocumentWrapper, EmptyTabContent, ErrorFormatter, FlexBar, Form, H1, H2, H3, H4, H5, H6, HR, IconButton, IconButtonSkeleton, Icons, type IconsProps, Img, LI, Link$1 as Link, ListItem, Loader, Modal, OL, P, Placeholder, Pre, ProgressSpinner, ResetWrapper, ScrollArea, Separator, Spaced, Span, StorybookIcon, StorybookLogo, type SupportedLanguage, Symbols, SyntaxHighlighter, type SyntaxHighlighterFormatTypes, type SyntaxHighlighterProps, type SyntaxHighlighterRendererProps, TT, TabBar, TabButton, TabWrapper, Table, Tabs, TabsState, TooltipLinkList, type Link as TooltipLinkListLink, TooltipMessage, TooltipNote, UL, WithTooltip, WithTooltipPure, Zoom, codeCommon, components, createCopyToClipboardFunction, getStoryHref, icons, interleaveSeparators, nameSpaceClassNames, resetComponents, withReset };
