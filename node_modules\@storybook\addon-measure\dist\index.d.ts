import * as core_dist_types from 'storybook/internal/types';

interface MeasureParameters {
    /**
     * Measure configuration
     *
     * @see https://storybook.js.org/docs/essentials/measure-and-outline#parameters
     */
    measure: {
        /** Remove the addon panel and disable the addon's behavior */
        disable?: boolean;
    };
}

declare const _default: () => core_dist_types.ProjectAnnotations<core_dist_types.Renderer>;

export { MeasureParameters, _default as default };
