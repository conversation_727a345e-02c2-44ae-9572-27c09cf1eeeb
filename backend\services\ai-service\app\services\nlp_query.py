"""
Natural Language Processing Query Service.
Enables natural language querying of HR data and generates insights.
"""

import logging
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timedelta
import asyncio
import re
import json

import spacy
import pandas as pd
from transformers import pipeline, AutoTokenizer, AutoModel
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.exceptions import DataProcessingError, ValidationError
from app.models.nlp import NLPQueryRequest, NLPQueryResult, QueryIntent, DataInsight

logger = logging.getLogger(__name__)


class NLPQueryService:
    """Natural language processing service for HR data queries."""
    
    def __init__(self):
        self.nlp = None
        self.intent_classifier = None
        self.entity_extractor = None
        self.query_templates = {}
        self.schema_info = {}
        self.vectorizer = None
        self.initialized = False
        
    async def initialize(self):
        """Initialize NLP models and query templates."""
        try:
            # Load spaCy model
            self.nlp = spacy.load(settings.SPACY_MODEL)
            
            # Initialize intent classification pipeline
            self.intent_classifier = pipeline(
                "text-classification",
                model="microsoft/DialoGPT-medium",
                tokenizer="microsoft/DialoGPT-medium"
            )
            
            # Load query templates
            await self._load_query_templates()
            
            # Load database schema information
            await self._load_schema_info()
            
            # Initialize vectorizer for semantic matching
            self.vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words='english',
                ngram_range=(1, 2)
            )
            
            self.initialized = True
            logger.info("NLP Query Service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize NLP Query Service: {e}")
            raise DataProcessingError(f"Initialization failed: {e}")
    
    async def process_query(self, query: str, user_context: Dict[str, Any] = None) -> NLPQueryResult:
        """Process natural language query and return structured results."""
        if not self.initialized:
            await self.initialize()
        
        try:
            # Clean and preprocess query
            cleaned_query = self._preprocess_query(query)
            
            # Extract intent and entities
            intent = await self._extract_intent(cleaned_query)
            entities = await self._extract_entities(cleaned_query)
            
            # Generate SQL query
            sql_query = await self._generate_sql_query(intent, entities, user_context)
            
            # Execute query (simulated for now)
            results = await self._execute_query(sql_query, user_context)
            
            # Generate insights
            insights = await self._generate_insights(results, intent, entities)
            
            # Format response
            response = await self._format_response(results, insights, intent)
            
            return NLPQueryResult(
                original_query=query,
                cleaned_query=cleaned_query,
                intent=intent,
                entities=entities,
                sql_query=sql_query,
                results=results,
                insights=insights,
                response=response,
                confidence=self._calculate_query_confidence(intent, entities),
                processed_at=datetime.utcnow(),
            )
            
        except Exception as e:
            logger.error(f"Query processing failed: {e}")
            raise DataProcessingError(f"Query processing failed: {e}")
    
    async def get_query_suggestions(self, partial_query: str, limit: int = 5) -> List[str]:
        """Get query suggestions based on partial input."""
        try:
            suggestions = []
            
            # Common HR query patterns
            common_queries = [
                "Show me employee turnover rate for this year",
                "What is the average salary by department?",
                "How many employees joined last month?",
                "Show performance ratings distribution",
                "Which department has the highest satisfaction score?",
                "What are the top skills in the engineering team?",
                "Show me employees due for performance review",
                "What is the diversity ratio in management positions?",
                "How many open positions do we have?",
                "Show training completion rates by department",
            ]
            
            # Filter suggestions based on partial query
            if partial_query:
                for query in common_queries:
                    if partial_query.lower() in query.lower():
                        suggestions.append(query)
            else:
                suggestions = common_queries[:limit]
            
            return suggestions[:limit]
            
        except Exception as e:
            logger.error(f"Query suggestions failed: {e}")
            return []
    
    async def explain_query(self, query: str) -> Dict[str, Any]:
        """Explain how a natural language query will be processed."""
        try:
            cleaned_query = self._preprocess_query(query)
            intent = await self._extract_intent(cleaned_query)
            entities = await self._extract_entities(cleaned_query)
            
            explanation = {
                'query_understanding': {
                    'intent': intent.intent_type,
                    'confidence': intent.confidence,
                    'entities_found': [e.text for e in entities],
                },
                'data_sources': self._identify_data_sources(intent, entities),
                'expected_output': self._describe_expected_output(intent),
                'processing_steps': [
                    'Parse natural language query',
                    'Extract intent and entities',
                    'Map to database schema',
                    'Generate SQL query',
                    'Execute query',
                    'Generate insights',
                    'Format response',
                ],
            }
            
            return explanation
            
        except Exception as e:
            logger.error(f"Query explanation failed: {e}")
            return {'error': str(e)}
    
    def _preprocess_query(self, query: str) -> str:
        """Clean and preprocess the natural language query."""
        # Convert to lowercase
        query = query.lower().strip()
        
        # Remove extra whitespace
        query = re.sub(r'\s+', ' ', query)
        
        # Handle common abbreviations
        abbreviations = {
            'hr': 'human resources',
            'emp': 'employee',
            'dept': 'department',
            'mgr': 'manager',
            'perf': 'performance',
            'sal': 'salary',
        }
        
        for abbr, full in abbreviations.items():
            query = re.sub(r'\b' + abbr + r'\b', full, query)
        
        return query
    
    async def _extract_intent(self, query: str) -> QueryIntent:
        """Extract intent from the natural language query."""
        try:
            # Define intent patterns
            intent_patterns = {
                'count': ['how many', 'count', 'number of', 'total'],
                'average': ['average', 'mean', 'avg'],
                'sum': ['total', 'sum', 'aggregate'],
                'list': ['show', 'list', 'display', 'get'],
                'compare': ['compare', 'vs', 'versus', 'difference'],
                'trend': ['trend', 'over time', 'change', 'growth'],
                'filter': ['where', 'with', 'having', 'filter'],
                'rank': ['top', 'bottom', 'highest', 'lowest', 'best', 'worst'],
            }
            
            # Score each intent
            intent_scores = {}
            for intent_type, patterns in intent_patterns.items():
                score = 0
                for pattern in patterns:
                    if pattern in query:
                        score += 1
                intent_scores[intent_type] = score
            
            # Get the highest scoring intent
            if intent_scores:
                best_intent = max(intent_scores.items(), key=lambda x: x[1])
                intent_type = best_intent[0]
                confidence = min(best_intent[1] / len(intent_patterns[intent_type]), 1.0)
            else:
                intent_type = 'list'  # Default intent
                confidence = 0.5
            
            return QueryIntent(
                intent_type=intent_type,
                confidence=confidence,
                raw_query=query,
            )
            
        except Exception as e:
            logger.error(f"Intent extraction failed: {e}")
            return QueryIntent(
                intent_type='unknown',
                confidence=0.0,
                raw_query=query,
            )
    
    async def _extract_entities(self, query: str) -> List[Dict[str, Any]]:
        """Extract entities from the natural language query."""
        try:
            doc = self.nlp(query)
            entities = []
            
            # Extract named entities
            for ent in doc.ents:
                entities.append({
                    'text': ent.text,
                    'label': ent.label_,
                    'start': ent.start_char,
                    'end': ent.end_char,
                    'description': spacy.explain(ent.label_),
                })
            
            # Extract HR-specific entities
            hr_entities = await self._extract_hr_entities(query)
            entities.extend(hr_entities)
            
            return entities
            
        except Exception as e:
            logger.error(f"Entity extraction failed: {e}")
            return []
    
    async def _extract_hr_entities(self, query: str) -> List[Dict[str, Any]]:
        """Extract HR-specific entities from query."""
        entities = []
        
        # Department patterns
        departments = [
            'engineering', 'sales', 'marketing', 'hr', 'human resources',
            'finance', 'operations', 'it', 'legal', 'support'
        ]
        
        for dept in departments:
            if dept in query:
                entities.append({
                    'text': dept,
                    'label': 'DEPARTMENT',
                    'type': 'hr_entity',
                    'description': 'Department name',
                })
        
        # Job titles
        job_titles = [
            'manager', 'director', 'engineer', 'analyst', 'specialist',
            'coordinator', 'lead', 'senior', 'junior', 'intern'
        ]
        
        for title in job_titles:
            if title in query:
                entities.append({
                    'text': title,
                    'label': 'JOB_TITLE',
                    'type': 'hr_entity',
                    'description': 'Job title',
                })
        
        # Time periods
        time_patterns = [
            r'last (\w+)', r'this (\w+)', r'(\d+) (days?|weeks?|months?|years?)',
            r'(january|february|march|april|may|june|july|august|september|october|november|december)',
            r'(q1|q2|q3|q4)', r'(\d{4})'
        ]
        
        for pattern in time_patterns:
            matches = re.finditer(pattern, query, re.IGNORECASE)
            for match in matches:
                entities.append({
                    'text': match.group(),
                    'label': 'TIME_PERIOD',
                    'type': 'hr_entity',
                    'description': 'Time period',
                })
        
        return entities
    
    async def _generate_sql_query(self, intent: QueryIntent, entities: List[Dict[str, Any]], 
                                user_context: Dict[str, Any] = None) -> str:
        """Generate SQL query based on intent and entities."""
        try:
            # This is a simplified example - in practice, this would be much more sophisticated
            base_tables = {
                'employee': 'employees',
                'department': 'departments',
                'salary': 'salaries',
                'performance': 'performance_reviews',
                'attendance': 'attendance_records',
            }
            
            # Determine main table based on entities
            main_table = 'employees'  # Default
            
            # Build SELECT clause based on intent
            if intent.intent_type == 'count':
                select_clause = 'SELECT COUNT(*)'
            elif intent.intent_type == 'average':
                select_clause = 'SELECT AVG(salary)'
            elif intent.intent_type == 'sum':
                select_clause = 'SELECT SUM(salary)'
            else:
                select_clause = 'SELECT *'
            
            # Build FROM clause
            from_clause = f'FROM {main_table}'
            
            # Build WHERE clause based on entities
            where_conditions = []
            for entity in entities:
                if entity['label'] == 'DEPARTMENT':
                    where_conditions.append(f"department = '{entity['text']}'")
                elif entity['label'] == 'TIME_PERIOD':
                    # Handle time period filtering
                    where_conditions.append("created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)")
            
            where_clause = ''
            if where_conditions:
                where_clause = 'WHERE ' + ' AND '.join(where_conditions)
            
            # Combine clauses
            sql_query = f"{select_clause} {from_clause} {where_clause}".strip()
            
            # Add LIMIT for safety
            if 'LIMIT' not in sql_query.upper():
                sql_query += ' LIMIT 100'
            
            return sql_query
            
        except Exception as e:
            logger.error(f"SQL generation failed: {e}")
            return "SELECT 'Error generating query' as message"
    
    async def _execute_query(self, sql_query: str, user_context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Execute the generated SQL query (simulated for now)."""
        try:
            # In a real implementation, this would execute against the actual database
            # For now, return simulated data
            
            simulated_results = [
                {'id': 1, 'name': 'John Doe', 'department': 'Engineering', 'salary': 75000},
                {'id': 2, 'name': 'Jane Smith', 'department': 'Marketing', 'salary': 65000},
                {'id': 3, 'name': 'Bob Johnson', 'department': 'Sales', 'salary': 70000},
            ]
            
            return simulated_results
            
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            return [{'error': f'Query execution failed: {e}'}]
    
    async def _generate_insights(self, results: List[Dict[str, Any]], 
                               intent: QueryIntent, entities: List[Dict[str, Any]]) -> List[DataInsight]:
        """Generate insights from query results."""
        insights = []
        
        try:
            if not results or 'error' in results[0]:
                return insights
            
            # Generate basic insights
            insights.append(DataInsight(
                type='summary',
                title='Query Results Summary',
                description=f'Found {len(results)} records matching your query',
                value=len(results),
                confidence=0.9,
            ))
            
            # Generate specific insights based on intent
            if intent.intent_type == 'count':
                insights.append(DataInsight(
                    type='count',
                    title='Total Count',
                    description='Total number of records found',
                    value=len(results),
                    confidence=0.95,
                ))
            
            elif intent.intent_type == 'average' and results:
                # Calculate average if numeric data is present
                numeric_fields = [k for k, v in results[0].items() if isinstance(v, (int, float))]
                for field in numeric_fields:
                    values = [r[field] for r in results if field in r]
                    if values:
                        avg_value = sum(values) / len(values)
                        insights.append(DataInsight(
                            type='average',
                            title=f'Average {field.title()}',
                            description=f'Average value of {field}',
                            value=avg_value,
                            confidence=0.9,
                        ))
            
            return insights
            
        except Exception as e:
            logger.error(f"Insight generation failed: {e}")
            return insights
    
    async def _format_response(self, results: List[Dict[str, Any]], 
                             insights: List[DataInsight], intent: QueryIntent) -> str:
        """Format the response in natural language."""
        try:
            if not results or 'error' in results[0]:
                return "I couldn't find any data matching your query. Please try rephrasing your question."
            
            # Generate response based on intent
            if intent.intent_type == 'count':
                count = len(results)
                return f"I found {count} records matching your query."
            
            elif intent.intent_type == 'list':
                if len(results) <= 5:
                    # Show all results
                    response = f"Here are the {len(results)} results I found:\n"
                    for i, result in enumerate(results, 1):
                        response += f"{i}. {self._format_record(result)}\n"
                else:
                    # Show first few results
                    response = f"I found {len(results)} results. Here are the first 5:\n"
                    for i, result in enumerate(results[:5], 1):
                        response += f"{i}. {self._format_record(result)}\n"
                    response += f"... and {len(results) - 5} more results."
                
                return response
            
            elif intent.intent_type == 'average':
                # Find numeric insights
                avg_insights = [i for i in insights if i.type == 'average']
                if avg_insights:
                    response = "Here are the averages I calculated:\n"
                    for insight in avg_insights:
                        response += f"• {insight.title}: {insight.value:.2f}\n"
                    return response
            
            # Default response
            return f"I found {len(results)} records. Here's a summary of the key insights I discovered."
            
        except Exception as e:
            logger.error(f"Response formatting failed: {e}")
            return "I found some data, but had trouble formatting the response."
    
    def _format_record(self, record: Dict[str, Any]) -> str:
        """Format a single record for display."""
        # Simple formatting - show key fields
        key_fields = ['name', 'title', 'department', 'salary']
        formatted_parts = []
        
        for field in key_fields:
            if field in record:
                formatted_parts.append(f"{field}: {record[field]}")
        
        if not formatted_parts:
            # Fallback to first few fields
            formatted_parts = [f"{k}: {v}" for k, v in list(record.items())[:3]]
        
        return ", ".join(formatted_parts)
    
    def _calculate_query_confidence(self, intent: QueryIntent, entities: List[Dict[str, Any]]) -> float:
        """Calculate confidence in query processing."""
        base_confidence = intent.confidence
        
        # Boost confidence if entities were found
        if entities:
            entity_boost = min(len(entities) * 0.1, 0.3)
            base_confidence += entity_boost
        
        return min(base_confidence, 1.0)
    
    def _identify_data_sources(self, intent: QueryIntent, entities: List[Dict[str, Any]]) -> List[str]:
        """Identify which data sources will be used."""
        sources = ['employees']  # Default
        
        for entity in entities:
            if entity['label'] == 'DEPARTMENT':
                sources.append('departments')
            elif 'salary' in intent.raw_query:
                sources.append('salaries')
            elif 'performance' in intent.raw_query:
                sources.append('performance_reviews')
        
        return list(set(sources))
    
    def _describe_expected_output(self, intent: QueryIntent) -> str:
        """Describe what kind of output to expect."""
        descriptions = {
            'count': 'A numerical count of records',
            'average': 'Average values for numerical fields',
            'sum': 'Sum totals for numerical fields',
            'list': 'A list of matching records',
            'compare': 'Comparison between different groups',
            'trend': 'Trend analysis over time',
            'rank': 'Ranked list of results',
        }
        
        return descriptions.get(intent.intent_type, 'Relevant data matching your query')
    
    async def _load_query_templates(self):
        """Load predefined query templates."""
        self.query_templates = {
            'employee_count': {
                'pattern': 'how many employees',
                'sql': 'SELECT COUNT(*) FROM employees',
                'description': 'Count total employees',
            },
            'department_salary': {
                'pattern': 'average salary by department',
                'sql': 'SELECT department, AVG(salary) FROM employees GROUP BY department',
                'description': 'Average salary by department',
            },
            # Add more templates as needed
        }
    
    async def _load_schema_info(self):
        """Load database schema information."""
        self.schema_info = {
            'employees': {
                'columns': ['id', 'name', 'email', 'department', 'salary', 'hire_date'],
                'description': 'Employee information',
            },
            'departments': {
                'columns': ['id', 'name', 'manager_id', 'budget'],
                'description': 'Department information',
            },
            # Add more schema info as needed
        }
