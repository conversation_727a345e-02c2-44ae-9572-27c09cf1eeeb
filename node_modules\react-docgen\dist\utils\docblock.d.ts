/**
 * Helper functions to work with docblock comments.
 */
import type { NodePath } from '@babel/traverse';
/**
 * Given a path, this function returns the closest preceding docblock if it
 * exists.
 */
export declare function getDocblock(path: NodePath, trailing?: boolean): string | null;
/**
 * Given a string, this functions returns an object with doclet names as keys
 * and their "content" as values.
 */
export declare function getDoclets(str: string): Record<string, string>;
