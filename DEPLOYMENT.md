# PeopleNest HRMS - Deployment Guide

This document provides comprehensive instructions for deploying the PeopleNest HRMS application to production environments.

## Overview

The PeopleNest HRMS application is designed for cloud-native deployment using:

- **Containerization**: Docker with multi-stage builds
- **Orchestration**: Kubernetes with Helm charts
- **CI/CD**: GitHub Actions with automated testing and deployment
- **Monitoring**: Prometheus, Grafana, and distributed tracing
- **Security**: Zero-trust architecture with SOC 2 compliance

## Architecture

### Microservices Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   AI Service    │
│   (React/TS)    │    │   (NestJS)      │    │   (FastAPI)     │
│   Port: 80      │    │   Port: 3001    │    │   Port: 8003    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              Infrastructure                     │
         │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌────────┐ │
         │  │PostgreSQL│ │ MongoDB │ │  Redis  │ │ Kafka  │ │
         │  │Port: 5432│ │Port:27017│ │Port:6379│ │Port:9092│ │
         │  └─────────┘ └─────────┘ └─────────┘ └────────┘ │
         └─────────────────────────────────────────────────┘
```

### Deployment Environments

1. **Development**: Local Docker Compose
2. **Staging**: Kubernetes cluster with reduced resources
3. **Production**: Kubernetes cluster with high availability

## Prerequisites

### System Requirements

- **Kubernetes**: v1.25+
- **Docker**: v20.10+
- **Helm**: v3.8+
- **kubectl**: v1.25+
- **Node.js**: v20+ (for local development)
- **Python**: v3.11+ (for AI services)

### Infrastructure Requirements

#### Minimum Production Requirements

- **CPU**: 8 cores
- **Memory**: 16 GB RAM
- **Storage**: 100 GB SSD
- **Network**: 1 Gbps bandwidth

#### Recommended Production Requirements

- **CPU**: 16 cores
- **Memory**: 32 GB RAM
- **Storage**: 500 GB SSD
- **Network**: 10 Gbps bandwidth

### Cloud Provider Setup

#### AWS EKS

```bash
# Create EKS cluster
eksctl create cluster \
  --name peoplenest-prod \
  --version 1.25 \
  --region us-east-1 \
  --nodegroup-name standard-workers \
  --node-type m5.xlarge \
  --nodes 3 \
  --nodes-min 1 \
  --nodes-max 10 \
  --managed
```

#### Google GKE

```bash
# Create GKE cluster
gcloud container clusters create peoplenest-prod \
  --zone us-central1-a \
  --machine-type n1-standard-4 \
  --num-nodes 3 \
  --enable-autoscaling \
  --min-nodes 1 \
  --max-nodes 10
```

#### Azure AKS

```bash
# Create AKS cluster
az aks create \
  --resource-group peoplenest-rg \
  --name peoplenest-prod \
  --node-count 3 \
  --node-vm-size Standard_D4s_v3 \
  --enable-cluster-autoscaler \
  --min-count 1 \
  --max-count 10
```

## Quick Deployment

### 1. Clone Repository

```bash
git clone https://github.com/your-org/peoplenest-hrms.git
cd peoplenest-hrms
```

### 2. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Update configuration
nano .env
```

### 3. Update Secrets

```bash
# Update Kubernetes secrets
nano k8s/secrets.yaml

# Important: Change all CHANGE_ME_IN_PRODUCTION values
```

### 4. Deploy to Kubernetes

```bash
# Make deployment script executable
chmod +x scripts/deploy.sh

# Deploy to production
./scripts/deploy.sh --environment production
```

## Detailed Deployment Steps

### 1. Container Images

#### Build Images Locally

```bash
# Build backend image
docker build -t peoplenest/backend:latest ./backend

# Build frontend image
docker build -t peoplenest/frontend:latest ./frontend

# Build AI service image
docker build -t peoplenest/ai-service:latest ./backend/services/ai-service
```

#### Push to Registry

```bash
# Tag images for registry
docker tag peoplenest/backend:latest ghcr.io/your-org/peoplenest/backend:latest
docker tag peoplenest/frontend:latest ghcr.io/your-org/peoplenest/frontend:latest
docker tag peoplenest/ai-service:latest ghcr.io/your-org/peoplenest/ai-service:latest

# Push to registry
docker push ghcr.io/your-org/peoplenest/backend:latest
docker push ghcr.io/your-org/peoplenest/frontend:latest
docker push ghcr.io/your-org/peoplenest/ai-service:latest
```

### 2. Kubernetes Deployment

#### Create Namespace

```bash
kubectl apply -f k8s/namespace.yaml
```

#### Apply Secrets and ConfigMaps

```bash
# Apply secrets (ensure they're updated for production)
kubectl apply -f k8s/secrets.yaml

# Apply configuration
kubectl apply -f k8s/configmap.yaml
```

#### Deploy Infrastructure Services

```bash
# Deploy databases
kubectl apply -f k8s/postgres-deployment.yaml
kubectl apply -f k8s/mongodb-deployment.yaml
kubectl apply -f k8s/redis-deployment.yaml

# Wait for databases to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=postgres -n peoplenest --timeout=300s
```

#### Deploy Application Services

```bash
# Deploy backend
kubectl apply -f k8s/backend-deployment.yaml

# Deploy AI service
kubectl apply -f k8s/ai-service-deployment.yaml

# Deploy frontend
kubectl apply -f k8s/frontend-deployment.yaml

# Wait for deployments
kubectl rollout status deployment/backend -n peoplenest
kubectl rollout status deployment/frontend -n peoplenest
```

#### Deploy Monitoring

```bash
# Deploy monitoring stack
kubectl apply -f k8s/monitoring.yaml

# Wait for monitoring services
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=prometheus -n peoplenest --timeout=300s
```

### 3. Database Setup

#### Run Migrations

```bash
# Get backend pod name
BACKEND_POD=$(kubectl get pods -n peoplenest -l app.kubernetes.io/name=backend -o jsonpath='{.items[0].metadata.name}')

# Run migrations
kubectl exec -n peoplenest $BACKEND_POD -- npm run migration:run

# Create admin user
kubectl exec -n peoplenest $BACKEND_POD -- npm run admin:create
```

### 4. Configure Ingress

#### Install Ingress Controller

```bash
# Install NGINX Ingress Controller
helm upgrade --install ingress-nginx ingress-nginx \
  --repo https://kubernetes.github.io/ingress-nginx \
  --namespace ingress-nginx --create-namespace
```

#### Apply Ingress Configuration

```bash
kubectl apply -f k8s/ingress.yaml
```

## Configuration

### Environment Variables

#### Backend Configuration

```yaml
# Database
DATABASE_HOST: postgres-service
DATABASE_PORT: 5432
DATABASE_NAME: peoplenest_prod
DATABASE_USERNAME: peoplenest_user
DATABASE_PASSWORD: <secret>

# Redis
REDIS_HOST: redis-service
REDIS_PORT: 6379
REDIS_PASSWORD: <secret>

# JWT
JWT_SECRET: <secret>
JWT_REFRESH_SECRET: <secret>

# Encryption
ENCRYPTION_KEY: <secret>
```

#### Frontend Configuration

```yaml
# API URLs
VITE_API_URL: https://api.peoplenest.com
VITE_GRAPHQL_URL: https://api.peoplenest.com/graphql

# Application
VITE_APP_NAME: PeopleNest HRMS
VITE_APP_VERSION: 1.0.0
```

### Security Configuration

#### TLS/SSL Setup

```bash
# Install cert-manager
helm install cert-manager jetstack/cert-manager \
  --namespace cert-manager \
  --create-namespace \
  --set installCRDs=true

# Apply certificate issuer
kubectl apply -f k8s/cert-issuer.yaml
```

#### Network Policies

```bash
# Apply network policies
kubectl apply -f k8s/network-policies.yaml
```

## Monitoring and Observability

### Prometheus Metrics

Access Prometheus at: `http://prometheus.peoplenest.com`

Key metrics to monitor:
- Application response times
- Database connection pools
- Memory and CPU usage
- Error rates
- Request throughput

### Grafana Dashboards

Access Grafana at: `http://grafana.peoplenest.com`

Default credentials:
- Username: `admin`
- Password: `<configured in secrets>`

### Log Aggregation

```bash
# Deploy ELK stack
helm install elasticsearch elastic/elasticsearch
helm install kibana elastic/kibana
helm install filebeat elastic/filebeat
```

### Distributed Tracing

```bash
# Deploy Jaeger
kubectl apply -f k8s/jaeger.yaml
```

## Scaling and Performance

### Horizontal Pod Autoscaling

```bash
# Enable metrics server
kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml

# HPA is configured in deployment files
kubectl get hpa -n peoplenest
```

### Vertical Pod Autoscaling

```bash
# Install VPA
git clone https://github.com/kubernetes/autoscaler.git
cd autoscaler/vertical-pod-autoscaler
./hack/vpa-up.sh
```

### Database Scaling

#### PostgreSQL High Availability

```bash
# Deploy PostgreSQL cluster with Patroni
helm install postgresql-ha bitnami/postgresql-ha
```

#### MongoDB Replica Set

```bash
# Deploy MongoDB replica set
helm install mongodb bitnami/mongodb \
  --set architecture=replicaset \
  --set replicaCount=3
```

## Backup and Disaster Recovery

### Database Backups

```bash
# PostgreSQL backup
kubectl create job --from=cronjob/postgres-backup postgres-backup-manual

# MongoDB backup
kubectl create job --from=cronjob/mongodb-backup mongodb-backup-manual
```

### Application Data Backup

```bash
# Backup persistent volumes
velero backup create peoplenest-backup --include-namespaces peoplenest
```

### Disaster Recovery Plan

1. **RTO (Recovery Time Objective)**: 4 hours
2. **RPO (Recovery Point Objective)**: 1 hour
3. **Backup Frequency**: Every 6 hours
4. **Cross-region replication**: Enabled

## Security

### Security Scanning

```bash
# Scan container images
trivy image peoplenest/backend:latest
trivy image peoplenest/frontend:latest
trivy image peoplenest/ai-service:latest

# Scan Kubernetes manifests
trivy config k8s/
```

### RBAC Configuration

```bash
# Apply RBAC policies
kubectl apply -f k8s/rbac.yaml
```

### Pod Security Standards

```bash
# Apply pod security policies
kubectl apply -f k8s/pod-security-policies.yaml
```

## Troubleshooting

### Common Issues

#### Pod Startup Issues

```bash
# Check pod status
kubectl get pods -n peoplenest

# Check pod logs
kubectl logs -n peoplenest <pod-name>

# Describe pod for events
kubectl describe pod -n peoplenest <pod-name>
```

#### Database Connection Issues

```bash
# Test database connectivity
kubectl run -it --rm debug --image=postgres:15 --restart=Never -- psql -h postgres-service -U peoplenest_user -d peoplenest_prod
```

#### Performance Issues

```bash
# Check resource usage
kubectl top pods -n peoplenest
kubectl top nodes

# Check HPA status
kubectl get hpa -n peoplenest
```

### Health Checks

```bash
# Backend health
curl https://api.peoplenest.com/health

# Frontend health
curl https://app.peoplenest.com/health

# Database health
kubectl exec -n peoplenest postgres-0 -- pg_isready
```

## Maintenance

### Rolling Updates

```bash
# Update backend
kubectl set image deployment/backend backend=peoplenest/backend:v1.1.0 -n peoplenest

# Monitor rollout
kubectl rollout status deployment/backend -n peoplenest
```

### Rollback

```bash
# Rollback to previous version
kubectl rollout undo deployment/backend -n peoplenest

# Rollback to specific revision
kubectl rollout undo deployment/backend --to-revision=2 -n peoplenest
```

### Maintenance Windows

Recommended maintenance schedule:
- **Security updates**: Weekly
- **Application updates**: Bi-weekly
- **Infrastructure updates**: Monthly
- **Database maintenance**: Quarterly

## Support

### Monitoring Alerts

Configure alerts for:
- High error rates (>5%)
- High response times (>2s)
- Low disk space (<20%)
- High CPU usage (>80%)
- Memory leaks
- Database connection issues

### Log Analysis

```bash
# View application logs
kubectl logs -f deployment/backend -n peoplenest

# Search logs with grep
kubectl logs deployment/backend -n peoplenest | grep ERROR

# Export logs for analysis
kubectl logs deployment/backend -n peoplenest > backend.log
```

### Performance Monitoring

Key performance indicators:
- Response time: <500ms (95th percentile)
- Throughput: >1000 requests/second
- Error rate: <1%
- Uptime: >99.9%
