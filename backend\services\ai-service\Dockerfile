# Multi-stage build for PeopleNest AI Service
FROM python:3.11-slim AS base

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    libpq-dev \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Dependencies stage
FROM base AS deps
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Builder stage
FROM base AS builder
WORKDIR /app

# Copy requirements and install all dependencies (including dev)
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY . .

# Download and prepare ML models
RUN python -c "
import nltk
import spacy
from transformers import AutoTokenizer, AutoModel
from sentence_transformers import SentenceTransformer

# Download NLTK data
nltk.download('punkt')
nltk.download('stopwords')
nltk.download('wordnet')
nltk.download('averaged_perceptron_tagger')

# Download spaCy model
import subprocess
subprocess.run(['python', '-m', 'spacy', 'download', 'en_core_web_sm'])

# Download transformer models
AutoTokenizer.from_pretrained('bert-base-uncased')
AutoModel.from_pretrained('bert-base-uncased')

# Download sentence transformer
SentenceTransformer('all-MiniLM-L6-v2')
"

# Production stage
FROM base AS runner
WORKDIR /app

# Create a non-root user
RUN groupadd --gid 1001 python && \
    useradd --uid 1001 --gid python --shell /bin/bash --create-home python

# Copy installed packages from deps stage
COPY --from=deps /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=deps /usr/local/bin /usr/local/bin

# Copy application code and models
COPY --from=builder /app /app
COPY --from=builder /root/nltk_data /home/<USER>/nltk_data

# Set ownership
RUN chown -R python:python /app /home/<USER>

# Switch to non-root user
USER python

# Set NLTK data path
ENV NLTK_DATA=/home/<USER>/nltk_data

# Expose the port the app runs on
EXPOSE 8003

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:8003/health || exit 1

# Set environment to production
ENV ENVIRONMENT=production

# Start the application
CMD ["python", "main.py"]
