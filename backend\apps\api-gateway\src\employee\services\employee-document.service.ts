import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { EmployeeDocument } from '@app/database';

@Injectable()
export class EmployeeDocumentService {
  constructor(
    @InjectRepository(EmployeeDocument)
    private readonly documentRepository: Repository<EmployeeDocument>,
  ) {}

  // TODO: Implement document management methods
  async findByEmployeeId(employeeId: string, tenantId: string): Promise<EmployeeDocument[]> {
    return this.documentRepository.find({
      where: { employeeId, tenantId },
      order: { createdAt: 'DESC' },
    });
  }
}
