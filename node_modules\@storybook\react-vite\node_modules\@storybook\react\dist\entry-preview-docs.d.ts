import { LegacyStoryFn, DecoratorFunction } from 'storybook/internal/types';
import { R as ReactRenderer } from './types-5617c98e.js';
import 'react';

declare const applyDecorators: (storyFn: LegacyStoryFn<ReactRenderer>, decorators: DecoratorFunction<ReactRenderer>[]) => LegacyStoryFn<ReactRenderer>;

declare const decorators: DecoratorFunction<ReactRenderer>[];

declare const parameters: {
    docs: {
        story: {
            inline: boolean;
        };
    };
};

export { applyDecorators, decorators, parameters };
