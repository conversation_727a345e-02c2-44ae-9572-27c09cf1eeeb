import {
  <PERSON><PERSON><PERSON>,
  Column,
  Index,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BeforeInsert,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { User } from './user.entity';
import { Exclude } from 'class-transformer';

@Entity('mfa_backup_codes')
@Index(['userId', 'isUsed'])
@Index(['codeHash'], { unique: true })
export class MfaBackupCode extends TenantAwareEntity {
  @Column({
    type: 'uuid',
    comment: 'User ID this backup code belongs to',
  })
  userId: string;

  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    comment: 'Hashed backup code',
  })
  @Exclude()
  codeHash: string;

  @Column({
    type: 'varchar',
    length: 50,
    comment: 'Partial code for display (first 4 characters)',
  })
  partialCode: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this backup code has been used',
  })
  @Index()
  isUsed: boolean;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When this backup code was used',
  })
  usedAt?: Date;

  @Column({
    type: 'varchar',
    length: 45,
    nullable: true,
    comment: 'IP address when code was used',
  })
  usedFromIp?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'User agent when code was used',
  })
  usedFromUserAgent?: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'When this backup code expires',
  })
  expiresAt?: Date;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this backup code is active',
  })
  isActive: boolean;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Session ID when code was used',
  })
  sessionId?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Additional metadata about code usage',
  })
  metadata?: Record<string, any>;

  @Column({
    type: 'int',
    default: 1,
    comment: 'Generation number for code rotation',
  })
  generation: number;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this code was generated for emergency access',
  })
  isEmergency: boolean;

  // Relationships
  @ManyToOne(() => User, user => user.mfaBackupCodes, { eager: false })
  @JoinColumn({ name: 'user_id' })
  user: User;

  // Virtual properties
  get isExpired(): boolean {
    return this.expiresAt ? this.expiresAt < new Date() : false;
  }

  get isValid(): boolean {
    return this.isActive && !this.isUsed && !this.isExpired;
  }

  get displayCode(): string {
    return `${this.partialCode}****`;
  }

  get ageInDays(): number {
    const age = new Date().getTime() - this.createdAt.getTime();
    return Math.floor(age / (1000 * 60 * 60 * 24));
  }

  @BeforeInsert()
  setPartialCode() {
    // This would be set from the actual code before hashing
    // The partial code is set by the service layer
  }

  markAsUsed(sessionId?: string, ipAddress?: string, userAgent?: string): void {
    this.isUsed = true;
    this.usedAt = new Date();
    this.sessionId = sessionId;
    this.usedFromIp = ipAddress;
    this.usedFromUserAgent = userAgent;
  }

  deactivate(): void {
    this.isActive = false;
  }
}
