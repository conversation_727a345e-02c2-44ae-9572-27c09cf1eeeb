import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';

export interface MetricData {
  name: string;
  value: number;
  labels: Record<string, string>;
  timestamp: Date;
  type: 'counter' | 'gauge' | 'histogram' | 'summary';
}

export interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime: number;
  timestamp: Date;
  details?: Record<string, any>;
}

export interface AlertRule {
  id: string;
  name: string;
  condition: string;
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  cooldown: number; // minutes
  lastTriggered?: Date;
}

@Injectable()
export class MonitoringService {
  private readonly logger = new Logger(MonitoringService.name);
  private readonly metrics = new Map<string, MetricData[]>();
  private readonly healthChecks = new Map<string, HealthCheckResult>();
  private readonly alerts = new Map<string, AlertRule>();
  private readonly activeAlerts = new Set<string>();

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initializeDefaultAlerts();
  }

  /**
   * Record a metric
   */
  recordMetric(metric: Omit<MetricData, 'timestamp'>): void {
    const metricWithTimestamp: MetricData = {
      ...metric,
      timestamp: new Date(),
    };

    if (!this.metrics.has(metric.name)) {
      this.metrics.set(metric.name, []);
    }

    const metricHistory = this.metrics.get(metric.name)!;
    metricHistory.push(metricWithTimestamp);

    // Keep only last 1000 data points per metric
    if (metricHistory.length > 1000) {
      metricHistory.shift();
    }

    // Check alert rules
    this.checkAlertRules(metric.name, metric.value, metric.labels);

    this.logger.debug(`Recorded metric: ${metric.name} = ${metric.value}`);
  }

  /**
   * Get metrics by name
   */
  getMetrics(name: string, timeRange?: { start: Date; end: Date }): MetricData[] {
    const metrics = this.metrics.get(name) || [];
    
    if (!timeRange) {
      return metrics;
    }

    return metrics.filter(
      metric => metric.timestamp >= timeRange.start && metric.timestamp <= timeRange.end
    );
  }

  /**
   * Get all metric names
   */
  getMetricNames(): string[] {
    return Array.from(this.metrics.keys());
  }

  /**
   * Record health check result
   */
  recordHealthCheck(result: HealthCheckResult): void {
    this.healthChecks.set(result.service, result);
    
    // Record as metric
    this.recordMetric({
      name: 'service_health',
      value: result.status === 'healthy' ? 1 : 0,
      labels: { service: result.service },
      type: 'gauge',
    });

    this.recordMetric({
      name: 'service_response_time',
      value: result.responseTime,
      labels: { service: result.service },
      type: 'histogram',
    });

    this.logger.debug(`Health check recorded for ${result.service}: ${result.status}`);
  }

  /**
   * Get health check results
   */
  getHealthChecks(): Record<string, HealthCheckResult> {
    return Object.fromEntries(this.healthChecks);
  }

  /**
   * Get overall system health
   */
  getSystemHealth(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    services: Record<string, HealthCheckResult>;
    summary: {
      total: number;
      healthy: number;
      degraded: number;
      unhealthy: number;
    };
  } {
    const services = this.getHealthChecks();
    const serviceStatuses = Object.values(services);
    
    const summary = {
      total: serviceStatuses.length,
      healthy: serviceStatuses.filter(s => s.status === 'healthy').length,
      degraded: serviceStatuses.filter(s => s.status === 'degraded').length,
      unhealthy: serviceStatuses.filter(s => s.status === 'unhealthy').length,
    };

    let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    
    if (summary.unhealthy > 0) {
      overallStatus = 'unhealthy';
    } else if (summary.degraded > 0) {
      overallStatus = 'degraded';
    }

    return {
      status: overallStatus,
      services,
      summary,
    };
  }

  /**
   * Add alert rule
   */
  addAlertRule(rule: AlertRule): void {
    this.alerts.set(rule.id, rule);
    this.logger.log(`Added alert rule: ${rule.name}`);
  }

  /**
   * Remove alert rule
   */
  removeAlertRule(ruleId: string): void {
    this.alerts.delete(ruleId);
    this.activeAlerts.delete(ruleId);
    this.logger.log(`Removed alert rule: ${ruleId}`);
  }

  /**
   * Get all alert rules
   */
  getAlertRules(): AlertRule[] {
    return Array.from(this.alerts.values());
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): AlertRule[] {
    return Array.from(this.activeAlerts)
      .map(id => this.alerts.get(id))
      .filter(Boolean) as AlertRule[];
  }

  /**
   * Check alert rules for a metric
   */
  private checkAlertRules(metricName: string, value: number, labels: Record<string, string>): void {
    for (const rule of this.alerts.values()) {
      if (!rule.enabled) continue;

      // Check if rule applies to this metric
      if (!this.ruleApplies(rule, metricName, labels)) continue;

      // Check cooldown
      if (rule.lastTriggered) {
        const cooldownMs = rule.cooldown * 60 * 1000;
        if (Date.now() - rule.lastTriggered.getTime() < cooldownMs) {
          continue;
        }
      }

      // Evaluate condition
      if (this.evaluateCondition(rule.condition, value, rule.threshold)) {
        this.triggerAlert(rule, metricName, value, labels);
      }
    }
  }

  /**
   * Check if alert rule applies to metric
   */
  private ruleApplies(rule: AlertRule, metricName: string, labels: Record<string, string>): boolean {
    // Simple implementation - in production, this would be more sophisticated
    return rule.condition.includes(metricName);
  }

  /**
   * Evaluate alert condition
   */
  private evaluateCondition(condition: string, value: number, threshold: number): boolean {
    // Simple implementation - in production, this would support complex expressions
    if (condition.includes('>')) {
      return value > threshold;
    } else if (condition.includes('<')) {
      return value < threshold;
    } else if (condition.includes('>=')) {
      return value >= threshold;
    } else if (condition.includes('<=')) {
      return value <= threshold;
    } else if (condition.includes('==')) {
      return value === threshold;
    }
    return false;
  }

  /**
   * Trigger an alert
   */
  private triggerAlert(rule: AlertRule, metricName: string, value: number, labels: Record<string, string>): void {
    rule.lastTriggered = new Date();
    this.activeAlerts.add(rule.id);

    const alertData = {
      rule,
      metricName,
      value,
      labels,
      timestamp: new Date(),
    };

    this.eventEmitter.emit('alert.triggered', alertData);
    
    this.logger.warn(`Alert triggered: ${rule.name} - ${metricName} = ${value}`);
  }

  /**
   * Initialize default alert rules
   */
  private initializeDefaultAlerts(): void {
    const defaultRules: AlertRule[] = [
      {
        id: 'high-error-rate',
        name: 'High Error Rate',
        condition: 'error_rate > threshold',
        threshold: 0.05, // 5%
        severity: 'high',
        enabled: true,
        cooldown: 5,
      },
      {
        id: 'slow-response-time',
        name: 'Slow Response Time',
        condition: 'response_time > threshold',
        threshold: 5000, // 5 seconds
        severity: 'medium',
        enabled: true,
        cooldown: 10,
      },
      {
        id: 'service-down',
        name: 'Service Down',
        condition: 'service_health < threshold',
        threshold: 1,
        severity: 'critical',
        enabled: true,
        cooldown: 1,
      },
      {
        id: 'high-memory-usage',
        name: 'High Memory Usage',
        condition: 'memory_usage > threshold',
        threshold: 0.9, // 90%
        severity: 'high',
        enabled: true,
        cooldown: 15,
      },
      {
        id: 'high-cpu-usage',
        name: 'High CPU Usage',
        condition: 'cpu_usage > threshold',
        threshold: 0.8, // 80%
        severity: 'medium',
        enabled: true,
        cooldown: 10,
      },
    ];

    defaultRules.forEach(rule => this.addAlertRule(rule));
  }

  /**
   * Periodic cleanup of old metrics
   */
  @Cron(CronExpression.EVERY_HOUR)
  cleanupOldMetrics(): void {
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago

    for (const [name, metrics] of this.metrics.entries()) {
      const filteredMetrics = metrics.filter(metric => metric.timestamp > cutoffTime);
      this.metrics.set(name, filteredMetrics);
    }

    this.logger.debug('Cleaned up old metrics');
  }

  /**
   * Handle alert events
   */
  @OnEvent('alert.triggered')
  handleAlertTriggered(alertData: any): void {
    // In production, this would send notifications via email, Slack, etc.
    this.logger.warn(`ALERT: ${alertData.rule.name} - ${alertData.metricName} = ${alertData.value}`);
  }

  /**
   * Get Prometheus-formatted metrics
   */
  getPrometheusMetrics(): string {
    const lines: string[] = [];

    for (const [name, metrics] of this.metrics.entries()) {
      if (metrics.length === 0) continue;

      const latestMetric = metrics[metrics.length - 1];
      const labelString = Object.entries(latestMetric.labels)
        .map(([key, value]) => `${key}="${value}"`)
        .join(',');

      lines.push(`# TYPE ${name} ${latestMetric.type}`);
      lines.push(`${name}{${labelString}} ${latestMetric.value}`);
    }

    return lines.join('\n');
  }

  /**
   * Get monitoring dashboard data
   */
  getDashboardData(): {
    systemHealth: any;
    activeAlerts: AlertRule[];
    recentMetrics: Record<string, MetricData[]>;
    topServices: Array<{ name: string; responseTime: number; errorRate: number }>;
  } {
    const systemHealth = this.getSystemHealth();
    const activeAlerts = this.getActiveAlerts();
    
    // Get recent metrics (last hour)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentMetrics: Record<string, MetricData[]> = {};
    
    for (const name of this.getMetricNames()) {
      recentMetrics[name] = this.getMetrics(name, { start: oneHourAgo, end: new Date() });
    }

    // Calculate top services by performance
    const topServices = Object.values(this.getHealthChecks())
      .map(health => ({
        name: health.service,
        responseTime: health.responseTime,
        errorRate: 0, // Would be calculated from error metrics
      }))
      .sort((a, b) => a.responseTime - b.responseTime)
      .slice(0, 10);

    return {
      systemHealth,
      activeAlerts,
      recentMetrics,
      topServices,
    };
  }
}
