import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import { firstValueFrom } from 'rxjs';

export interface ServiceInstance {
  id: string;
  name: string;
  version: string;
  host: string;
  port: number;
  protocol: 'http' | 'https';
  status: 'healthy' | 'unhealthy' | 'unknown';
  lastHealthCheck: Date;
  metadata: Record<string, any>;
  endpoints: {
    health: string;
    metrics?: string;
    docs?: string;
  };
  tags: string[];
  weight: number;
}

export interface ServiceRegistry {
  [serviceName: string]: ServiceInstance[];
}

@Injectable()
export class ServiceDiscoveryService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ServiceDiscoveryService.name);
  private readonly registry: ServiceRegistry = {};
  private readonly healthCheckInterval: number;
  private readonly maxRetries: number;
  private readonly retryDelay: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.healthCheckInterval = this.configService.get<number>('SERVICE_HEALTH_CHECK_INTERVAL', 30000);
    this.maxRetries = this.configService.get<number>('SERVICE_MAX_RETRIES', 3);
    this.retryDelay = this.configService.get<number>('SERVICE_RETRY_DELAY', 5000);
  }

  async onModuleInit() {
    this.logger.log('Initializing Service Discovery...');
    await this.loadStaticServices();
    await this.performInitialHealthChecks();
    this.logger.log('Service Discovery initialized successfully');
  }

  async onModuleDestroy() {
    this.logger.log('Service Discovery shutting down...');
  }

  /**
   * Load static service configurations from environment
   */
  private async loadStaticServices(): Promise<void> {
    const services = [
      {
        name: 'auth-service',
        host: this.configService.get('AUTH_SERVICE_HOST', 'localhost'),
        port: this.configService.get<number>('AUTH_SERVICE_PORT', 3001),
        version: '1.0.0',
        tags: ['authentication', 'security'],
        weight: 1,
      },
      {
        name: 'employee-service',
        host: this.configService.get('EMPLOYEE_SERVICE_HOST', 'localhost'),
        port: this.configService.get<number>('EMPLOYEE_SERVICE_PORT', 3002),
        version: '1.0.0',
        tags: ['employee', 'hr'],
        weight: 1,
      },
      {
        name: 'payroll-service',
        host: this.configService.get('PAYROLL_SERVICE_HOST', 'localhost'),
        port: this.configService.get<number>('PAYROLL_SERVICE_PORT', 3003),
        version: '1.0.0',
        tags: ['payroll', 'finance'],
        weight: 1,
      },
      {
        name: 'performance-service',
        host: this.configService.get('PERFORMANCE_SERVICE_HOST', 'localhost'),
        port: this.configService.get<number>('PERFORMANCE_SERVICE_PORT', 3004),
        version: '1.0.0',
        tags: ['performance', 'reviews'],
        weight: 1,
      },
      {
        name: 'ai-service',
        host: this.configService.get('AI_SERVICE_HOST', 'localhost'),
        port: this.configService.get<number>('AI_SERVICE_PORT', 8000),
        version: '1.0.0',
        tags: ['ai', 'ml', 'analytics'],
        weight: 1,
        protocol: 'http' as const,
      },
      {
        name: 'notification-service',
        host: this.configService.get('NOTIFICATION_SERVICE_HOST', 'localhost'),
        port: this.configService.get<number>('NOTIFICATION_SERVICE_PORT', 3006),
        version: '1.0.0',
        tags: ['notification', 'messaging'],
        weight: 1,
      },
    ];

    for (const serviceConfig of services) {
      const instance: ServiceInstance = {
        id: `${serviceConfig.name}-${serviceConfig.host}-${serviceConfig.port}`,
        name: serviceConfig.name,
        version: serviceConfig.version,
        host: serviceConfig.host,
        port: serviceConfig.port,
        protocol: serviceConfig.protocol || 'http',
        status: 'unknown',
        lastHealthCheck: new Date(),
        metadata: {},
        endpoints: {
          health: `${serviceConfig.protocol || 'http'}://${serviceConfig.host}:${serviceConfig.port}/health`,
          metrics: `${serviceConfig.protocol || 'http'}://${serviceConfig.host}:${serviceConfig.port}/metrics`,
          docs: `${serviceConfig.protocol || 'http'}://${serviceConfig.host}:${serviceConfig.port}/docs`,
        },
        tags: serviceConfig.tags,
        weight: serviceConfig.weight,
      };

      this.registerService(instance);
    }
  }

  /**
   * Register a service instance
   */
  registerService(instance: ServiceInstance): void {
    if (!this.registry[instance.name]) {
      this.registry[instance.name] = [];
    }

    // Check if instance already exists
    const existingIndex = this.registry[instance.name].findIndex(
      (existing) => existing.id === instance.id,
    );

    if (existingIndex >= 0) {
      // Update existing instance
      this.registry[instance.name][existingIndex] = instance;
      this.logger.log(`Updated service instance: ${instance.name} (${instance.id})`);
    } else {
      // Add new instance
      this.registry[instance.name].push(instance);
      this.logger.log(`Registered service instance: ${instance.name} (${instance.id})`);
    }

    this.eventEmitter.emit('service.registered', { instance });
  }

  /**
   * Deregister a service instance
   */
  deregisterService(serviceName: string, instanceId: string): void {
    if (this.registry[serviceName]) {
      const index = this.registry[serviceName].findIndex(
        (instance) => instance.id === instanceId,
      );

      if (index >= 0) {
        const instance = this.registry[serviceName][index];
        this.registry[serviceName].splice(index, 1);
        this.logger.log(`Deregistered service instance: ${serviceName} (${instanceId})`);
        this.eventEmitter.emit('service.deregistered', { instance });
      }
    }
  }

  /**
   * Get healthy instances of a service
   */
  getHealthyInstances(serviceName: string): ServiceInstance[] {
    const instances = this.registry[serviceName] || [];
    return instances.filter((instance) => instance.status === 'healthy');
  }

  /**
   * Get a service instance using load balancing
   */
  getInstance(serviceName: string, strategy: 'round-robin' | 'weighted' | 'random' = 'round-robin'): ServiceInstance | null {
    const healthyInstances = this.getHealthyInstances(serviceName);
    
    if (healthyInstances.length === 0) {
      this.logger.warn(`No healthy instances available for service: ${serviceName}`);
      return null;
    }

    switch (strategy) {
      case 'round-robin':
        return this.roundRobinSelection(healthyInstances);
      case 'weighted':
        return this.weightedSelection(healthyInstances);
      case 'random':
        return this.randomSelection(healthyInstances);
      default:
        return healthyInstances[0];
    }
  }

  private roundRobinSelection(instances: ServiceInstance[]): ServiceInstance {
    // Simple round-robin implementation
    // In production, you'd want to maintain state for proper round-robin
    const index = Math.floor(Date.now() / 1000) % instances.length;
    return instances[index];
  }

  private weightedSelection(instances: ServiceInstance[]): ServiceInstance {
    const totalWeight = instances.reduce((sum, instance) => sum + instance.weight, 0);
    let random = Math.random() * totalWeight;

    for (const instance of instances) {
      random -= instance.weight;
      if (random <= 0) {
        return instance;
      }
    }

    return instances[0];
  }

  private randomSelection(instances: ServiceInstance[]): ServiceInstance {
    const index = Math.floor(Math.random() * instances.length);
    return instances[index];
  }

  /**
   * Get all registered services
   */
  getAllServices(): ServiceRegistry {
    return { ...this.registry };
  }

  /**
   * Get service by name
   */
  getService(serviceName: string): ServiceInstance[] {
    return this.registry[serviceName] || [];
  }

  /**
   * Perform health check on all services
   */
  @Cron(CronExpression.EVERY_30_SECONDS)
  async performHealthChecks(): Promise<void> {
    const promises: Promise<void>[] = [];

    for (const [serviceName, instances] of Object.entries(this.registry)) {
      for (const instance of instances) {
        promises.push(this.checkInstanceHealth(instance));
      }
    }

    await Promise.allSettled(promises);
  }

  /**
   * Perform initial health checks
   */
  private async performInitialHealthChecks(): Promise<void> {
    this.logger.log('Performing initial health checks...');
    await this.performHealthChecks();
  }

  /**
   * Check health of a specific service instance
   */
  private async checkInstanceHealth(instance: ServiceInstance): Promise<void> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(instance.endpoints.health, {
          timeout: 5000,
          headers: {
            'User-Agent': 'PeopleNest-Gateway/1.0.0',
          },
        }),
      );

      const wasHealthy = instance.status === 'healthy';
      instance.status = response.status === 200 ? 'healthy' : 'unhealthy';
      instance.lastHealthCheck = new Date();

      // Update metadata from health response
      if (response.data && typeof response.data === 'object') {
        instance.metadata = {
          ...instance.metadata,
          ...response.data,
        };
      }

      // Emit event if status changed
      if (!wasHealthy && instance.status === 'healthy') {
        this.logger.log(`Service instance recovered: ${instance.name} (${instance.id})`);
        this.eventEmitter.emit('service.recovered', { instance });
      }

    } catch (error) {
      const wasHealthy = instance.status === 'healthy';
      instance.status = 'unhealthy';
      instance.lastHealthCheck = new Date();

      if (wasHealthy) {
        this.logger.warn(`Service instance unhealthy: ${instance.name} (${instance.id}) - ${error.message}`);
        this.eventEmitter.emit('service.unhealthy', { instance, error: error.message });
      }
    }
  }

  /**
   * Get service discovery metrics
   */
  getMetrics(): {
    totalServices: number;
    totalInstances: number;
    healthyInstances: number;
    unhealthyInstances: number;
    serviceBreakdown: Record<string, { total: number; healthy: number; unhealthy: number }>;
  } {
    let totalInstances = 0;
    let healthyInstances = 0;
    let unhealthyInstances = 0;
    const serviceBreakdown: Record<string, { total: number; healthy: number; unhealthy: number }> = {};

    for (const [serviceName, instances] of Object.entries(this.registry)) {
      const healthy = instances.filter(i => i.status === 'healthy').length;
      const unhealthy = instances.filter(i => i.status === 'unhealthy').length;
      
      serviceBreakdown[serviceName] = {
        total: instances.length,
        healthy,
        unhealthy,
      };

      totalInstances += instances.length;
      healthyInstances += healthy;
      unhealthyInstances += unhealthy;
    }

    return {
      totalServices: Object.keys(this.registry).length,
      totalInstances,
      healthyInstances,
      unhealthyInstances,
      serviceBreakdown,
    };
  }

  /**
   * Force health check for a specific service
   */
  async forceHealthCheck(serviceName: string): Promise<void> {
    const instances = this.registry[serviceName] || [];
    const promises = instances.map(instance => this.checkInstanceHealth(instance));
    await Promise.allSettled(promises);
  }
}
