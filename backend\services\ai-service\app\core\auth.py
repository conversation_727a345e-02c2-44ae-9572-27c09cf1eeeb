"""
Authentication and authorization for AI service.
"""

import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import jwt
from fastapi import HTT<PERSON>Exception, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import httpx

from app.core.config import settings
from app.core.exceptions import AuthenticationError, AuthorizationError

logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer()


class AuthService:
    """Authentication service for AI microservice."""
    
    def __init__(self):
        self.jwt_secret = settings.JWT_SECRET_KEY
        self.jwt_algorithm = settings.JWT_ALGORITHM
        self.auth_service_url = settings.AUTH_SERVICE_URL
        
    async def verify_token(self, token: str) -> Dict:
        """Verify JWT token and return user information."""
        try:
            # Decode JWT token
            payload = jwt.decode(
                token, 
                self.jwt_secret, 
                algorithms=[self.jwt_algorithm]
            )
            
            # Check token expiration
            exp = payload.get('exp')
            if exp and datetime.utcnow().timestamp() > exp:
                raise AuthenticationError("Token has expired")
            
            # Extract user information
            user_info = {
                'user_id': payload.get('sub'),
                'email': payload.get('email'),
                'roles': payload.get('roles', []),
                'permissions': payload.get('permissions', []),
                'organization_id': payload.get('organization_id'),
                'department': payload.get('department'),
            }
            
            return user_info
            
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token has expired")
        except jwt.InvalidTokenError:
            raise AuthenticationError("Invalid token")
        except Exception as e:
            logger.error(f"Token verification failed: {e}")
            raise AuthenticationError("Token verification failed")
    
    async def validate_permissions(self, user: Dict, required_permissions: List[str]) -> bool:
        """Validate user permissions."""
        try:
            user_permissions = user.get('permissions', [])
            user_roles = user.get('roles', [])
            
            # Check if user has required permissions
            for permission in required_permissions:
                if permission in user_permissions:
                    continue
                
                # Check role-based permissions
                if self._check_role_permission(user_roles, permission):
                    continue
                
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Permission validation failed: {e}")
            return False
    
    def _check_role_permission(self, roles: List[str], permission: str) -> bool:
        """Check if any user role has the required permission."""
        # Define role-permission mappings
        role_permissions = {
            'admin': ['*'],  # Admin has all permissions
            'hr_manager': [
                'ai:resume:parse', 'ai:sentiment:analyze', 'ai:predict:attrition',
                'ai:predict:performance', 'ai:query:nlp', 'ai:anomaly:detect'
            ],
            'hr_specialist': [
                'ai:resume:parse', 'ai:sentiment:analyze', 'ai:query:nlp'
            ],
            'manager': [
                'ai:sentiment:analyze', 'ai:predict:performance', 'ai:query:nlp'
            ],
            'finance_manager': [
                'ai:anomaly:detect', 'ai:predict:trends', 'ai:query:nlp'
            ],
            'employee': [
                'ai:query:nlp'  # Limited access
            ]
        }
        
        for role in roles:
            role_perms = role_permissions.get(role, [])
            if '*' in role_perms or permission in role_perms:
                return True
        
        return False
    
    async def get_user_context(self, user: Dict) -> Dict:
        """Get additional user context for AI operations."""
        try:
            # This could fetch additional context from the main auth service
            context = {
                'user_id': user.get('user_id'),
                'organization_id': user.get('organization_id'),
                'department': user.get('department'),
                'roles': user.get('roles', []),
                'data_access_level': self._determine_data_access_level(user),
                'allowed_operations': self._get_allowed_operations(user),
            }
            
            return context
            
        except Exception as e:
            logger.error(f"Failed to get user context: {e}")
            return {}
    
    def _determine_data_access_level(self, user: Dict) -> str:
        """Determine user's data access level."""
        roles = user.get('roles', [])
        
        if 'admin' in roles:
            return 'full'
        elif any(role in roles for role in ['hr_manager', 'finance_manager']):
            return 'department'
        elif 'manager' in roles:
            return 'team'
        else:
            return 'self'
    
    def _get_allowed_operations(self, user: Dict) -> List[str]:
        """Get list of allowed AI operations for user."""
        roles = user.get('roles', [])
        operations = []
        
        if 'admin' in roles:
            operations = ['all']
        elif 'hr_manager' in roles:
            operations = [
                'resume_parsing', 'sentiment_analysis', 'attrition_prediction',
                'performance_forecasting', 'nlp_query', 'anomaly_detection'
            ]
        elif 'hr_specialist' in roles:
            operations = ['resume_parsing', 'sentiment_analysis', 'nlp_query']
        elif 'manager' in roles:
            operations = ['sentiment_analysis', 'performance_forecasting', 'nlp_query']
        elif 'finance_manager' in roles:
            operations = ['anomaly_detection', 'trend_analysis', 'nlp_query']
        else:
            operations = ['nlp_query']
        
        return operations


# Initialize auth service
auth_service = AuthService()


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict:
    """Get current authenticated user."""
    try:
        token = credentials.credentials
        user = await auth_service.verify_token(token)
        return user
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error(f"Authentication failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        )


def require_permissions(permissions: List[str]):
    """Decorator to require specific permissions."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Get current user from kwargs
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            # Check permissions
            has_permission = await auth_service.validate_permissions(current_user, permissions)
            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient permissions"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


async def get_user_context(current_user: Dict = Depends(get_current_user)) -> Dict:
    """Get user context for AI operations."""
    return await auth_service.get_user_context(current_user)


class PermissionChecker:
    """Permission checker for different AI operations."""
    
    @staticmethod
    def can_parse_resume(user: Dict) -> bool:
        """Check if user can parse resumes."""
        return auth_service._check_role_permission(
            user.get('roles', []), 
            'ai:resume:parse'
        )
    
    @staticmethod
    def can_analyze_sentiment(user: Dict) -> bool:
        """Check if user can analyze sentiment."""
        return auth_service._check_role_permission(
            user.get('roles', []), 
            'ai:sentiment:analyze'
        )
    
    @staticmethod
    def can_predict_attrition(user: Dict) -> bool:
        """Check if user can predict attrition."""
        return auth_service._check_role_permission(
            user.get('roles', []), 
            'ai:predict:attrition'
        )
    
    @staticmethod
    def can_forecast_performance(user: Dict) -> bool:
        """Check if user can forecast performance."""
        return auth_service._check_role_permission(
            user.get('roles', []), 
            'ai:predict:performance'
        )
    
    @staticmethod
    def can_query_nlp(user: Dict) -> bool:
        """Check if user can use NLP queries."""
        return auth_service._check_role_permission(
            user.get('roles', []), 
            'ai:query:nlp'
        )
    
    @staticmethod
    def can_detect_anomalies(user: Dict) -> bool:
        """Check if user can detect anomalies."""
        return auth_service._check_role_permission(
            user.get('roles', []), 
            'ai:anomaly:detect'
        )


class DataAccessFilter:
    """Filter data based on user access level."""
    
    @staticmethod
    def filter_employee_data(data: List[Dict], user: Dict) -> List[Dict]:
        """Filter employee data based on user access level."""
        access_level = auth_service._determine_data_access_level(user)
        
        if access_level == 'full':
            return data
        elif access_level == 'department':
            user_dept = user.get('department')
            return [item for item in data if item.get('department') == user_dept]
        elif access_level == 'team':
            user_id = user.get('user_id')
            return [item for item in data if item.get('manager_id') == user_id]
        else:  # self
            user_id = user.get('user_id')
            return [item for item in data if item.get('employee_id') == user_id]
    
    @staticmethod
    def can_access_employee(employee_id: str, user: Dict) -> bool:
        """Check if user can access specific employee data."""
        access_level = auth_service._determine_data_access_level(user)
        
        if access_level == 'full':
            return True
        elif access_level == 'self':
            return employee_id == user.get('user_id')
        else:
            # For department/team level, would need to query database
            # For now, return True (implement proper check in production)
            return True


# Rate limiting (basic implementation)
class RateLimiter:
    """Basic rate limiter for AI service endpoints."""
    
    def __init__(self):
        self.requests = {}
    
    async def check_rate_limit(self, user_id: str, endpoint: str, limit: int = 100) -> bool:
        """Check if user has exceeded rate limit."""
        key = f"{user_id}:{endpoint}"
        current_time = datetime.utcnow()
        
        if key not in self.requests:
            self.requests[key] = []
        
        # Remove old requests (older than 1 hour)
        self.requests[key] = [
            req_time for req_time in self.requests[key]
            if current_time - req_time < timedelta(hours=1)
        ]
        
        # Check if limit exceeded
        if len(self.requests[key]) >= limit:
            return False
        
        # Add current request
        self.requests[key].append(current_time)
        return True


# Initialize rate limiter
rate_limiter = RateLimiter()


async def check_rate_limit(
    endpoint: str,
    limit: int = 100,
    current_user: Dict = Depends(get_current_user)
):
    """Check rate limit for endpoint."""
    user_id = current_user.get('user_id')
    if not await rate_limiter.check_rate_limit(user_id, endpoint, limit):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded"
        )
