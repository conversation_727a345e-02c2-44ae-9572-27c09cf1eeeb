import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  Join<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';
import { Application } from './application.entity';
import { Candidate } from './candidate.entity';
import { Employee } from './employee.entity';

export enum InterviewType {
  PHONE_SCREENING = 'phone_screening',
  VIDEO_INTERVIEW = 'video_interview',
  IN_PERSON = 'in_person',
  TECHNICAL = 'technical',
  BEHAVIORAL = 'behavioral',
  PANEL = 'panel',
  FINAL = 'final',
  CULTURAL_FIT = 'cultural_fit',
}

export enum InterviewStatus {
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  RESCHEDULED = 'rescheduled',
  NO_SHOW = 'no_show',
}

@Entity('interviews')
@Index(['tenantId', 'candidateId'])
@Index(['tenantId', 'applicationId'])
@Index(['tenantId', 'scheduledDate'])
@Index(['tenantId', 'status'])
export class Interview {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ name: 'application_id' })
  applicationId: string;

  @ManyToOne(() => Application, application => application.interviews, { eager: true })
  @JoinColumn({ name: 'application_id' })
  application: Application;

  @Column({ name: 'candidate_id' })
  candidateId: string;

  @ManyToOne(() => Candidate, candidate => candidate.interviews, { eager: true })
  @JoinColumn({ name: 'candidate_id' })
  candidate: Candidate;

  @Column({
    type: 'enum',
    enum: InterviewType,
    name: 'interview_type',
  })
  interviewType: InterviewType;

  @Column({
    type: 'enum',
    enum: InterviewStatus,
    default: InterviewStatus.SCHEDULED,
  })
  status: InterviewStatus;

  @Column({ name: 'scheduled_date', type: 'timestamp' })
  scheduledDate: Date;

  @Column({ type: 'integer', name: 'duration_minutes' })
  durationMinutes: number;

  @Column({ length: 255, nullable: true })
  location: string;

  @Column({ length: 500, nullable: true, name: 'meeting_link' })
  meetingLink: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ name: 'interviewer_id' })
  interviewerId: string;

  @ManyToOne(() => Employee, { eager: true })
  @JoinColumn({ name: 'interviewer_id' })
  interviewer: Employee;

  @Column({ type: 'json', nullable: true, name: 'additional_interviewers' })
  additionalInterviewers: Array<{
    employeeId: string;
    name: string;
    role: string;
  }>;

  @Column({ name: 'started_at', type: 'timestamp', nullable: true })
  startedAt: Date;

  @Column({ name: 'ended_at', type: 'timestamp', nullable: true })
  endedAt: Date;

  @Column({ type: 'json', nullable: true, name: 'evaluation_criteria' })
  evaluationCriteria: Array<{
    criteriaId: string;
    criteriaName: string;
    weight: number;
    rating?: number;
    comments?: string;
  }>;

  @Column({ type: 'json', nullable: true, name: 'questions_asked' })
  questionsAsked: Array<{
    questionId: string;
    question: string;
    answer?: string;
    rating?: number;
    notes?: string;
  }>;

  @Column({ type: 'decimal', precision: 3, scale: 2, nullable: true, name: 'overall_rating' })
  overallRating: number;

  @Column({ type: 'text', nullable: true, name: 'strengths_observed' })
  strengthsObserved: string;

  @Column({ type: 'text', nullable: true, name: 'concerns_noted' })
  concernsNoted: string;

  @Column({ type: 'text', nullable: true, name: 'recommendation' })
  recommendation: string;

  @Column({ type: 'json', nullable: true, name: 'technical_assessment' })
  technicalAssessment: {
    problemsSolved?: number;
    totalProblems?: number;
    codingSkillRating?: number;
    problemSolvingRating?: number;
    communicationRating?: number;
    notes?: string;
  };

  @Column({ type: 'json', nullable: true, name: 'behavioral_assessment' })
  behavioralAssessment: {
    leadershipPotential?: number;
    teamwork?: number;
    adaptability?: number;
    communication?: number;
    motivation?: number;
    culturalFit?: number;
    examples?: Array<{
      competency: string;
      example: string;
      rating: number;
    }>;
  };

  @Column({ type: 'boolean', default: false, name: 'recommend_for_next_round' })
  recommendForNextRound: boolean;

  @Column({ type: 'text', nullable: true, name: 'next_steps' })
  nextSteps: string;

  @Column({ type: 'json', nullable: true, name: 'follow_up_actions' })
  followUpActions: Array<{
    action: string;
    assignedTo: string;
    dueDate: string;
    completed: boolean;
  }>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  // Computed properties
  get actualDurationMinutes(): number {
    if (!this.startedAt || !this.endedAt) return 0;
    return Math.floor((this.endedAt.getTime() - this.startedAt.getTime()) / (1000 * 60));
  }

  get isCompleted(): boolean {
    return this.status === InterviewStatus.COMPLETED;
  }

  get averageCriteriaRating(): number {
    if (!this.evaluationCriteria || this.evaluationCriteria.length === 0) return 0;
    const ratedCriteria = this.evaluationCriteria.filter(c => c.rating !== undefined);
    if (ratedCriteria.length === 0) return 0;
    
    const totalWeightedScore = ratedCriteria.reduce((sum, criteria) => {
      return sum + (criteria.rating! * criteria.weight);
    }, 0);
    
    const totalWeight = ratedCriteria.reduce((sum, criteria) => sum + criteria.weight, 0);
    return totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
  }
}
