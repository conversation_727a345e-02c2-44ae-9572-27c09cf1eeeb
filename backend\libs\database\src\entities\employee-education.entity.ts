import { Entity, Column, Index, ManyToOne, JoinColumn } from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { Employee } from './employee.entity';

@Entity('employee_education')
@Index(['employeeId'])
export class EmployeeEducation extends TenantAwareEntity {
  @Column({ type: 'uuid', comment: 'Employee ID' })
  employeeId: string;

  @Column({ type: 'varchar', length: 255, comment: 'Institution name' })
  institution: string;

  @Column({ type: 'varchar', length: 255, comment: 'Degree/Qualification' })
  degree: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: 'Field of study' })
  fieldOfStudy?: string;

  @Column({ type: 'date', nullable: true, comment: 'Start date' })
  startDate?: Date;

  @Column({ type: 'date', nullable: true, comment: 'End date' })
  endDate?: Date;

  @Column({ type: 'varchar', length: 10, nullable: true, comment: 'Grade/GPA' })
  grade?: string;

  @ManyToOne(() => Employee, employee => employee.education, { eager: false })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;
}
