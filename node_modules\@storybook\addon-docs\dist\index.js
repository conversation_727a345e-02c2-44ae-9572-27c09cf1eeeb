"use strict";var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __esm=(fn,res)=>function(){return fn&&(res=(0,fn[__getOwnPropNames(fn)[0]])(fn=0)),res};var __export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})},__copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to},__reExport=(target,mod,secondTarget)=>(__copyProps(target,mod,"default"),secondTarget&&__copyProps(secondTarget,mod,"default")),__toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod)),__toCommonJS=mod=>__copyProps(__defProp({},"__esModule",{value:!0}),mod);var DocsRenderer_exports={};__export(DocsRenderer_exports,{DocsRenderer:()=>DocsRenderer,defaultComponents:()=>defaultComponents});var import_react,import_blocks,import_react_dom_shim,defaultComponents,ErrorBoundary,DocsRenderer,init_DocsRenderer=__esm({"src/DocsRenderer.tsx"(){"use strict";import_react=__toESM(require("react")),import_blocks=require("@storybook/blocks"),import_react_dom_shim=require("@storybook/react-dom-shim"),defaultComponents={code:import_blocks.CodeOrSourceMdx,a:import_blocks.AnchorMdx,...import_blocks.HeadersMdx},ErrorBoundary=class extends import_react.Component{constructor(){super(...arguments);this.state={hasError:!1}}static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(err){let{showException}=this.props;showException(err)}render(){let{hasError}=this.state,{children}=this.props;return hasError?null:import_react.default.createElement(import_react.default.Fragment,null,children)}},DocsRenderer=class{constructor(){this.render=async(context,docsParameter,element)=>{let components={...defaultComponents,...docsParameter?.components},TDocs=import_blocks.Docs;return new Promise((resolve,reject)=>{import("@mdx-js/react").then(({MDXProvider})=>(0,import_react_dom_shim.renderElement)(import_react.default.createElement(ErrorBoundary,{showException:reject,key:Math.random()},import_react.default.createElement(MDXProvider,{components},import_react.default.createElement(TDocs,{context,docsParameter}))),element)).then(()=>resolve())})},this.unmount=element=>{(0,import_react_dom_shim.unmountElement)(element)}}}}});var index_exports={};__export(index_exports,{DocsRenderer:()=>DocsRenderer,default:()=>index_default});module.exports=__toCommonJS(index_exports);var import_preview_api=require("storybook/internal/preview-api");var preview_exports={};__export(preview_exports,{parameters:()=>parameters});var excludeTags=Object.entries(globalThis.TAGS_OPTIONS??{}).reduce((acc,entry)=>{let[tag,option]=entry;return option.excludeFromDocsStories&&(acc[tag]=!0),acc},{}),parameters={docs:{renderer:async()=>{let{DocsRenderer:DocsRenderer2}=await Promise.resolve().then(()=>(init_DocsRenderer(),DocsRenderer_exports));return new DocsRenderer2},stories:{filter:story=>(story.tags||[]).filter(tag=>excludeTags[tag]).length===0&&!story.parameters.docs?.disable}}};__reExport(index_exports,require("@storybook/blocks"),module.exports);init_DocsRenderer();var index_default=()=>(0,import_preview_api.definePreview)(preview_exports);0&&(module.exports={DocsRenderer,...require("@storybook/blocks")});
