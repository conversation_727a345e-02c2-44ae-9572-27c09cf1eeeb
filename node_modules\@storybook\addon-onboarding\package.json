{"name": "@storybook/addon-onboarding", "version": "9.0.13", "description": "Storybook Addon Onboarding - Introduces a new onboarding experience", "keywords": ["storybook-addons", "addon-onboarding"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/onboarding", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/onboarding"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./manager": "./dist/manager.js", "./preset": "./dist/preset.js", "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/addon-bundle.ts"}, "devDependencies": {"@neoconfetti/react": "^1.0.0", "@storybook/icons": "^1.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-joyride": "^2.8.2", "typescript": "^5.8.3"}, "peerDependencies": {"storybook": "^9.0.13"}, "publishConfig": {"access": "public"}, "bundler": {"exportEntries": ["./src/index.ts"], "managerEntries": ["./src/manager.tsx"], "nodeEntries": ["./src/preset.ts"]}}