import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';

export enum HolidayType {
  PUBLIC = 'public',
  COMPANY = 'company',
  RELIGIOUS = 'religious',
  CULTURAL = 'cultural',
  OPTIONAL = 'optional',
}

@Entity('holidays')
@Index(['tenantId', 'date'])
@Index(['tenantId', 'isActive'])
export class Holiday {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'date' })
  date: Date;

  @Column({
    type: 'enum',
    enum: HolidayType,
    default: HolidayType.COMPANY,
    name: 'holiday_type',
  })
  holidayType: HolidayType;

  @Column({ type: 'boolean', default: true, name: 'is_active' })
  isActive: boolean;

  @Column({ type: 'boolean', default: false, name: 'is_recurring' })
  isRecurring: boolean;

  @Column({ type: 'json', nullable: true, name: 'applicable_locations' })
  applicableLocations: string[];

  @Column({ type: 'json', nullable: true, name: 'applicable_departments' })
  applicableDepartments: string[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;
}
