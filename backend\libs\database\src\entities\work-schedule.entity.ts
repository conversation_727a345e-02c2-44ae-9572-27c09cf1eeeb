import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { Employee } from './employee.entity';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';

export enum ScheduleType {
  STANDARD = 'standard',
  FLEXIBLE = 'flexible',
  SHIFT = 'shift',
  REMOTE = 'remote',
  HYBRID = 'hybrid',
  COMPRESSED = 'compressed',
}

@Entity('work_schedules')
@Index(['tenantId', 'employeeId'])
@Index(['tenantId', 'isActive'])
export class WorkSchedule {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ name: 'employee_id' })
  employeeId: string;

  @ManyToOne(() => Employee, { eager: true })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;

  @Column({ length: 255 })
  name: string;

  @Column({
    type: 'enum',
    enum: ScheduleType,
    default: ScheduleType.STANDARD,
    name: 'schedule_type',
  })
  scheduleType: ScheduleType;

  @Column({ type: 'boolean', default: true, name: 'is_active' })
  isActive: boolean;

  @Column({ name: 'effective_from', type: 'date' })
  effectiveFrom: Date;

  @Column({ name: 'effective_to', type: 'date', nullable: true })
  effectiveTo: Date;

  @Column({ type: 'json', name: 'weekly_schedule' })
  weeklySchedule: {
    monday?: { startTime: string; endTime: string; isWorkingDay: boolean };
    tuesday?: { startTime: string; endTime: string; isWorkingDay: boolean };
    wednesday?: { startTime: string; endTime: string; isWorkingDay: boolean };
    thursday?: { startTime: string; endTime: string; isWorkingDay: boolean };
    friday?: { startTime: string; endTime: string; isWorkingDay: boolean };
    saturday?: { startTime: string; endTime: string; isWorkingDay: boolean };
    sunday?: { startTime: string; endTime: string; isWorkingDay: boolean };
  };

  @Column({ type: 'decimal', precision: 5, scale: 2, name: 'hours_per_week' })
  hoursPerWeek: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, name: 'hours_per_day' })
  hoursPerDay: number;

  @Column({ type: 'json', nullable: true, name: 'break_schedule' })
  breakSchedule: Array<{
    name: string;
    startTime: string;
    duration: number; // in minutes
    isPaid: boolean;
  }>;

  @Column({ type: 'json', nullable: true, name: 'overtime_rules' })
  overtimeRules: {
    dailyThreshold?: number;
    weeklyThreshold?: number;
    multiplier?: number;
    requiresApproval?: boolean;
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;
}
