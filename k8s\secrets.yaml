apiVersion: v1
kind: Secret
metadata:
  name: peoplenest-secrets
  namespace: peoplenest
  labels:
    app.kubernetes.io/name: peoplenest
    app.kubernetes.io/component: secrets
type: Opaque
stringData:
  # Database Credentials
  DATABASE_USERNAME: "peoplenest_user"
  DATABASE_PASSWORD: "CHANGE_ME_IN_PRODUCTION"
  
  # MongoDB Credentials
  MONGODB_USERNAME: "peoplenest_user"
  MONGODB_PASSWORD: "CHANGE_ME_IN_PRODUCTION"
  MONGODB_URI: "********************************************************************************************************"
  
  # Redis Password
  REDIS_PASSWORD: "CHANGE_ME_IN_PRODUCTION"
  
  # JWT Secrets
  JWT_SECRET: "CHANGE_ME_IN_PRODUCTION_VERY_LONG_SECRET_KEY"
  JWT_REFRESH_SECRET: "CHAN<PERSON>_ME_IN_PRODUCTION_REFRESH_SECRET_KEY"
  
  # Encryption Keys
  ENCRYPTION_KEY: "CHANGE_ME_IN_PRODUCTION_32_CHAR_KEY"
  ENCRYPTION_IV: "CHANGE_ME_IN_PRODUCTION_16_CHAR_IV"
  
  # Email Configuration
  EMAIL_HOST: "smtp.gmail.com"
  EMAIL_PORT: "587"
  EMAIL_USERNAME: "<EMAIL>"
  EMAIL_PASSWORD: "CHANGE_ME_IN_PRODUCTION"
  
  # External API Keys
  OPENAI_API_KEY: "CHANGE_ME_IN_PRODUCTION"
  HUGGINGFACE_API_KEY: "CHANGE_ME_IN_PRODUCTION"
  
  # OAuth Credentials
  GOOGLE_CLIENT_ID: "CHANGE_ME_IN_PRODUCTION"
  GOOGLE_CLIENT_SECRET: "CHANGE_ME_IN_PRODUCTION"
  MICROSOFT_CLIENT_ID: "CHANGE_ME_IN_PRODUCTION"
  MICROSOFT_CLIENT_SECRET: "CHANGE_ME_IN_PRODUCTION"
  
  # Twilio Credentials
  TWILIO_ACCOUNT_SID: "CHANGE_ME_IN_PRODUCTION"
  TWILIO_AUTH_TOKEN: "CHANGE_ME_IN_PRODUCTION"
  TWILIO_PHONE_NUMBER: "+**********"
  
  # Monitoring & Analytics
  SENTRY_DSN: "CHANGE_ME_IN_PRODUCTION"
  ANALYTICS_API_KEY: "CHANGE_ME_IN_PRODUCTION"
  
  # File Storage
  AWS_ACCESS_KEY_ID: "CHANGE_ME_IN_PRODUCTION"
  AWS_SECRET_ACCESS_KEY: "CHANGE_ME_IN_PRODUCTION"
  AWS_REGION: "us-east-1"
  AWS_S3_BUCKET: "peoplenest-files"
  
  # Kafka Security
  KAFKA_USERNAME: "peoplenest_user"
  KAFKA_PASSWORD: "CHANGE_ME_IN_PRODUCTION"
  
---
apiVersion: v1
kind: Secret
metadata:
  name: database-credentials
  namespace: peoplenest
  labels:
    app.kubernetes.io/name: peoplenest
    app.kubernetes.io/component: database
type: Opaque
stringData:
  POSTGRES_DB: "peoplenest_prod"
  POSTGRES_USER: "peoplenest_user"
  POSTGRES_PASSWORD: "CHANGE_ME_IN_PRODUCTION"
  
---
apiVersion: v1
kind: Secret
metadata:
  name: mongodb-credentials
  namespace: peoplenest
  labels:
    app.kubernetes.io/name: peoplenest
    app.kubernetes.io/component: mongodb
type: Opaque
stringData:
  MONGO_INITDB_ROOT_USERNAME: "admin"
  MONGO_INITDB_ROOT_PASSWORD: "CHANGE_ME_IN_PRODUCTION"
  MONGO_INITDB_DATABASE: "peoplenest_docs"
  
---
apiVersion: v1
kind: Secret
metadata:
  name: redis-credentials
  namespace: peoplenest
  labels:
    app.kubernetes.io/name: peoplenest
    app.kubernetes.io/component: redis
type: Opaque
stringData:
  REDIS_PASSWORD: "CHANGE_ME_IN_PRODUCTION"
