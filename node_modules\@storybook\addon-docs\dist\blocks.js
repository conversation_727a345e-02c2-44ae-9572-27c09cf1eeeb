"use strict";var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __hasOwnProp=Object.prototype.hasOwnProperty;var __copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to},__reExport=(target,mod,secondTarget)=>(__copyProps(target,mod,"default"),secondTarget&&__copyProps(secondTarget,mod,"default"));var __toCommonJS=mod=>__copyProps(__defProp({},"__esModule",{value:!0}),mod);var blocks_exports={};module.exports=__toCommonJS(blocks_exports);var import_client_logger=require("storybook/internal/client-logger");__reExport(blocks_exports,require("@storybook/blocks"),module.exports);(0,import_client_logger.deprecate)("Import from '@storybook/addon-docs/blocks' is deprecated. Please import from '@storybook/blocks' instead.");0&&(module.exports={...require("@storybook/blocks")});
