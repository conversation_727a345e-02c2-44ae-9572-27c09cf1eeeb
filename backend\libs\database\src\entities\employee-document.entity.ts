import {
  Entity,
  Column,
  Index,
  ManyToOne,
  Join<PERSON><PERSON>umn,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { Employee } from './employee.entity';
import { DocumentType } from '@app/common/enums/status.enum';

@Entity('employee_documents')
@Index(['employeeId', 'type'])
export class EmployeeDocument extends TenantAwareEntity {
  @Column({
    type: 'uuid',
    comment: 'Employee ID',
  })
  employeeId: string;

  @Column({
    type: 'enum',
    enum: DocumentType,
    comment: 'Type of document',
  })
  type: DocumentType;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Document name',
  })
  name: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Document description',
  })
  description?: string;

  @Column({
    type: 'varchar',
    length: 500,
    comment: 'Document file URL',
  })
  fileUrl: string;

  @Column({
    type: 'varchar',
    length: 100,
    comment: 'File name',
  })
  fileName: string;

  @Column({
    type: 'varchar',
    length: 50,
    comment: 'File MIME type',
  })
  mimeType: string;

  @Column({
    type: 'bigint',
    comment: 'File size in bytes',
  })
  fileSize: number;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether the document is active',
  })
  isActive: boolean;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Document expiry date',
  })
  expiryDate?: Date;

  // Relationships
  @ManyToOne(() => Employee, employee => employee.documents, { eager: false })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;
}
