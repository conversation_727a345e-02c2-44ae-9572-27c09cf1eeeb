import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  Join<PERSON>olumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';
import { Employee } from './employee.entity';

export enum NotificationType {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  SUCCESS = 'success',
  REMINDER = 'reminder',
  ALERT = 'alert',
}

export enum NotificationCategory {
  SYSTEM = 'system',
  PAYROLL = 'payroll',
  PERFORMANCE = 'performance',
  LEAVE = 'leave',
  RECRUITMENT = 'recruitment',
  TRAINING = 'training',
  COMPLIANCE = 'compliance',
  GENERAL = 'general',
}

export enum NotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

@Entity('notifications')
@Index(['tenantId', 'recipientId'])
@Index(['tenantId', 'status'])
@Index(['tenantId', 'category'])
@Index(['tenantId', 'createdAt'])
export class Notification {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ name: 'recipient_id' })
  recipientId: string;

  @ManyToOne(() => Employee, { eager: true })
  @JoinColumn({ name: 'recipient_id' })
  recipient: Employee;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text' })
  message: string;

  @Column({
    type: 'enum',
    enum: NotificationType,
    default: NotificationType.INFO,
    name: 'notification_type',
  })
  notificationType: NotificationType;

  @Column({
    type: 'enum',
    enum: NotificationCategory,
    name: 'notification_category',
  })
  notificationCategory: NotificationCategory;

  @Column({
    type: 'enum',
    enum: NotificationStatus,
    default: NotificationStatus.PENDING,
  })
  status: NotificationStatus;

  @Column({ type: 'json', nullable: true, name: 'delivery_channels' })
  deliveryChannels: Array<{
    channel: 'email' | 'sms' | 'push' | 'in_app';
    status: NotificationStatus;
    sentAt?: string;
    deliveredAt?: string;
    errorMessage?: string;
  }>;

  @Column({ type: 'json', nullable: true })
  metadata: {
    entityType?: string;
    entityId?: string;
    actionUrl?: string;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    expiresAt?: string;
    tags?: string[];
  };

  @Column({ name: 'scheduled_for', type: 'timestamp', nullable: true })
  scheduledFor: Date;

  @Column({ name: 'sent_at', type: 'timestamp', nullable: true })
  sentAt: Date;

  @Column({ name: 'read_at', type: 'timestamp', nullable: true })
  readAt: Date;

  @Column({ type: 'boolean', default: false, name: 'is_read' })
  isRead: boolean;

  @Column({ type: 'boolean', default: false, name: 'is_archived' })
  isArchived: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  // Computed properties
  get isExpired(): boolean {
    if (!this.metadata?.expiresAt) return false;
    return new Date(this.metadata.expiresAt) < new Date();
  }

  get isPending(): boolean {
    return this.status === NotificationStatus.PENDING;
  }

  get isScheduled(): boolean {
    return this.scheduledFor && this.scheduledFor > new Date();
  }
}
