import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';
import { Department } from './department.entity';
import { Position } from './position.entity';
import { Application } from './application.entity';

export enum JobStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  CLOSED = 'closed',
  CANCELLED = 'cancelled',
}

export enum JobType {
  FULL_TIME = 'full_time',
  PART_TIME = 'part_time',
  CONTRACT = 'contract',
  TEMPORARY = 'temporary',
  INTERNSHIP = 'internship',
  FREELANCE = 'freelance',
}

@Entity('job_postings')
@Index(['tenantId', 'status'])
@Index(['tenantId', 'departmentId'])
export class JobPosting {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ name: 'department_id' })
  departmentId: string;

  @ManyToOne(() => Department, { eager: true })
  @JoinColumn({ name: 'department_id' })
  department: Department;

  @Column({ name: 'position_id' })
  positionId: string;

  @ManyToOne(() => Position, { eager: true })
  @JoinColumn({ name: 'position_id' })
  position: Position;

  @Column({
    type: 'enum',
    enum: JobStatus,
    default: JobStatus.DRAFT,
  })
  status: JobStatus;

  @Column({
    type: 'enum',
    enum: JobType,
    name: 'job_type',
  })
  jobType: JobType;

  @Column({ length: 255, nullable: true })
  location: string;

  @Column({ type: 'boolean', default: false, name: 'is_remote' })
  isRemote: boolean;

  @Column({ type: 'decimal', precision: 12, scale: 2, nullable: true, name: 'salary_min' })
  salaryMin: number;

  @Column({ type: 'decimal', precision: 12, scale: 2, nullable: true, name: 'salary_max' })
  salaryMax: number;

  @Column({ length: 50, nullable: true, name: 'salary_currency' })
  salaryCurrency: string;

  @Column({ type: 'json', nullable: true })
  requirements: string[];

  @Column({ type: 'json', nullable: true })
  responsibilities: string[];

  @Column({ type: 'json', nullable: true })
  benefits: string[];

  @Column({ type: 'json', nullable: true, name: 'required_skills' })
  requiredSkills: string[];

  @Column({ type: 'json', nullable: true, name: 'preferred_skills' })
  preferredSkills: string[];

  @Column({ type: 'integer', nullable: true, name: 'experience_years_min' })
  experienceYearsMin: number;

  @Column({ type: 'integer', nullable: true, name: 'experience_years_max' })
  experienceYearsMax: number;

  @Column({ type: 'json', nullable: true, name: 'education_requirements' })
  educationRequirements: Array<{
    level: string;
    field?: string;
    required: boolean;
  }>;

  @Column({ name: 'posted_date', type: 'date' })
  postedDate: Date;

  @Column({ name: 'application_deadline', type: 'date', nullable: true })
  applicationDeadline: Date;

  @Column({ type: 'integer', default: 0, name: 'applications_count' })
  applicationsCount: number;

  @Column({ type: 'integer', nullable: true, name: 'positions_available' })
  positionsAvailable: number;

  @OneToMany(() => Application, application => application.jobPosting)
  applications: Application[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;
}
