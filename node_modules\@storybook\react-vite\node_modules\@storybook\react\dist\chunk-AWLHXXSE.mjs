import { applyDecorators } from './chunk-XLZBPYSH.mjs';
import { isForwardRef, isMemo, reactElementToJsxString } from './chunk-CKO6TW2F.mjs';
import { __export } from './chunk-XP5HYGXS.mjs';
import React, { createElement, isValidElement } from 'react';
import { logger } from 'storybook/internal/client-logger';
import { getDocgenSection, SourceType } from 'storybook/internal/docs-tools';
import { useRef, useEffect, emitTransformCode } from 'storybook/preview-api';

var entry_preview_docs_exports={};__export(entry_preview_docs_exports,{applyDecorators:()=>applyDecorators2,decorators:()=>decorators,parameters:()=>parameters});var reactElementToJSXString=reactElementToJsxString,toPascalCase=str=>str.charAt(0).toUpperCase()+str.slice(1),getReactSymbolName=elementType=>(elementType.$$typeof||elementType).toString().replace(/^Symbol\((.*)\)$/,"$1").split(".").map(segment=>segment.split("_").map(toPascalCase).join("")).join(".");function simplifyNodeForStringify(node){if(isValidElement(node)){let props=Object.keys(node.props).reduce((acc,cur)=>(acc[cur]=simplifyNodeForStringify(node.props[cur]),acc),{});return {...node,props,_owner:null}}return Array.isArray(node)?node.map(simplifyNodeForStringify):node}var renderJsx=(code,options)=>{if(typeof code>"u")return logger.warn("Too many skip or undefined component"),null;let renderedJSX=code,Type=renderedJSX.type;for(let i=0;i<options?.skip;i+=1){if(typeof renderedJSX>"u")return logger.warn("Cannot skip undefined element"),null;if(React.Children.count(renderedJSX)>1)return logger.warn("Trying to skip an array of elements"),null;typeof renderedJSX.props.children>"u"?(logger.warn("Not enough children to skip elements."),typeof renderedJSX.type=="function"&&renderedJSX.type.name===""&&(renderedJSX=React.createElement(Type,{...renderedJSX.props}))):typeof renderedJSX.props.children=="function"?renderedJSX=renderedJSX.props.children():renderedJSX=renderedJSX.props.children;}let displayNameDefaults;typeof options?.displayName=="string"?displayNameDefaults={showFunctions:!0,displayName:()=>options.displayName}:displayNameDefaults={displayName:el=>el.type.displayName?el.type.displayName:getDocgenSection(el.type,"displayName")?getDocgenSection(el.type,"displayName"):el.type.render?.displayName?el.type.render.displayName:typeof el.type=="symbol"||el.type.$$typeof&&typeof el.type.$$typeof=="symbol"?getReactSymbolName(el.type):el.type.name&&el.type.name!=="_default"?el.type.name:typeof el.type=="function"?"No Display Name":isForwardRef(el.type)?el.type.render.name:isMemo(el.type)?el.type.type.name:el.type};let opts={...displayNameDefaults,...{filterProps:(value,key)=>value!==void 0},...options};return React.Children.map(code,c=>{let child=typeof c=="number"?c.toString():c,string=(typeof reactElementToJSXString=="function"?reactElementToJSXString:reactElementToJSXString.default)(simplifyNodeForStringify(child),opts);if(string.indexOf("&quot;")>-1){let matches=string.match(/\S+=\\"([^"]*)\\"/g);matches&&matches.forEach(match=>{string=string.replace(match,match.replace(/&quot;/g,"'"));});}return string}).join(`
`).replace(/function\s+noRefCheck\(\)\s*\{\}/g,"() => {}")},defaultOpts={skip:0,showFunctions:!1,enableBeautify:!0,showDefaultProps:!1},skipJsxRender=context=>{let sourceParams=context?.parameters.docs?.source,isArgsStory=context?.parameters.__isArgsStory;return sourceParams?.type===SourceType.DYNAMIC?!1:!isArgsStory||sourceParams?.code||sourceParams?.type===SourceType.CODE},isMdx=node=>node.type?.displayName==="MDXCreateElement"&&!!node.props?.mdxType,mdxToJsx=node=>{if(!isMdx(node))return node;let{mdxType,originalType,children,...rest}=node.props,jsxChildren=[];return children&&(jsxChildren=(Array.isArray(children)?children:[children]).map(mdxToJsx)),createElement(originalType,rest,...jsxChildren)},jsxDecorator=(storyFn,context)=>{let jsx=useRef(void 0),story=storyFn(),skip=skipJsxRender(context),options={...defaultOpts,...context?.parameters.jsx||{}},storyJsx=context.originalStoryFn(context.args,context);return useEffect(()=>{if(skip)return;let sourceJsx=mdxToJsx(storyJsx),rendered=renderJsx(sourceJsx,options);rendered&&jsx.current!==rendered&&(emitTransformCode(rendered,context),jsx.current=rendered);}),story};var applyDecorators2=(storyFn,decorators2)=>{let jsxIndex=decorators2.findIndex(d=>d.originalFn===jsxDecorator),reorderedDecorators=jsxIndex===-1?decorators2:[...decorators2.splice(jsxIndex,1),...decorators2];return applyDecorators(storyFn,reorderedDecorators)};var decorators=[jsxDecorator],parameters={docs:{story:{inline:!0}}};

export { applyDecorators2 as applyDecorators, decorators, entry_preview_docs_exports, parameters };
