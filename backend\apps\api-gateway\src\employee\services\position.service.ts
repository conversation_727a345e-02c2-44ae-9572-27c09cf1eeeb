import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Position } from '@app/database';

@Injectable()
export class PositionService {
  constructor(
    @InjectRepository(Position)
    private readonly positionRepository: Repository<Position>,
  ) {}

  // TODO: Implement position management methods
  async findAll(tenantId: string): Promise<Position[]> {
    return this.positionRepository.find({
      where: { tenantId },
      order: { title: 'ASC' },
    });
  }

  async findById(id: string, tenantId: string): Promise<Position> {
    return this.positionRepository.findOne({
      where: { id, tenantId },
    });
  }
}
