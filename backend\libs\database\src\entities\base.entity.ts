import {
  PrimaryGeneratedColumn,
  CreateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  DeleteDateColumn,
  Column,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

export abstract class BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @CreateDateColumn({
    type: 'timestamp with time zone',
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp with time zone',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  @DeleteDateColumn({
    type: 'timestamp with time zone',
    nullable: true,
  })
  deletedAt?: Date;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who created this record',
  })
  createdBy?: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who last updated this record',
  })
  updatedBy?: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who deleted this record',
  })
  deletedBy?: string;

  @BeforeInsert()
  generateId() {
    if (!this.id) {
      this.id = uuidv4();
    }
  }

  @BeforeUpdate()
  updateTimestamp() {
    this.updatedAt = new Date();
  }
}

export abstract class TenantAwareEntity extends BaseEntity {
  @Column({
    type: 'uuid',
    comment: 'Tenant ID for multi-tenancy',
  })
  tenantId: string;
}

export abstract class EncryptedEntity extends TenantAwareEntity {
  @Column({
    type: 'text',
    nullable: true,
    comment: 'Encryption key reference for PII data',
  })
  encryptionKeyId?: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this record contains encrypted PII data',
  })
  isEncrypted: boolean;
}
