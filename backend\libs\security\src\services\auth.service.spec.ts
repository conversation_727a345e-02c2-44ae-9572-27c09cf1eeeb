import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { UnauthorizedException, BadRequestException } from '@nestjs/common';

import { AuthService } from './auth.service';
import { PasswordService } from './password.service';
import { TokenService } from './token.service';
import { SessionService } from './session.service';
import { MfaService } from './mfa.service';
import { AuditService } from './audit.service';
import { User } from '@app/database/entities/user.entity';
import { UserRole } from '@app/common/enums/user-role.enum';
import { UserStatus } from '@app/common/enums/status.enum';

describe('AuthService', () => {
  let service: AuthService;
  let userRepository: Repository<User>;
  let passwordService: PasswordService;
  let tokenService: TokenService;
  let sessionService: SessionService;
  let mfaService: MfaService;
  let auditService: AuditService;
  let configService: ConfigService;

  const mockUserRepository = {
    findOne: jest.fn(),
    findOneBy: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
    create: jest.fn(),
    remove: jest.fn(),
  };

  const mockPasswordService = {
    hashPassword: jest.fn(),
    verifyPassword: jest.fn(),
    generateResetToken: jest.fn(),
    verifyResetToken: jest.fn(),
  };

  const mockTokenService = {
    generateTokens: jest.fn(),
    generateMfaToken: jest.fn(),
    verifyAccessToken: jest.fn(),
    verifyRefreshToken: jest.fn(),
    revokeRefreshToken: jest.fn(),
    generatePasswordResetToken: jest.fn(),
    verifyPasswordResetToken: jest.fn(),
  };

  const mockSessionService = {
    createSession: jest.fn(),
    getSession: jest.fn(),
    updateSession: jest.fn(),
    deleteSession: jest.fn(),
    deleteAllUserSessions: jest.fn(),
  };

  const mockMfaService = {
    isMfaEnabled: jest.fn(),
    verifyMfaCode: jest.fn(),
    generateMfaSecret: jest.fn(),
    enableMfa: jest.fn(),
    disableMfa: jest.fn(),
  };

  const mockAuditService = {
    log: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn((key: string, defaultValue?: any) => {
      const config = {
        JWT_EXPIRES_IN_SECONDS: 86400,
        MAX_LOGIN_ATTEMPTS: 5,
        ACCOUNT_LOCK_DURATION: 900000,
        PASSWORD_RESET_EXPIRES_IN: 3600,
      };
      return config[key] || defaultValue;
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: PasswordService,
          useValue: mockPasswordService,
        },
        {
          provide: TokenService,
          useValue: mockTokenService,
        },
        {
          provide: SessionService,
          useValue: mockSessionService,
        },
        {
          provide: MfaService,
          useValue: mockMfaService,
        },
        {
          provide: AuditService,
          useValue: mockAuditService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    passwordService = module.get<PasswordService>(PasswordService);
    tokenService = module.get<TokenService>(TokenService);
    sessionService = module.get<SessionService>(SessionService);
    mfaService = module.get<MfaService>(MfaService);
    auditService = module.get<AuditService>(AuditService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validateUser', () => {
    const testEmail = '<EMAIL>';
    const testPassword = 'password123';
    const mockUser = {
      id: 'user-123',
      email: testEmail,
      password: 'hashed-password',
      status: UserStatus.ACTIVE,
      isLocked: false,
      failedLoginAttempts: 0,
    };

    it('should validate user with correct credentials', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockPasswordService.verifyPassword.mockResolvedValue(true);

      const result = await service.validateUser(testEmail, testPassword);

      expect(result).toEqual(mockUser);
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { email: testEmail },
        select: expect.arrayContaining(['password']),
      });
      expect(mockPasswordService.verifyPassword).toHaveBeenCalledWith(
        testPassword,
        mockUser.password
      );
    });

    it('should return null for invalid email', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);

      const result = await service.validateUser(testEmail, testPassword);

      expect(result).toBeNull();
    });

    it('should return null for invalid password', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockPasswordService.verifyPassword.mockResolvedValue(false);

      const result = await service.validateUser(testEmail, testPassword);

      expect(result).toBeNull();
    });

    it('should return null for inactive user', async () => {
      const inactiveUser = { ...mockUser, status: UserStatus.INACTIVE };
      mockUserRepository.findOne.mockResolvedValue(inactiveUser);

      const result = await service.validateUser(testEmail, testPassword);

      expect(result).toBeNull();
    });

    it('should return null for locked user', async () => {
      const lockedUser = { ...mockUser, isLocked: true };
      mockUserRepository.findOne.mockResolvedValue(lockedUser);

      const result = await service.validateUser(testEmail, testPassword);

      expect(result).toBeNull();
    });
  });

  describe('login', () => {
    const loginRequest = {
      email: '<EMAIL>',
      password: 'password123',
      rememberMe: false,
    };
    const ip = '***********';
    const userAgent = 'Mozilla/5.0';
    const mockUser = {
      id: 'user-123',
      email: loginRequest.email,
      role: UserRole.EMPLOYEE,
      tenantId: 'tenant-123',
    };

    it('should login user successfully without MFA', async () => {
      const sessionId = 'session-123';
      const tokens = {
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
      };

      jest.spyOn(service, 'validateUser').mockResolvedValue(mockUser as any);
      mockMfaService.isMfaEnabled.mockResolvedValue(false);
      mockSessionService.createSession.mockResolvedValue(sessionId);
      mockTokenService.generateTokens.mockResolvedValue(tokens);
      mockUserRepository.update.mockResolvedValue(undefined);

      const result = await service.login(loginRequest, ip, userAgent);

      expect(result).toEqual({
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresIn: 86400,
        user: expect.objectContaining({
          id: mockUser.id,
          email: mockUser.email,
          role: mockUser.role,
        }),
      });

      expect(mockSessionService.createSession).toHaveBeenCalledWith({
        userId: mockUser.id,
        ip,
        userAgent,
        rememberMe: false,
      });

      expect(mockUserRepository.update).toHaveBeenCalledWith(mockUser.id, {
        lastLoginAt: expect.any(Date),
      });
    });

    it('should require MFA when enabled', async () => {
      const mfaToken = 'mfa-token';

      jest.spyOn(service, 'validateUser').mockResolvedValue(mockUser as any);
      mockMfaService.isMfaEnabled.mockResolvedValue(true);
      mockTokenService.generateMfaToken.mockResolvedValue(mfaToken);

      const result = await service.login(loginRequest, ip, userAgent);

      expect(result).toEqual({
        requiresMfa: true,
        mfaToken,
      });
    });

    it('should login with valid MFA code', async () => {
      const loginWithMfa = { ...loginRequest, mfaCode: '123456' };
      const sessionId = 'session-123';
      const tokens = {
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
      };

      jest.spyOn(service, 'validateUser').mockResolvedValue(mockUser as any);
      mockMfaService.isMfaEnabled.mockResolvedValue(true);
      mockMfaService.verifyMfaCode.mockResolvedValue(true);
      mockSessionService.createSession.mockResolvedValue(sessionId);
      mockTokenService.generateTokens.mockResolvedValue(tokens);
      mockUserRepository.update.mockResolvedValue(undefined);

      const result = await service.login(loginWithMfa, ip, userAgent);

      expect(result.accessToken).toBe(tokens.accessToken);
      expect(mockMfaService.verifyMfaCode).toHaveBeenCalledWith(
        mockUser.id,
        '123456'
      );
    });

    it('should throw error for invalid MFA code', async () => {
      const loginWithMfa = { ...loginRequest, mfaCode: '123456' };

      jest.spyOn(service, 'validateUser').mockResolvedValue(mockUser as any);
      mockMfaService.isMfaEnabled.mockResolvedValue(true);
      mockMfaService.verifyMfaCode.mockResolvedValue(false);

      await expect(
        service.login(loginWithMfa, ip, userAgent)
      ).rejects.toThrow(UnauthorizedException);
    });

    it('should throw error for invalid credentials', async () => {
      jest.spyOn(service, 'validateUser').mockResolvedValue(null);

      await expect(
        service.login(loginRequest, ip, userAgent)
      ).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('refreshToken', () => {
    const refreshTokenRequest = {
      refreshToken: 'refresh-token',
    };

    const mockPayload = {
      userId: 'user-123',
      sessionId: 'session-123',
      jti: 'token-id',
    };

    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
      role: UserRole.EMPLOYEE,
      tenantId: 'tenant-123',
    };

    it('should refresh token successfully', async () => {
      const newTokens = {
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
      };

      mockTokenService.verifyRefreshToken.mockResolvedValue(mockPayload);
      mockUserRepository.findOneBy.mockResolvedValue(mockUser);
      mockSessionService.getSession.mockResolvedValue({ id: 'session-123' });
      mockTokenService.generateTokens.mockResolvedValue(newTokens);
      mockTokenService.revokeRefreshToken.mockResolvedValue(undefined);

      const result = await service.refreshToken(refreshTokenRequest);

      expect(result).toEqual({
        accessToken: newTokens.accessToken,
        refreshToken: newTokens.refreshToken,
        expiresIn: 86400,
      });

      expect(mockTokenService.revokeRefreshToken).toHaveBeenCalledWith(
        mockPayload.jti
      );
    });

    it('should throw error for invalid refresh token', async () => {
      mockTokenService.verifyRefreshToken.mockRejectedValue(
        new Error('Invalid token')
      );

      await expect(
        service.refreshToken(refreshTokenRequest)
      ).rejects.toThrow(UnauthorizedException);
    });

    it('should throw error for non-existent user', async () => {
      mockTokenService.verifyRefreshToken.mockResolvedValue(mockPayload);
      mockUserRepository.findOneBy.mockResolvedValue(null);

      await expect(
        service.refreshToken(refreshTokenRequest)
      ).rejects.toThrow(UnauthorizedException);
    });

    it('should throw error for invalid session', async () => {
      mockTokenService.verifyRefreshToken.mockResolvedValue(mockPayload);
      mockUserRepository.findOneBy.mockResolvedValue(mockUser);
      mockSessionService.getSession.mockResolvedValue(null);

      await expect(
        service.refreshToken(refreshTokenRequest)
      ).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('logout', () => {
    const userId = 'user-123';
    const sessionId = 'session-123';

    it('should logout user successfully', async () => {
      mockSessionService.deleteSession.mockResolvedValue(undefined);

      await service.logout(userId, sessionId);

      expect(mockSessionService.deleteSession).toHaveBeenCalledWith(
        userId,
        sessionId
      );
      expect(mockAuditService.log).toHaveBeenCalledWith(
        userId,
        'USER_LOGOUT',
        'AUTH',
        expect.any(Object)
      );
    });
  });

  describe('changePassword', () => {
    const userId = 'user-123';
    const changePasswordRequest = {
      currentPassword: 'old-password',
      newPassword: 'new-password',
    };

    const mockUser = {
      id: userId,
      password: 'hashed-old-password',
    };

    it('should change password successfully', async () => {
      const hashedNewPassword = 'hashed-new-password';

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockPasswordService.verifyPassword.mockResolvedValue(true);
      mockPasswordService.hashPassword.mockResolvedValue(hashedNewPassword);
      mockUserRepository.update.mockResolvedValue(undefined);
      mockSessionService.deleteAllUserSessions.mockResolvedValue(undefined);

      await service.changePassword(userId, changePasswordRequest);

      expect(mockPasswordService.verifyPassword).toHaveBeenCalledWith(
        changePasswordRequest.currentPassword,
        mockUser.password
      );
      expect(mockPasswordService.hashPassword).toHaveBeenCalledWith(
        changePasswordRequest.newPassword
      );
      expect(mockUserRepository.update).toHaveBeenCalledWith(userId, {
        password: hashedNewPassword,
        passwordChangedAt: expect.any(Date),
        failedLoginAttempts: 0,
        lockedUntil: null,
      });
      expect(mockSessionService.deleteAllUserSessions).toHaveBeenCalledWith(
        userId
      );
    });

    it('should throw error for invalid current password', async () => {
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockPasswordService.verifyPassword.mockResolvedValue(false);

      await expect(
        service.changePassword(userId, changePasswordRequest)
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw error for non-existent user', async () => {
      mockUserRepository.findOne.mockResolvedValue(null);

      await expect(
        service.changePassword(userId, changePasswordRequest)
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('Account Locking', () => {
    const userId = 'user-123';

    describe('isAccountLocked', () => {
      it('should return false for unlocked account', async () => {
        const mockUser = {
          failedLoginAttempts: 2,
          lockedUntil: null,
        };

        mockUserRepository.findOne.mockResolvedValue(mockUser);

        const result = await service.isAccountLocked(userId);

        expect(result).toBe(false);
      });

      it('should return true for locked account', async () => {
        const futureDate = new Date(Date.now() + 60000); // 1 minute from now
        const mockUser = {
          failedLoginAttempts: 5,
          lockedUntil: futureDate,
        };

        mockUserRepository.findOne.mockResolvedValue(mockUser);

        const result = await service.isAccountLocked(userId);

        expect(result).toBe(true);
      });

      it('should return false for expired lock', async () => {
        const pastDate = new Date(Date.now() - 60000); // 1 minute ago
        const mockUser = {
          failedLoginAttempts: 5,
          lockedUntil: pastDate,
        };

        mockUserRepository.findOne.mockResolvedValue(mockUser);

        const result = await service.isAccountLocked(userId);

        expect(result).toBe(false);
      });

      it('should return false for non-existent user', async () => {
        mockUserRepository.findOne.mockResolvedValue(null);

        const result = await service.isAccountLocked(userId);

        expect(result).toBe(false);
      });
    });

    describe('incrementFailedLoginAttempts', () => {
      it('should increment failed attempts', async () => {
        const mockUser = {
          failedLoginAttempts: 2,
        };

        mockUserRepository.findOne.mockResolvedValue(mockUser);
        mockUserRepository.update.mockResolvedValue(undefined);

        await service.incrementFailedLoginAttempts(userId);

        expect(mockUserRepository.update).toHaveBeenCalledWith(userId, {
          failedLoginAttempts: 3,
        });
      });

      it('should lock account after max attempts', async () => {
        const mockUser = {
          failedLoginAttempts: 4, // One less than max
        };

        mockUserRepository.findOne.mockResolvedValue(mockUser);
        mockUserRepository.update.mockResolvedValue(undefined);

        await service.incrementFailedLoginAttempts(userId);

        expect(mockUserRepository.update).toHaveBeenCalledWith(userId, {
          failedLoginAttempts: 5,
          lockedUntil: expect.any(Date),
        });
      });

      it('should handle non-existent user gracefully', async () => {
        mockUserRepository.findOne.mockResolvedValue(null);

        await expect(
          service.incrementFailedLoginAttempts(userId)
        ).resolves.not.toThrow();
      });
    });
  });
});
