import React, { memo, useState, useCallback, Fragment, useMemo } from 'react';
import { useParameter, useGlobals, addons, types } from 'storybook/internal/manager-api';
import { IconButton, WithTooltip, TooltipLinkList } from 'storybook/internal/components';
import { GridIcon, RefreshIcon, CircleIcon, PhotoIcon } from '@storybook/icons';
import { logger } from 'storybook/internal/client-logger';
import memoize from 'memoizerific';
import { styled } from 'storybook/internal/theming';
import { dedent } from 'ts-dedent';

var ADDON_ID="storybook/background",PARAM_KEY="backgrounds";var DEFAULT_BACKGROUNDS={light:{name:"light",value:"#F8F8F8"},dark:{name:"dark",value:"#333"}};var BackgroundTool=memo(function(){let config=useParameter(PARAM_KEY),[globals,updateGlobals,storyGlobals]=useGlobals(),[isTooltipVisible,setIsTooltipVisible]=useState(!1),{options=DEFAULT_BACKGROUNDS,disable=!0}=config||{};if(disable)return null;let data=globals[PARAM_KEY]||{},backgroundName=data.value,isGridActive=data.grid||!1,item=options[backgroundName],isLocked=!!storyGlobals?.[PARAM_KEY],length=Object.keys(options).length;return React.createElement(Pure,{length,backgroundMap:options,item,updateGlobals,backgroundName,setIsTooltipVisible,isLocked,isGridActive,isTooltipVisible})}),Pure=memo(function(props){let{item,length,updateGlobals,setIsTooltipVisible,backgroundMap,backgroundName,isLocked,isGridActive:isGrid,isTooltipVisible}=props,update=useCallback(input=>{updateGlobals({[PARAM_KEY]:input});},[updateGlobals]);return React.createElement(Fragment,null,React.createElement(IconButton,{key:"grid",active:isGrid,disabled:isLocked,title:"Apply a grid to the preview",onClick:()=>update({value:backgroundName,grid:!isGrid})},React.createElement(GridIcon,null)),length>0?React.createElement(WithTooltip,{key:"background",placement:"top",closeOnOutsideClick:!0,tooltip:({onHide})=>React.createElement(TooltipLinkList,{links:[...item?[{id:"reset",title:"Reset background",icon:React.createElement(RefreshIcon,null),onClick:()=>{update({value:void 0,grid:isGrid}),onHide();}}]:[],...Object.entries(backgroundMap).map(([k,value])=>({id:k,title:value.name,icon:React.createElement(CircleIcon,{color:value?.value||"grey"}),active:k===backgroundName,onClick:()=>{update({value:k,grid:isGrid}),onHide();}}))].flat()}),onVisibleChange:setIsTooltipVisible},React.createElement(IconButton,{disabled:isLocked,key:"background",title:"Change the background of the preview",active:!!item||isTooltipVisible},React.createElement(PhotoIcon,null))):null)});var ColorIcon=styled.span(({background})=>({borderRadius:"1rem",display:"block",height:"1rem",width:"1rem",background}),({theme})=>({boxShadow:`${theme.appBorderColor} 0 0 0 1px inset`}));var getBackgroundColorByName=(currentSelectedValue,backgrounds=[],defaultName)=>{if(currentSelectedValue==="transparent")return "transparent";if(backgrounds.find(background=>background.value===currentSelectedValue)||currentSelectedValue)return currentSelectedValue;let defaultBackground=backgrounds.find(background=>background.name===defaultName);if(defaultBackground)return defaultBackground.value;if(defaultName){let availableColors=backgrounds.map(background=>background.name).join(", ");logger.warn(dedent`
        Backgrounds Addon: could not find the default color "${defaultName}".
        These are the available colors for your story based on your configuration:
        ${availableColors}.
      `);}return "transparent"};var createBackgroundSelectorItem=memoize(1e3)((id,name,value,hasSwatch,change,active)=>({id:id||name,title:name,onClick:()=>{change({selected:value,name});},value,right:hasSwatch?React.createElement(ColorIcon,{background:value}):void 0,active})),getDisplayedItems=memoize(10)((backgrounds,selectedBackgroundColor,change)=>{let backgroundSelectorItems=backgrounds.map(({name,value})=>createBackgroundSelectorItem(null,name,value,!0,change,value===selectedBackgroundColor));return selectedBackgroundColor!=="transparent"?[createBackgroundSelectorItem("reset","Clear background","transparent",null,change,!1),...backgroundSelectorItems]:backgroundSelectorItems}),DEFAULT_BACKGROUNDS_CONFIG={default:null,disable:!0,values:[]},BackgroundToolLegacy=memo(function(){let backgroundsConfig=useParameter(PARAM_KEY,DEFAULT_BACKGROUNDS_CONFIG),[isTooltipVisible,setIsTooltipVisible]=useState(!1),[globals,updateGlobals]=useGlobals(),globalsBackgroundColor=globals[PARAM_KEY]?.value,selectedBackgroundColor=useMemo(()=>getBackgroundColorByName(globalsBackgroundColor,backgroundsConfig.values,backgroundsConfig.default),[backgroundsConfig,globalsBackgroundColor]);Array.isArray(backgroundsConfig)&&logger.warn("Addon Backgrounds api has changed in Storybook 6.0. Please refer to the migration guide: https://github.com/storybookjs/storybook/blob/next/MIGRATION.md");let onBackgroundChange=useCallback(value=>{updateGlobals({[PARAM_KEY]:{...globals[PARAM_KEY],value}});},[backgroundsConfig,globals,updateGlobals]);return backgroundsConfig.disable?null:React.createElement(WithTooltip,{placement:"top",closeOnOutsideClick:!0,tooltip:({onHide})=>React.createElement(TooltipLinkList,{links:getDisplayedItems(backgroundsConfig.values,selectedBackgroundColor,({selected})=>{selectedBackgroundColor!==selected&&onBackgroundChange(selected),onHide();})}),onVisibleChange:setIsTooltipVisible},React.createElement(IconButton,{key:"background",title:"Change the background of the preview",active:selectedBackgroundColor!=="transparent"||isTooltipVisible},React.createElement(PhotoIcon,null)))});var GridToolLegacy=memo(function(){let[globals,updateGlobals]=useGlobals(),{grid}=useParameter(PARAM_KEY,{grid:{disable:!1}});if(grid?.disable)return null;let isActive=globals[PARAM_KEY]?.grid||!1;return React.createElement(IconButton,{key:"background",active:isActive,title:"Apply a grid to the preview",onClick:()=>updateGlobals({[PARAM_KEY]:{...globals[PARAM_KEY],grid:!isActive}})},React.createElement(GridIcon,null))});addons.register(ADDON_ID,()=>{addons.add(ADDON_ID,{title:"Backgrounds",type:types.TOOL,match:({viewMode,tabId})=>!!(viewMode&&viewMode.match(/^(story|docs)$/))&&!tabId,render:()=>FEATURES?.backgroundsStoryGlobals?React.createElement(BackgroundTool,null):React.createElement(Fragment,null,React.createElement(BackgroundToolLegacy,null),React.createElement(GridToolLegacy,null))});});
