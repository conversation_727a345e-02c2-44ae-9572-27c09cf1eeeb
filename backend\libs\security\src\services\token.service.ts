import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';

import { RefreshToken } from '@app/database/entities/refresh-token.entity';
import { JwtPayload } from '../interfaces/jwt-payload.interface';

interface TokenGenerationPayload {
  userId: string;
  email: string;
  role: string;
  tenantId?: string;
  sessionId: string;
}

@Injectable()
export class TokenService {
  constructor(
    @InjectRepository(RefreshToken)
    private refreshTokenRepository: Repository<RefreshToken>,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  /**
   * Generate access and refresh tokens
   */
  async generateTokens(payload: TokenGenerationPayload): Promise<{
    accessToken: string;
    refreshToken: string;
  }> {
    const { userId, email, role, tenantId, sessionId } = payload;

    // Generate access token
    const accessTokenPayload: JwtPayload = {
      sub: userId,
      email,
      role,
      tenantId,
      sessionId,
      tokenType: 'access',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + this.getAccessTokenExpirationSeconds(),
      iss: 'peoplenest-hrms',
      aud: 'peoplenest-users',
    };

    const accessToken = this.jwtService.sign(accessTokenPayload);

    // Generate refresh token
    const refreshTokenId = uuidv4();
    const refreshTokenPayload: JwtPayload = {
      sub: userId,
      email,
      role,
      tenantId,
      sessionId,
      tokenType: 'refresh',
      jti: refreshTokenId,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + this.getRefreshTokenExpirationSeconds(),
      iss: 'peoplenest-hrms',
      aud: 'peoplenest-users',
    };

    const refreshToken = this.jwtService.sign(refreshTokenPayload, {
      secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
      expiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRES_IN', '7d'),
    });

    // Store refresh token in database
    await this.storeRefreshToken({
      id: refreshTokenId,
      userId,
      sessionId,
      token: await this.hashToken(refreshToken),
      expiresAt: new Date(Date.now() + this.getRefreshTokenExpirationSeconds() * 1000),
    });

    return {
      accessToken,
      refreshToken,
    };
  }

  /**
   * Validate refresh token
   */
  async validateRefreshToken(
    userId: string,
    tokenId: string,
    token: string,
  ): Promise<boolean> {
    const storedToken = await this.refreshTokenRepository.findOne({
      where: {
        id: tokenId,
        userId,
        isRevoked: false,
      },
    });

    if (!storedToken) {
      return false;
    }

    // Check if token is expired
    if (storedToken.expiresAt < new Date()) {
      await this.revokeRefreshToken(tokenId);
      return false;
    }

    // Verify token hash
    const tokenHash = await this.hashToken(token);
    return tokenHash === storedToken.token;
  }

  /**
   * Validate refresh token and return payload
   */
  async validateRefreshToken(token: string): Promise<any> {
    try {
      const payload = this.jwtService.verify(token, {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
      });

      // Check if token exists in database and is not revoked
      const storedToken = await this.refreshTokenRepository.findOne({
        where: {
          id: payload.jti,
          userId: payload.sub,
          isRevoked: false,
        },
      });

      if (!storedToken) {
        return null;
      }

      return {
        userId: payload.sub,
        sessionId: payload.sessionId,
        jti: payload.jti,
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Revoke refresh token
   */
  async revokeRefreshToken(tokenId: string): Promise<void> {
    await this.refreshTokenRepository.update(
      { id: tokenId },
      { isRevoked: true, revokedAt: new Date() },
    );
  }

  /**
   * Revoke all refresh tokens for a session
   */
  async revokeRefreshTokensBySession(
    userId: string,
    sessionId: string,
  ): Promise<void> {
    await this.refreshTokenRepository.update(
      { userId, sessionId, isRevoked: false },
      { isRevoked: true, revokedAt: new Date() },
    );
  }

  /**
   * Revoke all refresh tokens for a user
   */
  async revokeAllRefreshTokens(userId: string): Promise<void> {
    await this.refreshTokenRepository.update(
      { userId, isRevoked: false },
      { isRevoked: true, revokedAt: new Date() },
    );
  }

  /**
   * Generate MFA token for two-step authentication
   */
  async generateMfaToken(userId: string): Promise<string> {
    const payload = {
      sub: userId,
      type: 'mfa',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 300, // 5 minutes
    };

    return this.jwtService.sign(payload, {
      secret: this.configService.get<string>('JWT_MFA_SECRET'),
    });
  }

  /**
   * Validate MFA token
   */
  async validateMfaToken(token: string): Promise<{ userId: string } | null> {
    try {
      const payload = this.jwtService.verify(token, {
        secret: this.configService.get<string>('JWT_MFA_SECRET'),
      });

      if (payload.type !== 'mfa') {
        return null;
      }

      return { userId: payload.sub };
    } catch (error) {
      return null;
    }
  }

  /**
   * Generate password reset token
   */
  async generatePasswordResetToken(userId: string): Promise<string> {
    const payload = {
      sub: userId,
      type: 'password_reset',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
    };

    return this.jwtService.sign(payload, {
      secret: this.configService.get<string>('JWT_RESET_SECRET'),
    });
  }

  /**
   * Validate password reset token
   */
  async validatePasswordResetToken(
    token: string,
  ): Promise<{ userId: string } | null> {
    try {
      const payload = this.jwtService.verify(token, {
        secret: this.configService.get<string>('JWT_RESET_SECRET'),
      });

      if (payload.type !== 'password_reset') {
        return null;
      }

      return { userId: payload.sub };
    } catch (error) {
      return null;
    }
  }

  /**
   * Revoke password reset token (implement token blacklist if needed)
   */
  async revokePasswordResetToken(token: string): Promise<void> {
    // TODO: Implement token blacklist if needed
    // For now, tokens will expire naturally
  }

  /**
   * Clean up expired refresh tokens
   */
  async cleanupExpiredTokens(): Promise<void> {
    await this.refreshTokenRepository.delete({
      expiresAt: { $lt: new Date() },
    });
  }

  /**
   * Store refresh token in database
   */
  private async storeRefreshToken(tokenData: {
    id: string;
    userId: string;
    sessionId: string;
    token: string;
    expiresAt: Date;
  }): Promise<void> {
    const refreshToken = this.refreshTokenRepository.create(tokenData);
    await this.refreshTokenRepository.save(refreshToken);
  }

  /**
   * Hash token for secure storage
   */
  private async hashToken(token: string): Promise<string> {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * Get access token expiration in seconds
   */
  private getAccessTokenExpirationSeconds(): number {
    const expiresIn = this.configService.get<string>('JWT_EXPIRES_IN', '24h');
    return this.parseTimeToSeconds(expiresIn);
  }

  /**
   * Get refresh token expiration in seconds
   */
  private getRefreshTokenExpirationSeconds(): number {
    const expiresIn = this.configService.get<string>('JWT_REFRESH_EXPIRES_IN', '7d');
    return this.parseTimeToSeconds(expiresIn);
  }

  /**
   * Parse time string to seconds
   */
  private parseTimeToSeconds(timeString: string): number {
    const match = timeString.match(/^(\d+)([smhd])$/);
    if (!match) {
      return 86400; // Default to 24 hours
    }

    const [, value, unit] = match;
    const num = parseInt(value, 10);

    switch (unit) {
      case 's':
        return num;
      case 'm':
        return num * 60;
      case 'h':
        return num * 3600;
      case 'd':
        return num * 86400;
      default:
        return 86400;
    }
  }
}
