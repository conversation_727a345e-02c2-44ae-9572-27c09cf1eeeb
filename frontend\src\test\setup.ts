import { expect, afterEach, vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';

// Extend Vitest's expect with jest-dom matchers
expect.extend(matchers);

// Cleanup after each test case
afterEach(() => {
  cleanup();
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  unobserve: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  unobserve: vi.fn(),
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: vi.fn(),
});

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
};

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

// Mock fetch
global.fetch = vi.fn();

// Mock URL.createObjectURL
Object.defineProperty(URL, 'createObjectURL', {
  writable: true,
  value: vi.fn(() => 'mocked-url'),
});

Object.defineProperty(URL, 'revokeObjectURL', {
  writable: true,
  value: vi.fn(),
});

// Mock crypto.randomUUID
Object.defineProperty(crypto, 'randomUUID', {
  writable: true,
  value: vi.fn(() => 'mocked-uuid'),
});

// Mock environment variables
vi.mock('import.meta', () => ({
  env: {
    VITE_API_URL: 'http://localhost:3001',
    VITE_GRAPHQL_URL: 'http://localhost:3001/graphql',
    VITE_APP_NAME: 'PeopleNest',
    VITE_APP_VERSION: '1.0.0',
    DEV: true,
    PROD: false,
    MODE: 'test',
  },
}));

// Mock React Router
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({
      pathname: '/',
      search: '',
      hash: '',
      state: null,
      key: 'default',
    }),
    useParams: () => ({}),
    useSearchParams: () => [new URLSearchParams(), vi.fn()],
  };
});

// Mock Apollo Client
vi.mock('@apollo/client', () => ({
  useQuery: vi.fn(() => ({
    data: null,
    loading: false,
    error: null,
    refetch: vi.fn(),
  })),
  useMutation: vi.fn(() => [
    vi.fn(),
    {
      data: null,
      loading: false,
      error: null,
    },
  ]),
  useLazyQuery: vi.fn(() => [
    vi.fn(),
    {
      data: null,
      loading: false,
      error: null,
    },
  ]),
  useSubscription: vi.fn(() => ({
    data: null,
    loading: false,
    error: null,
  })),
  gql: vi.fn((template, ...substitutions) => {
    let result = template[0];
    for (let i = 0; i < substitutions.length; i++) {
      result += substitutions[i] + template[i + 1];
    }
    return result;
  }),
  ApolloProvider: ({ children }: { children: React.ReactNode }) => children,
  createHttpLink: vi.fn(),
  InMemoryCache: vi.fn(() => ({})),
  ApolloClient: vi.fn(() => ({})),
}));

// Mock React Query
vi.mock('@tanstack/react-query', () => ({
  useQuery: vi.fn(() => ({
    data: null,
    isLoading: false,
    error: null,
    refetch: vi.fn(),
  })),
  useMutation: vi.fn(() => ({
    mutate: vi.fn(),
    mutateAsync: vi.fn(),
    data: null,
    isLoading: false,
    error: null,
  })),
  useQueryClient: vi.fn(() => ({
    invalidateQueries: vi.fn(),
    setQueryData: vi.fn(),
    getQueryData: vi.fn(),
  })),
  QueryClient: vi.fn(() => ({})),
  QueryClientProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock Redux
vi.mock('react-redux', () => ({
  useSelector: vi.fn(),
  useDispatch: vi.fn(() => vi.fn()),
  Provider: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock Zustand
vi.mock('zustand', () => ({
  create: vi.fn(() => vi.fn()),
}));

// Mock React Hook Form
vi.mock('react-hook-form', () => ({
  useForm: vi.fn(() => ({
    register: vi.fn(),
    handleSubmit: vi.fn((fn) => fn),
    formState: { errors: {}, isSubmitting: false, isValid: true },
    reset: vi.fn(),
    setValue: vi.fn(),
    getValues: vi.fn(),
    watch: vi.fn(),
    control: {},
  })),
  Controller: ({ render }: { render: any }) => render({ field: {}, fieldState: {} }),
  useController: vi.fn(() => ({
    field: {},
    fieldState: {},
  })),
}));

// Mock date-fns
vi.mock('date-fns', () => ({
  format: vi.fn((date, formatStr) => date.toISOString()),
  parseISO: vi.fn((dateStr) => new Date(dateStr)),
  isValid: vi.fn(() => true),
  addDays: vi.fn((date, days) => new Date(date.getTime() + days * 24 * 60 * 60 * 1000)),
  subDays: vi.fn((date, days) => new Date(date.getTime() - days * 24 * 60 * 60 * 1000)),
  startOfDay: vi.fn((date) => new Date(date.getFullYear(), date.getMonth(), date.getDate())),
  endOfDay: vi.fn((date) => new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999)),
}));

// Mock chart libraries
vi.mock('recharts', () => ({
  ResponsiveContainer: ({ children }: { children: React.ReactNode }) => children,
  LineChart: vi.fn(() => null),
  Line: vi.fn(() => null),
  XAxis: vi.fn(() => null),
  YAxis: vi.fn(() => null),
  CartesianGrid: vi.fn(() => null),
  Tooltip: vi.fn(() => null),
  Legend: vi.fn(() => null),
  BarChart: vi.fn(() => null),
  Bar: vi.fn(() => null),
  PieChart: vi.fn(() => null),
  Pie: vi.fn(() => null),
  Cell: vi.fn(() => null),
}));

// Mock file upload
global.File = class MockFile {
  constructor(
    public bits: BlobPart[],
    public name: string,
    public options?: FilePropertyBag
  ) {}
  
  get size() { return 1024; }
  get type() { return 'text/plain'; }
  get lastModified() { return Date.now(); }
  
  arrayBuffer() { return Promise.resolve(new ArrayBuffer(0)); }
  slice() { return new MockFile([], ''); }
  stream() { return new ReadableStream(); }
  text() { return Promise.resolve(''); }
};

global.FileReader = class MockFileReader {
  result: string | ArrayBuffer | null = null;
  error: DOMException | null = null;
  readyState: number = 0;
  
  onload: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null = null;
  onerror: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null = null;
  onprogress: ((this: FileReader, ev: ProgressEvent<FileReader>) => any) | null = null;
  
  abort() {}
  readAsArrayBuffer() {}
  readAsBinaryString() {}
  readAsDataURL() {
    this.result = 'data:text/plain;base64,';
    if (this.onload) {
      this.onload({} as ProgressEvent<FileReader>);
    }
  }
  readAsText() {
    this.result = '';
    if (this.onload) {
      this.onload({} as ProgressEvent<FileReader>);
    }
  }
  
  addEventListener() {}
  removeEventListener() {}
  dispatchEvent() { return true; }
};

// Test utilities
export const createMockUser = (overrides = {}) => ({
  id: 'user-123',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  role: 'EMPLOYEE',
  status: 'ACTIVE',
  ...overrides,
});

export const createMockEmployee = (overrides = {}) => ({
  id: 'emp-123',
  employeeId: 'EMP001',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  department: 'Engineering',
  position: 'Software Developer',
  hireDate: '2023-01-01',
  status: 'ACTIVE',
  ...overrides,
});

export const createMockApiResponse = <T>(data: T, overrides = {}) => ({
  data,
  status: 200,
  statusText: 'OK',
  headers: {},
  config: {},
  ...overrides,
});

// Custom render function for testing with providers
export { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
export { userEvent } from '@testing-library/user-event';
