import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';
import { Report } from './report.entity';

export enum ScheduleFrequency {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
  CUSTOM = 'custom',
}

export enum ScheduleStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

@Entity('report_schedules')
@Index(['tenantId', 'reportId'])
@Index(['tenantId', 'status'])
@Index(['tenantId', 'nextRunDate'])
export class ReportSchedule {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ name: 'report_id' })
  reportId: string;

  @ManyToOne(() => Report, report => report.schedules)
  @JoinColumn({ name: 'report_id' })
  report: Report;

  @Column({ length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: ScheduleFrequency,
  })
  frequency: ScheduleFrequency;

  @Column({
    type: 'enum',
    enum: ScheduleStatus,
    default: ScheduleStatus.ACTIVE,
  })
  status: ScheduleStatus;

  @Column({ length: 100, nullable: true, name: 'cron_expression' })
  cronExpression: string;

  @Column({ length: 50, nullable: true })
  timezone: string;

  @Column({ name: 'start_date', type: 'date' })
  startDate: Date;

  @Column({ name: 'end_date', type: 'date', nullable: true })
  endDate: Date;

  @Column({ name: 'next_run_date', type: 'timestamp' })
  nextRunDate: Date;

  @Column({ name: 'last_run_date', type: 'timestamp', nullable: true })
  lastRunDate: Date;

  @Column({ type: 'integer', default: 0, name: 'run_count' })
  runCount: number;

  @Column({ type: 'integer', nullable: true, name: 'max_runs' })
  maxRuns: number;

  @Column({ type: 'json', nullable: true, name: 'delivery_settings' })
  deliverySettings: {
    email?: {
      recipients: string[];
      subject?: string;
      body?: string;
      attachReport: boolean;
    };
    storage?: {
      location: string;
      retentionDays?: number;
    };
    webhook?: {
      url: string;
      headers?: Record<string, string>;
    };
  };

  @Column({ type: 'json', nullable: true, name: 'execution_history' })
  executionHistory: Array<{
    runDate: string;
    status: 'success' | 'failed';
    duration: number;
    recordCount?: number;
    errorMessage?: string;
    filePath?: string;
  }>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater: User;

  // Computed properties
  get isActive(): boolean {
    return this.status === ScheduleStatus.ACTIVE;
  }

  get isDue(): boolean {
    return this.nextRunDate <= new Date();
  }

  get hasReachedMaxRuns(): boolean {
    return this.maxRuns !== null && this.runCount >= this.maxRuns;
  }

  get successRate(): number {
    if (!this.executionHistory || this.executionHistory.length === 0) return 0;
    const successCount = this.executionHistory.filter(h => h.status === 'success').length;
    return (successCount / this.executionHistory.length) * 100;
  }
}
