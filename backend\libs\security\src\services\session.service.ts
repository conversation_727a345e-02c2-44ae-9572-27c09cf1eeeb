import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

import { Session } from '@app/database/entities/session.entity';
import { SessionInfo } from '../interfaces/auth.interface';

interface CreateSessionData {
  userId: string;
  ip: string;
  userAgent: string;
  rememberMe?: boolean;
}

@Injectable()
export class SessionService {
  constructor(
    @InjectRepository(Session)
    private sessionRepository: Repository<Session>,
    private configService: ConfigService,
  ) {}

  /**
   * Create a new session
   */
  async createSession(data: CreateSessionData): Promise<string> {
    const { userId, ip, userAgent, rememberMe = false } = data;

    const sessionId = uuidv4();
    const now = new Date();
    
    // Calculate expiration based on remember me option
    const expirationHours = rememberMe 
      ? this.configService.get<number>('SESSION_REMEMBER_HOURS', 720) // 30 days
      : this.configService.get<number>('SESSION_HOURS', 24); // 24 hours

    const expiresAt = new Date(now.getTime() + expirationHours * 60 * 60 * 1000);

    const session = this.sessionRepository.create({
      id: sessionId,
      userId,
      ip,
      userAgent,
      createdAt: now,
      lastActivityAt: now,
      expiresAt,
      isActive: true,
      rememberMe,
    });

    await this.sessionRepository.save(session);

    return sessionId;
  }

  /**
   * Validate session
   */
  async validateSession(userId: string, sessionId: string): Promise<boolean> {
    const session = await this.sessionRepository.findOne({
      where: {
        id: sessionId,
        userId,
        isActive: true,
      },
    });

    if (!session) {
      return false;
    }

    // Check if session is expired
    if (session.expiresAt < new Date()) {
      await this.revokeSession(sessionId);
      return false;
    }

    return true;
  }

  /**
   * Update session activity
   */
  async updateSessionActivity(sessionId: string): Promise<void> {
    await this.sessionRepository.update(sessionId, {
      lastActivityAt: new Date(),
    });
  }

  /**
   * Revoke session
   */
  async revokeSession(sessionId: string): Promise<void> {
    await this.sessionRepository.update(sessionId, {
      isActive: false,
      revokedAt: new Date(),
    });
  }

  /**
   * Revoke all sessions for a user
   */
  async revokeAllUserSessions(userId: string, exceptSessionId?: string): Promise<void> {
    const query = this.sessionRepository
      .createQueryBuilder()
      .update(Session)
      .set({
        isActive: false,
        revokedAt: new Date(),
      })
      .where('userId = :userId', { userId })
      .andWhere('isActive = :isActive', { isActive: true });

    if (exceptSessionId) {
      query.andWhere('id != :exceptSessionId', { exceptSessionId });
    }

    await query.execute();
  }

  /**
   * Get active sessions for a user
   */
  async getUserSessions(userId: string): Promise<SessionInfo[]> {
    const sessions = await this.sessionRepository.find({
      where: {
        userId,
        isActive: true,
        expiresAt: LessThan(new Date()),
      },
      order: {
        lastActivityAt: 'DESC',
      },
    });

    return sessions.map(session => ({
      id: session.id,
      userId: session.userId,
      ip: session.ip,
      userAgent: session.userAgent,
      createdAt: session.createdAt,
      lastActivityAt: session.lastActivityAt,
      expiresAt: session.expiresAt,
      isActive: session.isActive,
    }));
  }

  /**
   * Get session by ID
   */
  async getSession(sessionId: string): Promise<SessionInfo | null> {
    const session = await this.sessionRepository.findOne({
      where: { id: sessionId },
    });

    if (!session) {
      return null;
    }

    return {
      id: session.id,
      userId: session.userId,
      ip: session.ip,
      userAgent: session.userAgent,
      createdAt: session.createdAt,
      lastActivityAt: session.lastActivityAt,
      expiresAt: session.expiresAt,
      isActive: session.isActive,
    };
  }

  /**
   * Extend session expiration
   */
  async extendSession(sessionId: string, hours: number = 24): Promise<void> {
    const newExpiresAt = new Date(Date.now() + hours * 60 * 60 * 1000);
    
    await this.sessionRepository.update(sessionId, {
      expiresAt: newExpiresAt,
    });
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<void> {
    await this.sessionRepository.update(
      {
        expiresAt: LessThan(new Date()),
        isActive: true,
      },
      {
        isActive: false,
        revokedAt: new Date(),
      },
    );
  }

  /**
   * Get session statistics for a user
   */
  async getSessionStats(userId: string): Promise<{
    totalSessions: number;
    activeSessions: number;
    expiredSessions: number;
    revokedSessions: number;
  }> {
    const [total, active, expired, revoked] = await Promise.all([
      this.sessionRepository.count({ where: { userId } }),
      this.sessionRepository.count({ 
        where: { userId, isActive: true } 
      }),
      this.sessionRepository.count({ 
        where: { 
          userId, 
          isActive: false,
          expiresAt: LessThan(new Date()),
        } 
      }),
      this.sessionRepository.count({ 
        where: { 
          userId, 
          isActive: false,
          revokedAt: { $ne: null },
        } 
      }),
    ]);

    return {
      totalSessions: total,
      activeSessions: active,
      expiredSessions: expired,
      revokedSessions: revoked,
    };
  }

  /**
   * Check for concurrent sessions limit
   */
  async checkConcurrentSessionsLimit(userId: string): Promise<boolean> {
    const maxConcurrentSessions = this.configService.get<number>(
      'MAX_CONCURRENT_SESSIONS',
      5,
    );

    const activeSessions = await this.sessionRepository.count({
      where: {
        userId,
        isActive: true,
        expiresAt: LessThan(new Date()),
      },
    });

    return activeSessions < maxConcurrentSessions;
  }

  /**
   * Revoke oldest session if limit exceeded
   */
  async revokeOldestSessionIfNeeded(userId: string): Promise<void> {
    const canCreateSession = await this.checkConcurrentSessionsLimit(userId);
    
    if (!canCreateSession) {
      const oldestSession = await this.sessionRepository.findOne({
        where: {
          userId,
          isActive: true,
        },
        order: {
          lastActivityAt: 'ASC',
        },
      });

      if (oldestSession) {
        await this.revokeSession(oldestSession.id);
      }
    }
  }

  /**
   * Update session location/IP if changed
   */
  async updateSessionLocation(
    sessionId: string,
    ip: string,
    userAgent?: string,
  ): Promise<void> {
    const updateData: any = { ip };
    
    if (userAgent) {
      updateData.userAgent = userAgent;
    }

    await this.sessionRepository.update(sessionId, updateData);
  }

  /**
   * Mark session as suspicious
   */
  async markSessionSuspicious(
    sessionId: string,
    reason: string,
  ): Promise<void> {
    await this.sessionRepository.update(sessionId, {
      isSuspicious: true,
      suspiciousReason: reason,
      suspiciousAt: new Date(),
    });
  }
}
