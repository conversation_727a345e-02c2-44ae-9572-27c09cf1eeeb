{"name": "@storybook/addon-measure", "version": "8.6.14", "description": "Inspect layouts by visualizing the box model", "keywords": ["storybook-addons", "essentials", "style", "CSS", "design"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/addons/measure", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/addons/measure"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./preview": {"types": "./dist/preview.d.ts", "import": "./dist/preview.mjs", "require": "./dist/preview.js"}, "./manager": "./dist/manager.js", "./register": "./dist/manager.js", "./package.json": "./package.json"}, "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["dist/index.d.ts"], "manager": ["dist/manager.d.ts"], "preview": ["dist/preview.d.ts"]}}, "files": ["dist/**/*", "README.md", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/addon-bundle.ts"}, "dependencies": {"@storybook/global": "^5.0.0", "tiny-invariant": "^1.3.1"}, "devDependencies": {"@storybook/icons": "^1.2.12", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.7.3"}, "peerDependencies": {"storybook": "^8.6.14"}, "publishConfig": {"access": "public"}, "bundler": {"exportEntries": ["./src/index.ts"], "managerEntries": ["./src/manager.tsx"], "previewEntries": ["./src/preview.tsx"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae16", "storybook": {"displayName": "Measure", "unsupportedFrameworks": ["react-native"], "icon": "https://user-images.githubusercontent.com/42671/119589951-dbcd9600-bda1-11eb-9227-078f3cfc1e74.png"}}