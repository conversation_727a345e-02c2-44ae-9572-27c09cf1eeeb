import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AuditLog, Employee } from '@app/database';
import { AuditService } from '@app/security';

@Injectable()
export class EmployeeAuditService {
  private readonly logger = new Logger(EmployeeAuditService.name);

  constructor(
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
    private readonly auditService: AuditService,
  ) {}

  /**
   * Log employee creation
   */
  async logEmployeeCreation(
    employee: Employee,
    createdBy: string,
    tenantId: string,
  ): Promise<void> {
    try {
      await this.auditService.log({
        action: 'EMPLOYEE_CREATED',
        entityType: 'Employee',
        entityId: employee.id,
        userId: createdBy,
        tenantId,
        details: {
          employeeId: employee.employeeId,
          email: employee.email,
          name: `${employee.firstName} ${employee.lastName}`,
          department: employee.department?.name,
          position: employee.position?.title,
          employmentType: employee.employmentType,
          dateOfJoining: employee.dateOfJoining,
        },
        riskLevel: 'LOW',
        complianceTag: 'EMPLOYEE_LIFECYCLE',
      });

      this.logger.log(`Employee creation audit logged for ${employee.employeeId}`);
    } catch (error) {
      this.logger.error(`Failed to log employee creation audit for ${employee.employeeId}`, error);
    }
  }

  /**
   * Log employee update
   */
  async logEmployeeUpdate(
    employee: Employee,
    originalValues: Partial<Employee>,
    changes: Record<string, any>,
    updatedBy: string,
    tenantId: string,
  ): Promise<void> {
    try {
      // Determine risk level based on changes
      const riskLevel = this.determineRiskLevel(changes);
      
      await this.auditService.log({
        action: 'EMPLOYEE_UPDATED',
        entityType: 'Employee',
        entityId: employee.id,
        userId: updatedBy,
        tenantId,
        details: {
          employeeId: employee.employeeId,
          changes,
          originalValues: this.sanitizeOriginalValues(originalValues),
        },
        riskLevel,
        complianceTag: 'EMPLOYEE_DATA_CHANGE',
      });

      this.logger.log(`Employee update audit logged for ${employee.employeeId}`);
    } catch (error) {
      this.logger.error(`Failed to log employee update audit for ${employee.employeeId}`, error);
    }
  }

  /**
   * Log employee termination
   */
  async logEmployeeTermination(
    employee: Employee,
    terminatedBy: string,
    tenantId: string,
    reason?: string,
    lastWorkingDay?: Date,
  ): Promise<void> {
    try {
      await this.auditService.log({
        action: 'EMPLOYEE_TERMINATED',
        entityType: 'Employee',
        entityId: employee.id,
        userId: terminatedBy,
        tenantId,
        details: {
          employeeId: employee.employeeId,
          email: employee.email,
          name: `${employee.firstName} ${employee.lastName}`,
          reason,
          lastWorkingDay,
          terminationDate: new Date(),
          yearsOfService: this.calculateYearsOfService(employee.dateOfJoining),
        },
        riskLevel: 'MEDIUM',
        complianceTag: 'EMPLOYEE_TERMINATION',
      });

      this.logger.log(`Employee termination audit logged for ${employee.employeeId}`);
    } catch (error) {
      this.logger.error(`Failed to log employee termination audit for ${employee.employeeId}`, error);
    }
  }

  /**
   * Log employee data access
   */
  async logEmployeeDataAccess(
    employeeId: string,
    accessedBy: string,
    tenantId: string,
    accessType: 'VIEW' | 'EXPORT' | 'PRINT',
    dataFields?: string[],
  ): Promise<void> {
    try {
      await this.auditService.log({
        action: `EMPLOYEE_DATA_${accessType}`,
        entityType: 'Employee',
        entityId: employeeId,
        userId: accessedBy,
        tenantId,
        details: {
          accessType,
          dataFields,
          accessTime: new Date(),
        },
        riskLevel: accessType === 'EXPORT' ? 'MEDIUM' : 'LOW',
        complianceTag: 'DATA_ACCESS',
      });

      this.logger.log(`Employee data access audit logged for ${employeeId}`);
    } catch (error) {
      this.logger.error(`Failed to log employee data access audit for ${employeeId}`, error);
    }
  }

  /**
   * Log bulk employee action
   */
  async logBulkEmployeeAction(
    employeeIds: string[],
    action: string,
    performedBy: string,
    tenantId: string,
    reason?: string,
  ): Promise<void> {
    try {
      await this.auditService.log({
        action: `BULK_EMPLOYEE_${action.toUpperCase()}`,
        entityType: 'Employee',
        entityId: null, // Bulk action doesn't target single entity
        userId: performedBy,
        tenantId,
        details: {
          action,
          employeeIds,
          employeeCount: employeeIds.length,
          reason,
          actionDate: new Date(),
        },
        riskLevel: 'HIGH', // Bulk actions are always high risk
        complianceTag: 'BULK_OPERATION',
      });

      this.logger.log(`Bulk employee action audit logged for ${employeeIds.length} employees`);
    } catch (error) {
      this.logger.error(`Failed to log bulk employee action audit`, error);
    }
  }

  /**
   * Log sensitive data access
   */
  async logSensitiveDataAccess(
    employeeId: string,
    accessedBy: string,
    tenantId: string,
    dataType: 'SALARY' | 'SSN' | 'BANK_ACCOUNT' | 'PASSPORT' | 'VISA',
    purpose?: string,
  ): Promise<void> {
    try {
      await this.auditService.log({
        action: 'SENSITIVE_DATA_ACCESS',
        entityType: 'Employee',
        entityId: employeeId,
        userId: accessedBy,
        tenantId,
        details: {
          dataType,
          purpose,
          accessTime: new Date(),
        },
        riskLevel: 'HIGH',
        complianceTag: 'SENSITIVE_DATA_ACCESS',
      });

      this.logger.log(`Sensitive data access audit logged for ${employeeId}`);
    } catch (error) {
      this.logger.error(`Failed to log sensitive data access audit for ${employeeId}`, error);
    }
  }

  /**
   * Log document upload/download
   */
  async logDocumentAction(
    employeeId: string,
    documentId: string,
    action: 'UPLOAD' | 'DOWNLOAD' | 'DELETE' | 'VIEW',
    performedBy: string,
    tenantId: string,
    documentType?: string,
    fileName?: string,
  ): Promise<void> {
    try {
      await this.auditService.log({
        action: `DOCUMENT_${action}`,
        entityType: 'EmployeeDocument',
        entityId: documentId,
        userId: performedBy,
        tenantId,
        details: {
          employeeId,
          documentType,
          fileName,
          action,
          actionTime: new Date(),
        },
        riskLevel: action === 'DELETE' ? 'HIGH' : 'MEDIUM',
        complianceTag: 'DOCUMENT_MANAGEMENT',
      });

      this.logger.log(`Document ${action.toLowerCase()} audit logged for employee ${employeeId}`);
    } catch (error) {
      this.logger.error(`Failed to log document ${action.toLowerCase()} audit for employee ${employeeId}`, error);
    }
  }

  /**
   * Get employee audit trail
   */
  async getEmployeeAuditTrail(
    employeeId: string,
    tenantId: string,
    startDate?: Date,
    endDate?: Date,
    actions?: string[],
  ): Promise<AuditLog[]> {
    try {
      const queryBuilder = this.auditLogRepository
        .createQueryBuilder('audit')
        .where('audit.entityId = :employeeId', { employeeId })
        .andWhere('audit.tenantId = :tenantId', { tenantId })
        .orderBy('audit.createdAt', 'DESC');

      if (startDate) {
        queryBuilder.andWhere('audit.createdAt >= :startDate', { startDate });
      }

      if (endDate) {
        queryBuilder.andWhere('audit.createdAt <= :endDate', { endDate });
      }

      if (actions && actions.length > 0) {
        queryBuilder.andWhere('audit.action IN (:...actions)', { actions });
      }

      const auditLogs = await queryBuilder.getMany();

      this.logger.log(`Retrieved ${auditLogs.length} audit entries for employee ${employeeId}`);
      return auditLogs;
    } catch (error) {
      this.logger.error(`Failed to retrieve audit trail for employee ${employeeId}`, error);
      return [];
    }
  }

  /**
   * Determine risk level based on changes
   */
  private determineRiskLevel(changes: Record<string, any>): 'LOW' | 'MEDIUM' | 'HIGH' {
    const highRiskFields = ['baseSalary', 'status', 'employmentType', 'managerId'];
    const mediumRiskFields = ['departmentId', 'positionId', 'workLocation'];

    const changedFields = Object.keys(changes);

    if (changedFields.some(field => highRiskFields.includes(field))) {
      return 'HIGH';
    }

    if (changedFields.some(field => mediumRiskFields.includes(field))) {
      return 'MEDIUM';
    }

    return 'LOW';
  }

  /**
   * Sanitize original values for audit (remove sensitive data)
   */
  private sanitizeOriginalValues(originalValues: Partial<Employee>): Record<string, any> {
    const sanitized = { ...originalValues };
    
    // Remove sensitive fields from audit log
    delete sanitized.socialSecurityNumber;
    delete sanitized.taxId;
    delete sanitized.bankAccountNumber;
    delete sanitized.passportNumber;
    
    return sanitized;
  }

  /**
   * Calculate years of service
   */
  private calculateYearsOfService(dateOfJoining: Date): number {
    const today = new Date();
    const joinDate = new Date(dateOfJoining);
    return Math.floor((today.getTime() - joinDate.getTime()) / (1000 * 60 * 60 * 24 * 365.25));
  }
}
