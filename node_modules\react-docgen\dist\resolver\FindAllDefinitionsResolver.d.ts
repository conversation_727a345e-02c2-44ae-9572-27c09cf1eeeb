import type FileState from '../FileState.js';
import type { ComponentNodePath, ResolverClass } from './index.js';
/**
 * Given an AST, this function tries to find all object expressions that are
 * passed to `React.createClass` calls, by resolving all references properly.
 */
export default class FindAllDefinitionsResolver implements ResolverClass {
    resolve(file: FileState): ComponentNodePath[];
}
