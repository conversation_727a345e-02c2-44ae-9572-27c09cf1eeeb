import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '@app/common/enums/status.enum';
import { PayrollPeriodService } from './services/payroll-period.service';
import { PayrollCalculationService } from './services/payroll-calculation.service';
import { PayrollProcessingService } from './services/payroll-processing.service';
import { TaxCalculationService } from './services/tax-calculation.service';
import {
  CreatePayrollPeriodDto,
  UpdatePayrollPeriodDto,
  PayrollPeriodQueryDto,
} from './dto/create-payroll-period.dto';
import {
  CalculatePayrollDto,
  ProcessPayrollDto,
  PayrollValidationDto,
} from './dto/payroll-calculation.dto';

@ApiTags('Payroll')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('payroll')
export class PayrollController {
  constructor(
    private readonly payrollPeriodService: PayrollPeriodService,
    private readonly payrollCalculationService: PayrollCalculationService,
    private readonly payrollProcessingService: PayrollProcessingService,
    private readonly taxCalculationService: TaxCalculationService,
  ) {}

  // Payroll Period Management
  @Post('periods')
  @Roles(UserRole.HR, UserRole.FINANCE, UserRole.ADMIN)
  @ApiOperation({ summary: 'Create a new payroll period' })
  @ApiResponse({ status: 201, description: 'Payroll period created successfully' })
  async createPayrollPeriod(@Request() req, @Body() createPayrollPeriodDto: CreatePayrollPeriodDto) {
    return this.payrollPeriodService.create(req.user.tenantId, createPayrollPeriodDto);
  }

  @Get('periods')
  @Roles(UserRole.HR, UserRole.FINANCE, UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Get all payroll periods' })
  @ApiResponse({ status: 200, description: 'Payroll periods retrieved successfully' })
  async getPayrollPeriods(@Request() req, @Query() query: PayrollPeriodQueryDto) {
    return this.payrollPeriodService.findAll(req.user.tenantId, query);
  }

  @Get('periods/:id')
  @Roles(UserRole.HR, UserRole.FINANCE, UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Get payroll period by ID' })
  @ApiResponse({ status: 200, description: 'Payroll period retrieved successfully' })
  async getPayrollPeriod(@Request() req, @Param('id') id: string) {
    return this.payrollPeriodService.findOne(req.user.tenantId, id);
  }

  @Put('periods/:id')
  @Roles(UserRole.HR, UserRole.FINANCE, UserRole.ADMIN)
  @ApiOperation({ summary: 'Update payroll period' })
  @ApiResponse({ status: 200, description: 'Payroll period updated successfully' })
  async updatePayrollPeriod(
    @Request() req,
    @Param('id') id: string,
    @Body() updatePayrollPeriodDto: UpdatePayrollPeriodDto,
  ) {
    return this.payrollPeriodService.update(req.user.tenantId, id, updatePayrollPeriodDto);
  }

  @Delete('periods/:id')
  @Roles(UserRole.HR, UserRole.FINANCE, UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete payroll period' })
  @ApiResponse({ status: 204, description: 'Payroll period deleted successfully' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deletePayrollPeriod(@Request() req, @Param('id') id: string) {
    return this.payrollPeriodService.remove(req.user.tenantId, id);
  }

  @Post('periods/:id/lock')
  @Roles(UserRole.FINANCE, UserRole.ADMIN)
  @ApiOperation({ summary: 'Lock payroll period' })
  @ApiResponse({ status: 200, description: 'Payroll period locked successfully' })
  async lockPayrollPeriod(@Request() req, @Param('id') id: string) {
    return this.payrollPeriodService.lock(req.user.tenantId, id);
  }

  @Post('periods/:id/unlock')
  @Roles(UserRole.FINANCE, UserRole.ADMIN)
  @ApiOperation({ summary: 'Unlock payroll period' })
  @ApiResponse({ status: 200, description: 'Payroll period unlocked successfully' })
  async unlockPayrollPeriod(@Request() req, @Param('id') id: string) {
    return this.payrollPeriodService.unlock(req.user.tenantId, id);
  }

  @Get('periods/current')
  @Roles(UserRole.HR, UserRole.FINANCE, UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Get current payroll period' })
  @ApiResponse({ status: 200, description: 'Current payroll period retrieved successfully' })
  async getCurrentPayrollPeriod(@Request() req) {
    return this.payrollPeriodService.getCurrentPeriod(req.user.tenantId);
  }

  @Get('periods/upcoming')
  @Roles(UserRole.HR, UserRole.FINANCE, UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Get upcoming payroll periods' })
  @ApiResponse({ status: 200, description: 'Upcoming payroll periods retrieved successfully' })
  async getUpcomingPayrollPeriods(@Request() req, @Query('limit') limit?: number) {
    return this.payrollPeriodService.getUpcomingPeriods(req.user.tenantId, limit);
  }

  // Payroll Calculation
  @Post('calculate')
  @Roles(UserRole.HR, UserRole.FINANCE, UserRole.ADMIN)
  @ApiOperation({ summary: 'Calculate payroll for a period' })
  @ApiResponse({ status: 200, description: 'Payroll calculated successfully' })
  async calculatePayroll(@Request() req, @Body() calculatePayrollDto: CalculatePayrollDto) {
    return this.payrollCalculationService.calculatePayroll(req.user.tenantId, calculatePayrollDto);
  }

  // Payroll Processing
  @Post('process')
  @Roles(UserRole.FINANCE, UserRole.ADMIN)
  @ApiOperation({ summary: 'Process payroll payments' })
  @ApiResponse({ status: 200, description: 'Payroll processed successfully' })
  async processPayroll(@Request() req, @Body() processPayrollDto: ProcessPayrollDto) {
    return this.payrollProcessingService.processPayroll(req.user.tenantId, processPayrollDto);
  }

  @Post('periods/:id/approve')
  @Roles(UserRole.FINANCE, UserRole.ADMIN)
  @ApiOperation({ summary: 'Approve payroll period' })
  @ApiResponse({ status: 200, description: 'Payroll period approved successfully' })
  async approvePayroll(@Request() req, @Param('id') id: string) {
    return this.payrollProcessingService.approvePayroll(req.user.tenantId, id, req.user.id);
  }

  @Post('periods/:id/reject')
  @Roles(UserRole.FINANCE, UserRole.ADMIN)
  @ApiOperation({ summary: 'Reject payroll period' })
  @ApiResponse({ status: 200, description: 'Payroll period rejected successfully' })
  async rejectPayroll(
    @Request() req,
    @Param('id') id: string,
    @Body('reason') reason: string,
  ) {
    return this.payrollProcessingService.rejectPayroll(req.user.tenantId, id, reason);
  }

  @Post('periods/:id/reverse')
  @Roles(UserRole.FINANCE, UserRole.ADMIN)
  @ApiOperation({ summary: 'Reverse payroll period' })
  @ApiResponse({ status: 200, description: 'Payroll period reversed successfully' })
  async reversePayroll(
    @Request() req,
    @Param('id') id: string,
    @Body('reason') reason: string,
  ) {
    return this.payrollProcessingService.reversePayroll(req.user.tenantId, id, reason);
  }

  // Tax Calculations
  @Post('tax/calculate/:employeeId')
  @Roles(UserRole.HR, UserRole.FINANCE, UserRole.ADMIN)
  @ApiOperation({ summary: 'Calculate taxes for an employee' })
  @ApiResponse({ status: 200, description: 'Tax calculation completed successfully' })
  async calculateEmployeeTaxes(
    @Request() req,
    @Param('employeeId') employeeId: string,
    @Body() taxInput: any,
  ) {
    return this.taxCalculationService.calculateEmployeeTaxes(req.user.tenantId, employeeId, taxInput);
  }

  @Get('tax/estimate/:employeeId')
  @Roles(UserRole.HR, UserRole.FINANCE, UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Get annual tax estimate for employee' })
  @ApiResponse({ status: 200, description: 'Tax estimate retrieved successfully' })
  async getAnnualTaxEstimate(
    @Request() req,
    @Param('employeeId') employeeId: string,
    @Query('salary') salary: number,
  ) {
    return this.taxCalculationService.getEstimatedAnnualTax(req.user.tenantId, employeeId, salary);
  }

  @Get('tax/quarterly/:employeeId')
  @Roles(UserRole.HR, UserRole.FINANCE, UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Get quarterly tax estimate for employee' })
  @ApiResponse({ status: 200, description: 'Quarterly tax estimate retrieved successfully' })
  async getQuarterlyTaxEstimate(
    @Request() req,
    @Param('employeeId') employeeId: string,
    @Query('income') income: number,
  ) {
    return this.taxCalculationService.calculateQuarterlyEstimate(req.user.tenantId, employeeId, income);
  }

  // Employee Self-Service Endpoints
  @Get('my-payslips')
  @Roles(UserRole.EMPLOYEE, UserRole.MANAGER, UserRole.HR, UserRole.FINANCE, UserRole.ADMIN)
  @ApiOperation({ summary: 'Get employee payslips (self-service)' })
  @ApiResponse({ status: 200, description: 'Employee payslips retrieved successfully' })
  async getMyPayslips(@Request() req, @Query() query: any) {
    // For employees, only return their own payslips
    const employeeId = req.user.role === UserRole.EMPLOYEE ? req.user.employeeId : query.employeeId;
    
    if (!employeeId) {
      throw new Error('Employee ID is required');
    }

    // Implementation would go here to get payslips for the employee
    return { message: 'Employee payslips endpoint - implementation needed' };
  }

  @Get('my-payslips/:id')
  @Roles(UserRole.EMPLOYEE, UserRole.MANAGER, UserRole.HR, UserRole.FINANCE, UserRole.ADMIN)
  @ApiOperation({ summary: 'Get specific payslip (self-service)' })
  @ApiResponse({ status: 200, description: 'Payslip retrieved successfully' })
  async getMyPayslip(@Request() req, @Param('id') id: string) {
    // Implementation would verify the payslip belongs to the requesting employee
    return { message: 'Employee payslip detail endpoint - implementation needed' };
  }

  @Post('my-payslips/:id/view')
  @Roles(UserRole.EMPLOYEE, UserRole.MANAGER, UserRole.HR, UserRole.FINANCE, UserRole.ADMIN)
  @ApiOperation({ summary: 'Mark payslip as viewed' })
  @ApiResponse({ status: 200, description: 'Payslip marked as viewed' })
  async markPayslipViewed(@Request() req, @Param('id') id: string) {
    // Implementation would mark the payslip as viewed by the employee
    return { message: 'Mark payslip viewed endpoint - implementation needed' };
  }

  // Reporting Endpoints
  @Get('reports/summary')
  @Roles(UserRole.HR, UserRole.FINANCE, UserRole.ADMIN, UserRole.MANAGER)
  @ApiOperation({ summary: 'Get payroll summary report' })
  @ApiResponse({ status: 200, description: 'Payroll summary retrieved successfully' })
  async getPayrollSummary(@Request() req, @Query() query: any) {
    // Implementation would generate payroll summary reports
    return { message: 'Payroll summary report endpoint - implementation needed' };
  }

  @Get('reports/tax-summary')
  @Roles(UserRole.FINANCE, UserRole.ADMIN)
  @ApiOperation({ summary: 'Get tax summary report' })
  @ApiResponse({ status: 200, description: 'Tax summary retrieved successfully' })
  async getTaxSummary(@Request() req, @Query() query: any) {
    // Implementation would generate tax summary reports
    return { message: 'Tax summary report endpoint - implementation needed' };
  }

  @Get('reports/audit-trail')
  @Roles(UserRole.FINANCE, UserRole.ADMIN)
  @ApiOperation({ summary: 'Get payroll audit trail' })
  @ApiResponse({ status: 200, description: 'Audit trail retrieved successfully' })
  async getAuditTrail(@Request() req, @Query() query: any) {
    // Implementation would generate audit trail reports
    return { message: 'Payroll audit trail endpoint - implementation needed' };
  }
}
