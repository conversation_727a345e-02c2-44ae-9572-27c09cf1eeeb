import { Module, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import { CacheModule } from '@nestjs/cache-manager';
import { ThrottlerModule } from '@nestjs/throttler';
import { ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { TerminusModule } from '@nestjs/terminus';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloGatewayDriver, ApolloGatewayDriverConfig } from '@nestjs/apollo';
import { IntrospectAndCompose } from '@apollo/gateway';
import { HttpModule } from '@nestjs/axios';
import * as redisStore from 'cache-manager-redis-store';

import { DatabaseModule } from '@app/database';
import { CommonModule } from '@app/common';
import { SecurityModule } from '@app/security';

import { AppController } from './app.controller';
import { AppService } from './app.service';

// Gateway services and middleware
import { ServiceDiscoveryService } from './services/service-discovery.service';
import { ApiGatewayService } from './services/api-gateway.service';
import { CircuitBreakerMiddleware } from './middleware/circuit-breaker.middleware';
import { MonitoringService } from './services/monitoring.service';
import { HealthCheckService } from './services/health-check.service';
import { RateLimitingConfigService } from './config/rate-limiting.config';
import { GatewayController } from './controllers/gateway.controller';

// Feature modules
import { HealthModule } from './health/health.module';
import { AuthModule } from './auth/auth.module';
import { EmployeeModule } from './employee/employee.module';
import { PayrollModule } from './payroll/payroll.module';
import { PerformanceModule } from './performance/performance.module';
import { AIModule } from './ai/ai.module';
import { AdminModule } from './admin/admin.module';
import { NotificationModule } from './notification/notification.module';
import { SecurityModule as LocalSecurityModule } from './security/security.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
    }),

    // HTTP module for service communication
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),

    // Database connections
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('DATABASE_HOST'),
        port: configService.get('DATABASE_PORT'),
        username: configService.get('DATABASE_USERNAME'),
        password: configService.get('DATABASE_PASSWORD'),
        database: configService.get('DATABASE_NAME'),
        ssl: configService.get('DATABASE_SSL') === 'true',
        autoLoadEntities: true,
        synchronize: configService.get('DATABASE_SYNCHRONIZE') === 'true',
        logging: configService.get('DATABASE_LOGGING') === 'true',
        retryAttempts: 3,
        retryDelay: 3000,
        maxQueryExecutionTime: 30000,
        extra: {
          connectionLimit: configService.get('CONNECTION_POOL_SIZE', 10),
        },
      }),
      inject: [ConfigService],
    }),

    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        uri: configService.get('MONGODB_URI'),
        retryAttempts: 3,
        retryDelay: 3000,
        connectionFactory: (connection) => {
          connection.plugin(require('mongoose-autopopulate'));
          return connection;
        },
      }),
      inject: [ConfigService],
    }),

    // Cache configuration
    CacheModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        store: redisStore,
        host: configService.get('REDIS_HOST'),
        port: configService.get('REDIS_PORT'),
        password: configService.get('REDIS_PASSWORD'),
        db: configService.get('REDIS_DB', 0),
        ttl: configService.get('REDIS_TTL', 3600),
        max: 1000,
      }),
      inject: [ConfigService],
      isGlobal: true,
    }),

    // Rate limiting
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useClass: RateLimitingConfigService,
    }),

    // GraphQL Federation Gateway
    GraphQLModule.forRootAsync<ApolloGatewayDriverConfig>({
      driver: ApolloGatewayDriver,
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const { createGraphQLFederationConfig } = await import('./config/graphql-federation.config');
        return createGraphQLFederationConfig(configService);
      },
      inject: [ConfigService],
    }),

    // Scheduling
    ScheduleModule.forRoot(),

    // Event emitter
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),

    // Health checks
    TerminusModule,

    // Shared modules
    DatabaseModule,
    CommonModule,
    SecurityModule,

    // Feature modules
    HealthModule,
    AuthModule,
    EmployeeModule,
    PayrollModule,
    PerformanceModule,
    AIModule,
    AdminModule,
    NotificationModule,
    LocalSecurityModule,
  ],
  controllers: [AppController, GatewayController],
  providers: [
    AppService,
    ServiceDiscoveryService,
    ApiGatewayService,
    CircuitBreakerMiddleware,
    MonitoringService,
    HealthCheckService,
    RateLimitingConfigService,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(CircuitBreakerMiddleware)
      .forRoutes('*');
  }
}
