import {
  Entity,
  Column,
  Index,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BeforeI<PERSON>rt,
  BeforeUpdate,
} from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { Payslip } from './payslip.entity';
import { Employee } from './employee.entity';
import { PayrollItemType, Currency } from '@app/common/enums/status.enum';

@Entity('payroll_items')
@Index(['tenantId', 'payslipId', 'type'])
@Index(['tenantId', 'employeeId', 'type'])
@Index(['tenantId', 'code'])
export class PayrollItem extends TenantAwareEntity {
  @Column({
    type: 'uuid',
    comment: 'Payslip ID',
  })
  payslipId: string;

  @Column({
    type: 'uuid',
    comment: 'Employee ID',
  })
  employeeId: string;

  @Column({
    type: 'varchar',
    length: 50,
    comment: 'Payroll item code',
  })
  @Index()
  code: string;

  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Payroll item name/description',
  })
  name: string;

  @Column({
    type: 'enum',
    enum: PayrollItemType,
    comment: 'Type of payroll item',
  })
  @Index()
  type: PayrollItemType;

  @Column({
    type: 'varchar',
    length: 50,
    default: 'earnings',
    comment: 'Category: earnings, deductions, taxes, benefits',
  })
  category: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    comment: 'Item amount',
  })
  amount: number;

  @Column({
    type: 'enum',
    enum: Currency,
    default: Currency.USD,
    comment: 'Item currency',
  })
  currency: Currency;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 4,
    default: 1.0,
    comment: 'Exchange rate to base currency',
  })
  exchangeRate: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Base amount in tenant currency',
  })
  baseAmount?: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
    nullable: true,
    comment: 'Rate (for hourly calculations)',
  })
  rate?: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 2,
    nullable: true,
    comment: 'Units/hours (for rate-based calculations)',
  })
  units?: number;

  @Column({
    type: 'decimal',
    precision: 8,
    scale: 4,
    nullable: true,
    comment: 'Percentage (for percentage-based calculations)',
  })
  percentage?: number;

  @Column({
    type: 'varchar',
    length: 50,
    default: 'fixed',
    comment: 'Calculation method: fixed, rate, percentage, formula',
  })
  calculationMethod: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Calculation formula or expression',
  })
  formula?: string;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this item is taxable',
  })
  isTaxable: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this is a pre-tax deduction',
  })
  isPreTax: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this item affects overtime calculations',
  })
  affectsOvertime: boolean;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this item is included in gross pay',
  })
  includeInGross: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this is a recurring item',
  })
  isRecurring: boolean;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Display order',
  })
  sortOrder: number;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Reference to external system',
  })
  externalReference?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'GL account code for accounting integration',
  })
  glAccountCode?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Cost center code',
  })
  costCenter?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Department code',
  })
  departmentCode?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Tax configuration for this item',
  })
  taxConfiguration?: {
    federalTaxable?: boolean;
    stateTaxable?: boolean;
    localTaxable?: boolean;
    socialSecurityTaxable?: boolean;
    medicareTaxable?: boolean;
    unemploymentTaxable?: boolean;
    workersCompTaxable?: boolean;
    exemptions?: string[];
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Calculation details and audit trail',
  })
  calculationDetails?: {
    originalAmount?: number;
    adjustments?: Array<{
      type: string;
      amount: number;
      reason: string;
      appliedBy: string;
      appliedAt: Date;
    }>;
    rounding?: {
      originalAmount: number;
      roundedAmount: number;
      method: string;
    };
    dependencies?: string[]; // Other payroll item codes this depends on
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Year-to-date totals for this item',
  })
  ytdTotals?: {
    amount: number;
    units: number;
    count: number; // Number of times this item appeared
  };

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Effective start date for this item',
  })
  effectiveStartDate?: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Effective end date for this item',
  })
  effectiveEndDate?: Date;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this item is active',
  })
  isActive: boolean;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this is a manual adjustment',
  })
  isManualAdjustment: boolean;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'User who made manual adjustment',
  })
  adjustedBy?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Reason for manual adjustment',
  })
  adjustmentReason?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Additional notes or comments',
  })
  notes?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Payroll item metadata',
  })
  metadata?: Record<string, any>;

  // Relationships
  @ManyToOne(() => Payslip, payslip => payslip.payrollItems, { eager: false })
  @JoinColumn({ name: 'payslip_id' })
  payslip: Payslip;

  @ManyToOne(() => Employee, { eager: false })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;

  // Virtual properties
  get isEarning(): boolean {
    return [
      PayrollItemType.SALARY,
      PayrollItemType.HOURLY,
      PayrollItemType.OVERTIME,
      PayrollItemType.BONUS,
      PayrollItemType.COMMISSION,
      PayrollItemType.ALLOWANCE,
      PayrollItemType.REIMBURSEMENT,
    ].includes(this.type);
  }

  get isDeduction(): boolean {
    return this.type === PayrollItemType.DEDUCTION;
  }

  get isTax(): boolean {
    return this.type === PayrollItemType.TAX;
  }

  get isBenefit(): boolean {
    return this.type === PayrollItemType.BENEFIT;
  }

  get isContribution(): boolean {
    return this.type === PayrollItemType.CONTRIBUTION;
  }

  get effectiveAmount(): number {
    return this.baseAmount || this.amount;
  }

  get isCurrentlyEffective(): boolean {
    const now = new Date();
    const startOk = !this.effectiveStartDate || this.effectiveStartDate <= now;
    const endOk = !this.effectiveEndDate || this.effectiveEndDate >= now;
    return startOk && endOk && this.isActive;
  }

  // Methods
  calculateAmount(): number {
    switch (this.calculationMethod) {
      case 'fixed':
        return this.amount;
      case 'rate':
        return (this.rate || 0) * (this.units || 0);
      case 'percentage':
        return (this.amount * (this.percentage || 0)) / 100;
      case 'formula':
        // Formula calculation would be implemented here
        return this.amount;
      default:
        return this.amount;
    }
  }

  applyExchangeRate(): void {
    if (this.exchangeRate !== 1.0) {
      this.baseAmount = this.amount * this.exchangeRate;
    }
  }

  addAdjustment(type: string, amount: number, reason: string, adjustedBy: string): void {
    if (!this.calculationDetails) {
      this.calculationDetails = {};
    }
    if (!this.calculationDetails.adjustments) {
      this.calculationDetails.adjustments = [];
    }
    
    this.calculationDetails.adjustments.push({
      type,
      amount,
      reason,
      appliedBy: adjustedBy,
      appliedAt: new Date(),
    });
    
    this.amount += amount;
    this.isManualAdjustment = true;
    this.adjustedBy = adjustedBy;
    this.adjustmentReason = reason;
  }

  @BeforeInsert()
  @BeforeUpdate()
  validateAndCalculate(): void {
    // Apply exchange rate if needed
    this.applyExchangeRate();
    
    // Validate effective dates
    if (this.effectiveStartDate && this.effectiveEndDate) {
      if (this.effectiveStartDate > this.effectiveEndDate) {
        throw new Error('Effective start date must be before end date');
      }
    }
    
    // Ensure amount is calculated correctly
    if (this.calculationMethod !== 'fixed') {
      this.amount = this.calculateAmount();
    }
  }
}
