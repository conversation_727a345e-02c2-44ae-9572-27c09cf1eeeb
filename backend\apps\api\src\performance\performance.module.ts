import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';

// Entities
import { PerformanceReview } from '@app/database/entities/performance-review.entity';
import { Goal } from '@app/database/entities/goal.entity';
import { Feedback } from '@app/database/entities/feedback.entity';
import { PerformanceMetric } from '@app/database/entities/performance-metric.entity';
import { Employee } from '@app/database/entities/employee.entity';
import { User } from '@app/database/entities/user.entity';
import { Tenant } from '@app/database/entities/tenant.entity';
import { Department } from '@app/database/entities/department.entity';
import { Notification } from '@app/database/entities/notification.entity';

// Controllers
import { PerformanceReviewController } from './controllers/performance-review.controller';
import { GoalController } from './controllers/goal.controller';
import { FeedbackController } from './controllers/feedback.controller';
import { PerformanceMetricController } from './controllers/performance-metric.controller';
import { PerformanceDashboardController } from './controllers/performance-dashboard.controller';

// Services
import { PerformanceReviewService } from './services/performance-review.service';
import { GoalService } from './services/goal.service';
import { FeedbackService } from './services/feedback.service';
import { PerformanceMetricService } from './services/performance-metric.service';
import { PerformanceAnalyticsService } from './services/performance-analytics.service';
import { PerformanceNotificationService } from './services/performance-notification.service';
import { PerformanceReportService } from './services/performance-report.service';

// External modules
import { TenantModule } from '@app/common/tenant/tenant.module';
import { AuthModule } from '@app/security/auth.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      // Performance entities
      PerformanceReview,
      Goal,
      Feedback,
      PerformanceMetric,
      
      // Related entities
      Employee,
      User,
      Tenant,
      Department,
      Notification,
    ]),
    EventEmitterModule,
    TenantModule,
    AuthModule,
  ],
  controllers: [
    PerformanceReviewController,
    GoalController,
    FeedbackController,
    PerformanceMetricController,
    PerformanceDashboardController,
  ],
  providers: [
    PerformanceReviewService,
    GoalService,
    FeedbackService,
    PerformanceMetricService,
    PerformanceAnalyticsService,
    PerformanceNotificationService,
    PerformanceReportService,
  ],
  exports: [
    PerformanceReviewService,
    GoalService,
    FeedbackService,
    PerformanceMetricService,
    PerformanceAnalyticsService,
    PerformanceNotificationService,
    PerformanceReportService,
  ],
})
export class PerformanceModule {}
