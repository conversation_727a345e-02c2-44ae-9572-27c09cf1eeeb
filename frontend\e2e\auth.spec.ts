import { test, expect } from '@playwright/test';

test.describe('Authentication', () => {
  test.beforeEach(async ({ page }) => {
    // Start from the login page
    await page.goto('/login');
  });

  test('should display login form', async ({ page }) => {
    // Check that login form elements are present
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    
    // Check for login form title or heading
    await expect(page.locator('h1, h2').filter({ hasText: /login|sign in/i })).toBeVisible();
  });

  test('should show validation errors for empty fields', async ({ page }) => {
    // Click submit without filling fields
    await page.click('button[type="submit"]');
    
    // Check for validation error messages
    await expect(page.locator('text=Email is required')).toBeVisible();
    await expect(page.locator('text=Password is required')).toBeVisible();
  });

  test('should show error for invalid credentials', async ({ page }) => {
    // Fill in invalid credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'wrongpassword');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Check for error message
    await expect(page.locator('text=Invalid credentials')).toBeVisible();
  });

  test('should login successfully with valid credentials', async ({ page }) => {
    // Fill in valid credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Password1234');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard');
    
    // Check for dashboard elements
    await expect(page.locator('text=Dashboard')).toBeVisible();
    await expect(page.locator('text=Welcome')).toBeVisible();
  });

  test('should remember login state after page refresh', async ({ page }) => {
    // Login first
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Password1234');
    await page.click('button[type="submit"]');
    
    // Wait for redirect
    await expect(page).toHaveURL('/dashboard');
    
    // Refresh the page
    await page.reload();
    
    // Should still be on dashboard
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('text=Dashboard')).toBeVisible();
  });

  test('should logout successfully', async ({ page }) => {
    // Login first
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Password1234');
    await page.click('button[type="submit"]');
    
    // Wait for redirect
    await expect(page).toHaveURL('/dashboard');
    
    // Find and click logout button (could be in a dropdown or menu)
    const logoutButton = page.locator('button:has-text("Logout"), a:has-text("Logout"), [data-testid="logout"]');
    await logoutButton.click();
    
    // Should redirect to login page
    await expect(page).toHaveURL('/login');
    await expect(page.locator('input[type="email"]')).toBeVisible();
  });

  test('should redirect unauthenticated users to login', async ({ page }) => {
    // Try to access protected route directly
    await page.goto('/employees');
    
    // Should redirect to login
    await expect(page).toHaveURL('/login');
  });

  test('should handle password visibility toggle', async ({ page }) => {
    const passwordInput = page.locator('input[type="password"]');
    const toggleButton = page.locator('[data-testid="password-toggle"], button:near(input[type="password"])');
    
    // Password should be hidden initially
    await expect(passwordInput).toHaveAttribute('type', 'password');
    
    // Click toggle to show password
    if (await toggleButton.isVisible()) {
      await toggleButton.click();
      await expect(passwordInput).toHaveAttribute('type', 'text');
      
      // Click again to hide password
      await toggleButton.click();
      await expect(passwordInput).toHaveAttribute('type', 'password');
    }
  });

  test('should handle remember me functionality', async ({ page }) => {
    const rememberMeCheckbox = page.locator('input[type="checkbox"]:near(text="Remember me")');
    
    if (await rememberMeCheckbox.isVisible()) {
      // Check remember me
      await rememberMeCheckbox.check();
      await expect(rememberMeCheckbox).toBeChecked();
      
      // Login with remember me checked
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'Password1234');
      await page.click('button[type="submit"]');
      
      // Should redirect to dashboard
      await expect(page).toHaveURL('/dashboard');
    }
  });

  test('should show loading state during login', async ({ page }) => {
    // Fill credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Password1234');
    
    // Submit form and check for loading state
    await page.click('button[type="submit"]');
    
    // Check for loading indicator (spinner, disabled button, etc.)
    const loadingIndicator = page.locator(
      'button[disabled]:has-text("Logging in"), ' +
      'button[disabled]:has-text("Loading"), ' +
      '[data-testid="loading-spinner"], ' +
      '.spinner, .loading'
    );
    
    // Loading state might be brief, so use a short timeout
    await expect(loadingIndicator.first()).toBeVisible({ timeout: 2000 }).catch(() => {
      // Loading state might be too fast to catch, which is okay
    });
  });

  test('should handle keyboard navigation', async ({ page }) => {
    // Tab through form elements
    await page.keyboard.press('Tab');
    await expect(page.locator('input[type="email"]')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.locator('input[type="password"]')).toBeFocused();
    
    await page.keyboard.press('Tab');
    await expect(page.locator('button[type="submit"]')).toBeFocused();
    
    // Submit with Enter key
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Password1234');
    await page.keyboard.press('Enter');
    
    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard');
  });

  test('should handle session timeout', async ({ page }) => {
    // Login first
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Password1234');
    await page.click('button[type="submit"]');
    
    await expect(page).toHaveURL('/dashboard');
    
    // Simulate session timeout by clearing storage
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
    
    // Try to navigate to a protected route
    await page.goto('/employees');
    
    // Should redirect to login due to expired session
    await expect(page).toHaveURL('/login');
  });

  test('should display appropriate error messages', async ({ page }) => {
    // Test various error scenarios
    const testCases = [
      {
        email: 'invalid-email',
        password: 'password',
        expectedError: 'Invalid email format'
      },
      {
        email: '<EMAIL>',
        password: '123',
        expectedError: 'Password must be at least'
      }
    ];

    for (const testCase of testCases) {
      await page.fill('input[type="email"]', testCase.email);
      await page.fill('input[type="password"]', testCase.password);
      await page.click('button[type="submit"]');
      
      // Check for specific error message
      await expect(page.locator(`text=${testCase.expectedError}`)).toBeVisible();
      
      // Clear fields for next test
      await page.fill('input[type="email"]', '');
      await page.fill('input[type="password"]', '');
    }
  });
});
