import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  <PERSON>inColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';
import { JobPosting } from './job-posting.entity';
import { Candidate } from './candidate.entity';
import { Interview } from './interview.entity';

export enum ApplicationStatus {
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  SCREENING = 'screening',
  INTERVIEWING = 'interviewing',
  REFERENCE_CHECK = 'reference_check',
  OFFER_PENDING = 'offer_pending',
  OFFER_EXTENDED = 'offer_extended',
  OFFER_ACCEPTED = 'offer_accepted',
  OFFER_DECLINED = 'offer_declined',
  HIRED = 'hired',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn',
}

@Entity('applications')
@Index(['tenantId', 'candidateId'])
@Index(['tenantId', 'jobPostingId'])
@Index(['tenantId', 'status'])
export class Application {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ name: 'candidate_id' })
  candidateId: string;

  @ManyToOne(() => Candidate, candidate => candidate.applications, { eager: true })
  @JoinColumn({ name: 'candidate_id' })
  candidate: Candidate;

  @Column({ name: 'job_posting_id' })
  jobPostingId: string;

  @ManyToOne(() => JobPosting, jobPosting => jobPosting.applications, { eager: true })
  @JoinColumn({ name: 'job_posting_id' })
  jobPosting: JobPosting;

  @Column({
    type: 'enum',
    enum: ApplicationStatus,
    default: ApplicationStatus.SUBMITTED,
  })
  status: ApplicationStatus;

  @Column({ name: 'applied_date', type: 'timestamp' })
  appliedDate: Date;

  @Column({ type: 'text', nullable: true, name: 'cover_letter' })
  coverLetter: string;

  @Column({ type: 'json', nullable: true, name: 'custom_responses' })
  customResponses: Array<{
    questionId: string;
    question: string;
    answer: string;
  }>;

  @Column({ type: 'json', nullable: true, name: 'screening_answers' })
  screeningAnswers: Array<{
    questionId: string;
    question: string;
    answer: string;
    score?: number;
  }>;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true, name: 'screening_score' })
  screeningScore: number;

  @Column({ type: 'json', nullable: true, name: 'assessment_results' })
  assessmentResults: Array<{
    assessmentId: string;
    assessmentName: string;
    score: number;
    maxScore: number;
    completedAt: string;
  }>;

  @Column({ type: 'json', nullable: true, name: 'interview_feedback' })
  interviewFeedback: Array<{
    interviewId: string;
    interviewerName: string;
    rating: number;
    feedback: string;
    recommendation: string;
  }>;

  @Column({ type: 'json', nullable: true, name: 'reference_checks' })
  referenceChecks: Array<{
    referenceName: string;
    referenceTitle: string;
    referenceCompany: string;
    referencePhone: string;
    referenceEmail: string;
    status: string;
    feedback?: string;
    rating?: number;
  }>;

  @Column({ type: 'text', nullable: true, name: 'rejection_reason' })
  rejectionReason: string;

  @Column({ name: 'rejected_at', type: 'timestamp', nullable: true })
  rejectedAt: Date;

  @Column({ name: 'rejected_by', nullable: true })
  rejectedBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'rejected_by' })
  rejector: User;

  @Column({ type: 'json', nullable: true, name: 'offer_details' })
  offerDetails: {
    salary?: number;
    currency?: string;
    benefits?: string[];
    startDate?: string;
    offerExpiryDate?: string;
    negotiable?: boolean;
  };

  @Column({ name: 'offer_extended_at', type: 'timestamp', nullable: true })
  offerExtendedAt: Date;

  @Column({ name: 'offer_response_date', type: 'timestamp', nullable: true })
  offerResponseDate: Date;

  @Column({ type: 'text', nullable: true, name: 'offer_notes' })
  offerNotes: string;

  @Column({ type: 'json', nullable: true, name: 'ai_evaluation' })
  aiEvaluation: {
    overallScore?: number;
    skillsMatch?: number;
    experienceMatch?: number;
    culturalFit?: number;
    strengths?: string[];
    concerns?: string[];
    recommendation?: string;
    confidence?: number;
  };

  @OneToMany(() => Interview, interview => interview.application)
  interviews: Interview[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  // Computed properties
  get averageInterviewRating(): number {
    if (!this.interviewFeedback || this.interviewFeedback.length === 0) return 0;
    const totalRating = this.interviewFeedback.reduce((sum, feedback) => sum + feedback.rating, 0);
    return totalRating / this.interviewFeedback.length;
  }

  get daysSinceApplication(): number {
    const today = new Date();
    const timeDiff = today.getTime() - this.appliedDate.getTime();
    return Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  }

  get isOfferPending(): boolean {
    return [
      ApplicationStatus.OFFER_PENDING,
      ApplicationStatus.OFFER_EXTENDED
    ].includes(this.status);
  }
}
