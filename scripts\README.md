# PeopleNest HRMS - Development Automation Scripts

This directory contains automation scripts to easily manage the PeopleNest HRMS development environment.

## 🚀 Quick Start

### Windows (PowerShell)
```powershell
# Start all services (infrastructure + applications)
npm run start

# Check service status
npm run status

# Stop all services
npm run stop
```

### Linux/macOS (Bash)
```bash
# Make scripts executable (first time only)
chmod +x scripts/*.sh

# Start all services
./scripts/start-dev.sh

# Check service status
./scripts/status.sh

# Stop all services
./scripts/stop-dev.sh
```

## 🔐 JWT Secret Generation

### Overview
The JWT secret generation scripts automatically create cryptographically secure secrets for JWT token signing and refresh token signing. These secrets are essential for authentication security.

### Features
- **Cryptographically Secure**: Uses Node.js `crypto.randomBytes()` and PowerShell `RNGCryptoServiceProvider`
- **High Entropy**: Generates 512-bit secrets (64 bytes) for maximum security
- **Automatic Backup**: Creates backup files before updating environment files
- **Git Safety**: Automatically adds `.secrets/` to `.gitignore`
- **Cross-Platform**: Supports both Node.js and PowerShell execution

### Usage
```bash
# Generate JWT secrets using Node.js (Recommended)
npm run generate:jwt-secrets

# Generate JWT secrets using PowerShell (Windows)
npm run generate:jwt-secrets:ps

# Direct execution
node scripts/generate-jwt-secrets.js
powershell -ExecutionPolicy Bypass -File scripts/generate-jwt-secrets.ps1
```

### What It Does
1. **Generates Secure Secrets**: `JWT_SECRET` and `JWT_REFRESH_SECRET` with 512-bit entropy
2. **Updates Environment Files**: Updates `.env` and other environment files if they exist
3. **Creates Backups**: Backup files with timestamp for easy rollback
4. **Saves Reference**: Creates `.secrets/jwt-secrets-{timestamp}.json` with metadata
5. **Git Safety**: Adds `.secrets/` to `.gitignore` to prevent accidental commits

### Security Notes
⚠️ **Important**:
- Changing JWT secrets will invalidate all existing tokens
- Never commit the new secrets to version control
- Update production environment variables separately
- Store backup files securely

## 📋 Available Scripts

### Start Scripts

#### PowerShell (Windows)
- `scripts/start-dev.ps1` - Main startup script
- `npm run start` - Start all services
- `npm run start:backend` - Start only backend service
- `npm run start:frontend` - Start only frontend service  
- `npm run start:ai` - Start only AI services

#### Bash (Linux/macOS)
- `scripts/start-dev.sh` - Main startup script
- `./scripts/start-dev.sh --backend-only` - Start only backend
- `./scripts/start-dev.sh --frontend-only` - Start only frontend
- `./scripts/start-dev.sh --ai-only` - Start only AI services
- `./scripts/start-dev.sh --skip-infrastructure` - Skip Docker infrastructure

### Stop Scripts

#### PowerShell (Windows)
- `scripts/stop-dev.ps1` - Main stop script
- `npm run stop` - Stop all services gracefully
- `npm run stop:force` - Force stop all services
- `npm run stop:infra` - Stop only infrastructure
- `npm run stop:apps` - Stop only applications

#### Bash (Linux/macOS)
- `scripts/stop-dev.sh` - Main stop script
- `./scripts/stop-dev.sh --force` - Force stop all services
- `./scripts/stop-dev.sh --infrastructure-only` - Stop only infrastructure
- `./scripts/stop-dev.sh --application-only` - Stop only applications

### Status Scripts

#### PowerShell (Windows)
- `scripts/status.ps1` - Service status checker
- `npm run status` - Basic status check
- `npm run status:detailed` - Detailed status with processes
- `npm run status:watch` - Continuous status monitoring
- `npm run status:json` - JSON output for automation

## 🔧 Script Features

### Start Script Features
- ✅ **Health Checks** - Verifies all services are running properly
- ✅ **Dependency Installation** - Automatically installs missing dependencies
- ✅ **Service Orchestration** - Starts services in correct order
- ✅ **Cross-Platform** - Works on Windows, Linux, and macOS
- ✅ **Flexible Options** - Start individual services or combinations
- ✅ **Error Handling** - Graceful error handling and reporting
- ✅ **Colored Output** - Easy-to-read colored terminal output

### Stop Script Features
- ✅ **Graceful Shutdown** - Properly stops services without data loss
- ✅ **Force Kill Option** - Force stop unresponsive services
- ✅ **Selective Stopping** - Stop infrastructure or applications separately
- ✅ **Process Cleanup** - Finds and stops related processes
- ✅ **Docker Management** - Properly manages Docker containers
- ✅ **Cleanup Options** - Remove volumes and orphaned containers

### Status Script Features
- ✅ **Real-time Monitoring** - Live status updates
- ✅ **Health Verification** - HTTP and TCP health checks
- ✅ **Process Tracking** - Shows running application processes
- ✅ **Docker Integration** - Displays container status
- ✅ **JSON Output** - Machine-readable output for automation
- ✅ **Watch Mode** - Continuous monitoring with auto-refresh

## 🌐 Service URLs

After starting the services, you can access:

| Service | URL | Description |
|---------|-----|-------------|
| **Frontend** | http://localhost:3000 | React application |
| **Backend API** | http://localhost:4000 | NestJS GraphQL API |
| **GraphQL Playground** | http://localhost:4000/graphql | API explorer |
| **AI Services** | http://localhost:8000 | FastAPI AI services |
| **AI API Docs** | http://localhost:8000/docs | AI API documentation |
| **Grafana** | http://localhost:3001 | Monitoring dashboards |
| **Kibana** | http://localhost:5601 | Log analytics |
| **Elasticsearch** | http://localhost:9200 | Search engine |
| **MinIO Console** | http://localhost:9001 | Object storage |
| **Prometheus** | http://localhost:9090 | Metrics collection |
| **Jaeger** | http://localhost:16686 | Distributed tracing |

## 🔑 Default Credentials

### Admin User
- **Username:** `awadhesh`
- **Password:** `awadhesh123`
- **Email:** `<EMAIL>`
- **Role:** Super Admin

### Infrastructure Services
- **PostgreSQL:** `postgres` / `postgres123`
- **MinIO:** `minioadmin` / `minioadmin`
- **Grafana:** `admin` / `admin`

## 🛠️ Troubleshooting

### Common Issues

#### Docker Not Running
```bash
# Windows
# Start Docker Desktop from Start Menu

# Linux
sudo systemctl start docker

# macOS
# Start Docker Desktop from Applications
```

#### Port Conflicts
If you get port conflicts, check what's using the ports:
```powershell
# Windows
netstat -ano | findstr :3000
netstat -ano | findstr :4000

# Linux/macOS
lsof -i :3000
lsof -i :4000
```

#### Permission Issues (Linux/macOS)
```bash
# Make scripts executable
chmod +x scripts/*.sh

# Fix Docker permissions
sudo usermod -aG docker $USER
# Then logout and login again
```

#### Services Not Starting
1. Check Docker is running: `docker version`
2. Check available disk space: `df -h`
3. Check available memory: `free -h` (Linux) or Activity Monitor (macOS)
4. View detailed logs: `npm run status:detailed`

### Reset Everything
If you need to completely reset the environment:

```powershell
# Windows
npm run stop:force
docker system prune -a --volumes
npm run start

# Linux/macOS
./scripts/stop-dev.sh --force
docker system prune -a --volumes
./scripts/start-dev.sh
```

## 📝 Script Parameters

### Start Script Parameters

#### PowerShell
```powershell
scripts/start-dev.ps1 [OPTIONS]

Options:
  -SkipInfrastructure    Skip starting Docker infrastructure
  -BackendOnly          Start only backend service
  -FrontendOnly         Start only frontend service
  -AIOnly               Start only AI services
  -Verbose              Enable verbose output
```

#### Bash
```bash
scripts/start-dev.sh [OPTIONS]

Options:
  --skip-infrastructure  Skip starting Docker infrastructure
  --backend-only         Start only backend service
  --frontend-only        Start only frontend service
  --ai-only              Start only AI services
  --verbose              Enable verbose output
  --help                 Show help message
```

### Stop Script Parameters

#### PowerShell
```powershell
scripts/stop-dev.ps1 [OPTIONS]

Options:
  -InfrastructureOnly   Stop only Docker infrastructure
  -ApplicationOnly      Stop only application services
  -Force               Force kill processes and cleanup
  -Verbose             Show detailed process information
```

#### Bash
```bash
scripts/stop-dev.sh [OPTIONS]

Options:
  --infrastructure-only  Stop only Docker infrastructure
  --application-only     Stop only application services
  --force               Force kill processes and cleanup
  --verbose             Show detailed process information
  --help                Show help message
```

## 🔄 Development Workflow

### Typical Development Session
```bash
# 1. Start everything
npm run start

# 2. Check status
npm run status

# 3. Develop your features...

# 4. Stop when done
npm run stop
```

### Working on Specific Services
```bash
# Backend development
npm run start:backend
# Make changes to backend code...
npm run stop:apps

# Frontend development  
npm run start:frontend
# Make changes to frontend code...
npm run stop:apps

# AI services development
npm run start:ai
# Make changes to AI code...
npm run stop:apps
```

## 📊 Monitoring

### Continuous Monitoring
```bash
# Watch service status
npm run status:watch

# View logs
docker-compose logs -f

# Monitor specific service
docker logs -f peoplenest-postgres
```

### Health Checks
The scripts perform automatic health checks for:
- HTTP services (200 OK response)
- Database services (TCP connection)
- Process availability
- Docker container status

## 🚀 Production Deployment

These scripts are designed for development. For production deployment:
1. Use proper orchestration (Kubernetes, Docker Swarm)
2. Implement proper secrets management
3. Configure production-grade monitoring
4. Set up proper backup strategies
5. Implement CI/CD pipelines

## 📞 Support

If you encounter issues with the automation scripts:
1. Check the troubleshooting section above
2. Run with verbose output: `npm run status:detailed`
3. Check Docker logs: `docker-compose logs`
4. Review the script source code in this directory
