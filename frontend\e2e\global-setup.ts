import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup...');

  // Launch browser for setup
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Wait for the application to be ready
    console.log('⏳ Waiting for application to be ready...');
    await page.goto(config.projects[0].use?.baseURL || 'http://localhost:3000');
    
    // Wait for the app to load
    await page.waitForSelector('body', { timeout: 30000 });
    
    // Check if backend is healthy
    console.log('🔍 Checking backend health...');
    const healthResponse = await page.request.get('http://localhost:3001/health');
    if (!healthResponse.ok()) {
      throw new Error('Backend health check failed');
    }

    // Setup test data if needed
    await setupTestData(page);

    console.log('✅ Global setup completed successfully');
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

async function setupTestData(page: any) {
  console.log('📊 Setting up test data...');

  try {
    // Create test tenant if not exists
    const createTenantResponse = await page.request.post('http://localhost:3001/api/tenants', {
      data: {
        name: 'Test Company',
        domain: 'test.com',
        settings: {
          timezone: 'UTC',
          currency: 'USD',
          dateFormat: 'YYYY-MM-DD',
        },
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Create test admin user if not exists
    const createUserResponse = await page.request.post('http://localhost:3001/api/auth/register', {
      data: {
        email: '<EMAIL>',
        password: 'Password1234',
        firstName: 'Test',
        lastName: 'Admin',
        role: 'ADMIN',
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Create test employee user
    await page.request.post('http://localhost:3001/api/auth/register', {
      data: {
        email: '<EMAIL>',
        password: 'Password1234',
        firstName: 'Test',
        lastName: 'Employee',
        role: 'EMPLOYEE',
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Create test HR manager user
    await page.request.post('http://localhost:3001/api/auth/register', {
      data: {
        email: '<EMAIL>',
        password: 'Password1234',
        firstName: 'Test',
        lastName: 'HR',
        role: 'HR_MANAGER',
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('✅ Test data setup completed');
  } catch (error) {
    console.warn('⚠️ Test data setup failed (may already exist):', error.message);
    // Don't throw error as test data might already exist
  }
}

export default globalSetup;
