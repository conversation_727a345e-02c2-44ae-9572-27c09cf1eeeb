apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: peoplenest
  labels:
    app.kubernetes.io/name: backend
    app.kubernetes.io/component: api
    app.kubernetes.io/part-of: peoplenest-hrms
    app.kubernetes.io/version: "1.0.0"
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app.kubernetes.io/name: backend
  template:
    metadata:
      labels:
        app.kubernetes.io/name: backend
        app.kubernetes.io/component: api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3001"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: peoplenest-backend
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
      containers:
      - name: backend
        image: peoplenest/backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3001
          name: http
          protocol: TCP
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: peoplenest-config
              key: NODE_ENV
        - name: DATABASE_HOST
          valueFrom:
            configMapKeyRef:
              name: peoplenest-config
              key: DATABASE_HOST
        - name: DATABASE_PORT
          valueFrom:
            configMapKeyRef:
              name: peoplenest-config
              key: DATABASE_PORT
        - name: DATABASE_NAME
          valueFrom:
            configMapKeyRef:
              name: peoplenest-config
              key: DATABASE_NAME
        - name: DATABASE_USERNAME
          valueFrom:
            secretKeyRef:
              name: peoplenest-secrets
              key: DATABASE_USERNAME
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: peoplenest-secrets
              key: DATABASE_PASSWORD
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: peoplenest-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: peoplenest-config
              key: REDIS_PORT
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: peoplenest-secrets
              key: REDIS_PASSWORD
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: peoplenest-secrets
              key: JWT_SECRET
        - name: JWT_REFRESH_SECRET
          valueFrom:
            secretKeyRef:
              name: peoplenest-secrets
              key: JWT_REFRESH_SECRET
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: peoplenest-secrets
              key: ENCRYPTION_KEY
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: peoplenest-secrets
              key: MONGODB_URI
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        volumeMounts:
        - name: uploads
          mountPath: /app/uploads
        - name: logs
          mountPath: /app/logs
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: uploads
        persistentVolumeClaim:
          claimName: backend-uploads-pvc
      - name: logs
        emptyDir: {}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app.kubernetes.io/name
                  operator: In
                  values:
                  - backend
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: peoplenest
  labels:
    app.kubernetes.io/name: backend
    app.kubernetes.io/component: api
spec:
  type: ClusterIP
  ports:
  - port: 3001
    targetPort: http
    protocol: TCP
    name: http
  selector:
    app.kubernetes.io/name: backend
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: peoplenest-backend
  namespace: peoplenest
  labels:
    app.kubernetes.io/name: backend
    app.kubernetes.io/component: serviceaccount
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backend-uploads-pvc
  namespace: peoplenest
  labels:
    app.kubernetes.io/name: backend
    app.kubernetes.io/component: storage
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd
