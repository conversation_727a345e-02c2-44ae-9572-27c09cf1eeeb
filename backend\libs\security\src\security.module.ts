import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { EventEmitterModule } from '@nestjs/event-emitter';

// Entities
import { User } from '@app/database/entities/user.entity';
import { RefreshToken } from '@app/database/entities/refresh-token.entity';
import { Session } from '@app/database/entities/session.entity';
import { AuditLog } from '@app/database/entities/audit-log.entity';
import { Permission } from '@app/database/entities/permission.entity';
import { UserPermission } from '@app/database/entities/user-permission.entity';
import { MfaBackupCode } from '@app/database/entities/mfa-backup-code.entity';
import { Tenant } from '@app/database/entities/tenant.entity';
import { Employee } from '@app/database/entities/employee.entity';

// Strategies
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { RefreshTokenStrategy } from './strategies/refresh-token.strategy';

// Guards
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RolesGuard } from './guards/roles.guard';
import { MfaGuard } from './guards/mfa.guard';

// Services
import { AuthService } from './services/auth.service';
import { TokenService } from './services/token.service';
import { PasswordService } from './services/password.service';
import { MfaService } from './services/mfa.service';
import { PermissionService } from './services/permission.service';
import { SessionService } from './services/session.service';
import { AuditService } from './services/audit.service';
import { EncryptionService } from './services/encryption.service';
import { GDPRComplianceService } from './services/gdpr-compliance.service';
import { SOC2ComplianceService } from './services/soc2-compliance.service';
import { SecurityMonitoringService } from './services/security-monitoring.service';

@Global()
@Module({
  imports: [
    ConfigModule,
    ScheduleModule.forRoot(),
    EventEmitterModule.forRoot(),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '24h'),
          issuer: 'peoplenest-hrms',
          audience: 'peoplenest-users',
        },
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      User,
      RefreshToken,
      Session,
      AuditLog,
      Permission,
      UserPermission,
      MfaBackupCode,
      Tenant,
      Employee,
    ]),
  ],
  providers: [
    // Strategies
    JwtStrategy,
    LocalStrategy,
    RefreshTokenStrategy,

    // Guards
    JwtAuthGuard,
    RolesGuard,
    MfaGuard,

    // Services
    AuthService,
    TokenService,
    PasswordService,
    MfaService,
    PermissionService,
    SessionService,
    AuditService,
    EncryptionService,
    GDPRComplianceService,
    SOC2ComplianceService,
    SecurityMonitoringService,
  ],
  exports: [
    // Guards
    JwtAuthGuard,
    RolesGuard,
    MfaGuard,

    // Services
    AuthService,
    TokenService,
    PasswordService,
    MfaService,
    PermissionService,
    SessionService,
    AuditService,
    EncryptionService,
    GDPRComplianceService,
    SOC2ComplianceService,
    SecurityMonitoringService,

    // Modules
    PassportModule,
    JwtModule,
  ],
})
export class SecurityModule {}
