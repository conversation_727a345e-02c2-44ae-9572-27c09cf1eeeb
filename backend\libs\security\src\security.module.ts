import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { User } from '@app/database/entities/user.entity';
import { RefreshToken } from '@app/database/entities/refresh-token.entity';
import { Session } from '@app/database/entities/session.entity';
import { AuditLog } from '@app/database/entities/audit-log.entity';
import { Permission } from '@app/database/entities/permission.entity';
import { UserPermission } from '@app/database/entities/user-permission.entity';
import { MfaBackupCode } from '@app/database/entities/mfa-backup-code.entity';

// Strategies
import { JwtStrategy } from './strategies/jwt.strategy';
import { LocalStrategy } from './strategies/local.strategy';
import { RefreshTokenStrategy } from './strategies/refresh-token.strategy';

// Guards
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RolesGuard } from './guards/roles.guard';
import { MfaGuard } from './guards/mfa.guard';

// Services
import { AuthService } from './services/auth.service';
import { TokenService } from './services/token.service';
import { PasswordService } from './services/password.service';
import { MfaService } from './services/mfa.service';
import { PermissionService } from './services/permission.service';
import { SessionService } from './services/session.service';
import { AuditService } from './services/audit.service';

@Global()
@Module({
  imports: [
    ConfigModule,
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '24h'),
          issuer: 'peoplenest-hrms',
          audience: 'peoplenest-users',
        },
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      User,
      RefreshToken,
      Session,
      AuditLog,
      Permission,
      UserPermission,
      MfaBackupCode,
    ]),
  ],
  providers: [
    // Strategies
    JwtStrategy,
    LocalStrategy,
    RefreshTokenStrategy,

    // Guards
    JwtAuthGuard,
    RolesGuard,
    MfaGuard,

    // Services
    AuthService,
    TokenService,
    PasswordService,
    MfaService,
    PermissionService,
    SessionService,
    AuditService,
  ],
  exports: [
    // Guards
    JwtAuthGuard,
    RolesGuard,
    MfaGuard,

    // Services
    AuthService,
    TokenService,
    PasswordService,
    MfaService,
    PermissionService,
    SessionService,
    AuditService,

    // Modules
    PassportModule,
    JwtModule,
  ],
})
export class SecurityModule {}
