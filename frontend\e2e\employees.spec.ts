import { test, expect } from '@playwright/test';

test.describe('Employee Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'Password1234');
    await page.click('button[type="submit"]');
    
    // Wait for redirect to dashboard
    await expect(page).toHaveURL('/dashboard');
    
    // Navigate to employees page
    await page.goto('/employees');
  });

  test('should display employees list', async ({ page }) => {
    // Check page title
    await expect(page.locator('h1, h2').filter({ hasText: /employees/i })).toBeVisible();
    
    // Check for employee table or list
    await expect(page.locator('table, [data-testid="employee-list"]')).toBeVisible();
    
    // Check for common table headers
    await expect(page.locator('th:has-text("Name"), th:has-text("Email"), th:has-text("Department")')).toBeVisible();
  });

  test('should search employees', async ({ page }) => {
    // Find search input
    const searchInput = page.locator('input[placeholder*="Search"], input[type="search"], [data-testid="search-input"]');
    await expect(searchInput).toBeVisible();
    
    // Search for a specific employee
    await searchInput.fill('admin');
    
    // Wait for search results
    await page.waitForTimeout(1000); // Allow for debounced search
    
    // Check that results are filtered
    const employeeRows = page.locator('tr:has(td), [data-testid="employee-item"]');
    await expect(employeeRows.first()).toBeVisible();
  });

  test('should filter employees by department', async ({ page }) => {
    // Find department filter
    const departmentFilter = page.locator('select:near(text="Department"), [data-testid="department-filter"]');
    
    if (await departmentFilter.isVisible()) {
      // Select a department
      await departmentFilter.selectOption({ index: 1 }); // Select first non-empty option
      
      // Wait for filter to apply
      await page.waitForTimeout(1000);
      
      // Check that results are filtered
      const employeeRows = page.locator('tr:has(td), [data-testid="employee-item"]');
      await expect(employeeRows.first()).toBeVisible();
    }
  });

  test('should open add employee modal', async ({ page }) => {
    // Find and click add employee button
    const addButton = page.locator('button:has-text("Add Employee"), button:has-text("New Employee"), [data-testid="add-employee"]');
    await addButton.click();
    
    // Check that modal is open
    await expect(page.locator('[role="dialog"], .modal, [data-testid="employee-modal"]')).toBeVisible();
    
    // Check for form fields
    await expect(page.locator('input[name="firstName"], input[placeholder*="First"]')).toBeVisible();
    await expect(page.locator('input[name="lastName"], input[placeholder*="Last"]')).toBeVisible();
    await expect(page.locator('input[name="email"], input[type="email"]')).toBeVisible();
  });

  test('should create new employee', async ({ page }) => {
    // Open add employee modal
    const addButton = page.locator('button:has-text("Add Employee"), button:has-text("New Employee"), [data-testid="add-employee"]');
    await addButton.click();
    
    // Fill employee form
    await page.fill('input[name="firstName"], input[placeholder*="First"]', 'Test');
    await page.fill('input[name="lastName"], input[placeholder*="Last"]', 'Employee');
    await page.fill('input[name="email"], input[type="email"]', '<EMAIL>');
    
    // Fill additional required fields if present
    const phoneInput = page.locator('input[name="phone"], input[placeholder*="Phone"]');
    if (await phoneInput.isVisible()) {
      await phoneInput.fill('+1234567890');
    }
    
    const positionSelect = page.locator('select[name="positionId"], select[name="position"]');
    if (await positionSelect.isVisible()) {
      await positionSelect.selectOption({ index: 1 });
    }
    
    const hireDateInput = page.locator('input[name="hireDate"], input[type="date"]');
    if (await hireDateInput.isVisible()) {
      await hireDateInput.fill('2024-01-01');
    }
    
    // Submit form
    await page.click('button[type="submit"], button:has-text("Save"), button:has-text("Create")');
    
    // Check for success message or redirect
    await expect(page.locator('text=Employee created successfully, text=Success')).toBeVisible({ timeout: 10000 });
    
    // Verify employee appears in list
    await expect(page.locator('text=Test Employee')).toBeVisible();
  });

  test('should view employee details', async ({ page }) => {
    // Find first employee row and click view/details button
    const firstEmployeeRow = page.locator('tr:has(td)').first();
    const viewButton = firstEmployeeRow.locator('button:has-text("View"), button:has-text("Details"), a:has-text("View")');
    
    if (await viewButton.isVisible()) {
      await viewButton.click();
    } else {
      // If no explicit view button, click on the employee name/row
      await firstEmployeeRow.click();
    }
    
    // Check that we're on employee detail page or modal is open
    const isDetailPage = await page.locator('h1:has-text("Employee Details"), h2:has-text("Employee Profile")').isVisible();
    const isModal = await page.locator('[role="dialog"]:has-text("Employee Details")').isVisible();
    
    expect(isDetailPage || isModal).toBeTruthy();
    
    // Check for employee information
    await expect(page.locator('text=Email:, text=Department:, text=Position:')).toBeVisible();
  });

  test('should edit employee information', async ({ page }) => {
    // Find first employee and open edit
    const firstEmployeeRow = page.locator('tr:has(td)').first();
    const editButton = firstEmployeeRow.locator('button:has-text("Edit"), [data-testid="edit-employee"]');
    
    if (await editButton.isVisible()) {
      await editButton.click();
    } else {
      // Try clicking on employee row then edit
      await firstEmployeeRow.click();
      await page.click('button:has-text("Edit")');
    }
    
    // Check that edit form is open
    await expect(page.locator('input[name="firstName"], input[placeholder*="First"]')).toBeVisible();
    
    // Update employee information
    await page.fill('input[name="firstName"], input[placeholder*="First"]', 'Updated');
    
    // Save changes
    await page.click('button[type="submit"], button:has-text("Save"), button:has-text("Update")');
    
    // Check for success message
    await expect(page.locator('text=Employee updated successfully, text=Success')).toBeVisible({ timeout: 10000 });
  });

  test('should delete employee', async ({ page }) => {
    // Find first employee and open delete
    const firstEmployeeRow = page.locator('tr:has(td)').first();
    const deleteButton = firstEmployeeRow.locator('button:has-text("Delete"), [data-testid="delete-employee"]');
    
    if (await deleteButton.isVisible()) {
      await deleteButton.click();
    } else {
      // Try accessing delete through menu or modal
      const menuButton = firstEmployeeRow.locator('button:has-text("⋮"), button:has-text("Actions"), [data-testid="employee-menu"]');
      if (await menuButton.isVisible()) {
        await menuButton.click();
        await page.click('button:has-text("Delete")');
      }
    }
    
    // Confirm deletion in modal
    await expect(page.locator('text=Are you sure, text=Delete employee')).toBeVisible();
    await page.click('button:has-text("Delete"), button:has-text("Confirm")');
    
    // Check for success message
    await expect(page.locator('text=Employee deleted successfully, text=Success')).toBeVisible({ timeout: 10000 });
  });

  test('should sort employees by column', async ({ page }) => {
    // Find sortable column header
    const nameHeader = page.locator('th:has-text("Name"), th:has-text("Employee")');
    
    if (await nameHeader.isVisible()) {
      // Click to sort
      await nameHeader.click();
      
      // Wait for sort to apply
      await page.waitForTimeout(1000);
      
      // Check that sort indicator is present
      await expect(nameHeader.locator('svg, .sort-icon, [data-testid="sort-indicator"]')).toBeVisible();
    }
  });

  test('should paginate through employees', async ({ page }) => {
    // Check for pagination controls
    const nextButton = page.locator('button:has-text("Next"), button[aria-label="Next page"]');
    const pageInfo = page.locator('text=Page, text=of, [data-testid="pagination-info"]');
    
    if (await nextButton.isVisible()) {
      // Check current page info
      await expect(pageInfo).toBeVisible();
      
      // Go to next page
      await nextButton.click();
      
      // Wait for page to load
      await page.waitForTimeout(1000);
      
      // Check that page changed
      await expect(pageInfo).toBeVisible();
    }
  });

  test('should export employees data', async ({ page }) => {
    // Find export button
    const exportButton = page.locator('button:has-text("Export"), [data-testid="export-employees"]');
    
    if (await exportButton.isVisible()) {
      // Start download
      const downloadPromise = page.waitForEvent('download');
      await exportButton.click();
      
      // Wait for download to complete
      const download = await downloadPromise;
      expect(download.suggestedFilename()).toMatch(/employees.*\.(csv|xlsx|pdf)$/);
    }
  });

  test('should import employees data', async ({ page }) => {
    // Find import button
    const importButton = page.locator('button:has-text("Import"), [data-testid="import-employees"]');
    
    if (await importButton.isVisible()) {
      await importButton.click();
      
      // Check that import modal is open
      await expect(page.locator('[role="dialog"]:has-text("Import"), .modal:has-text("Import")')).toBeVisible();
      
      // Check for file input
      await expect(page.locator('input[type="file"]')).toBeVisible();
    }
  });

  test('should handle bulk actions', async ({ page }) => {
    // Check for bulk action checkboxes
    const selectAllCheckbox = page.locator('input[type="checkbox"]:near(th)').first();
    
    if (await selectAllCheckbox.isVisible()) {
      // Select all employees
      await selectAllCheckbox.check();
      
      // Check that bulk actions are available
      await expect(page.locator('button:has-text("Bulk"), [data-testid="bulk-actions"]')).toBeVisible();
    }
  });

  test('should validate employee form', async ({ page }) => {
    // Open add employee modal
    const addButton = page.locator('button:has-text("Add Employee"), button:has-text("New Employee")');
    await addButton.click();
    
    // Try to submit empty form
    await page.click('button[type="submit"], button:has-text("Save")');
    
    // Check for validation errors
    await expect(page.locator('text=First name is required, text=Email is required')).toBeVisible();
    
    // Fill invalid email
    await page.fill('input[type="email"]', 'invalid-email');
    await page.click('button[type="submit"]');
    
    // Check for email validation error
    await expect(page.locator('text=Invalid email format')).toBeVisible();
  });

  test('should handle employee status changes', async ({ page }) => {
    // Find first employee
    const firstEmployeeRow = page.locator('tr:has(td)').first();
    
    // Look for status dropdown or toggle
    const statusControl = firstEmployeeRow.locator('select:near(text="Status"), button:has-text("Active"), button:has-text("Inactive")');
    
    if (await statusControl.isVisible()) {
      await statusControl.click();
      
      // Select different status
      await page.click('option:has-text("Inactive"), button:has-text("Inactive")');
      
      // Check for confirmation or success message
      await expect(page.locator('text=Status updated, text=Success')).toBeVisible({ timeout: 10000 });
    }
  });
});
