{"name": "@peoplenest/frontend", "version": "1.0.0", "description": "PeopleNest HRMS Frontend Application", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@apollo/client": "^3.8.8", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@reduxjs/toolkit": "^2.0.1", "@tanstack/react-query": "^5.8.4", "@tanstack/react-table": "^8.10.7", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "graphql": "^16.8.1", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@playwright/test": "^1.40.1", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^7.6.3", "@storybook/addon-links": "^7.6.3", "@storybook/addon-onboarding": "^9.0.13", "@storybook/blocks": "^8.6.14", "@storybook/react": "^9.0.13", "@storybook/react-vite": "^9.0.13", "@storybook/testing-library": "^0.2.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.10.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "eslint-plugin-storybook": "^0.6.15", "jsdom": "^23.0.1", "postcss": "^8.4.32", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "storybook": "^9.0.13", "tailwindcss": "^3.3.6", "typescript": "^5.3.0", "vite": "^7.0.0", "vite-tsconfig-paths": "^4.2.1", "vitest": "^3.2.4"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}}