/**
 * User Status Enumeration
 * Defines the various states a user account can be in
 */
export enum UserStatus {
  /**
   * Active - User account is active and can access the system
   */
  ACTIVE = 'active',

  /**
   * Inactive - User account is temporarily disabled
   */
  INACTIVE = 'inactive',

  /**
   * Pending - User account is pending activation (e.g., email verification)
   */
  PENDING = 'pending',

  /**
   * Suspended - User account is suspended due to policy violations
   */
  SUSPENDED = 'suspended',

  /**
   * Locked - User account is locked due to security reasons (e.g., too many failed login attempts)
   */
  LOCKED = 'locked',

  /**
   * Archived - User account is archived (soft deleted)
   */
  ARCHIVED = 'archived',
}

/**
 * Status display names for UI
 */
export const USER_STATUS_DISPLAY_NAMES = {
  [UserStatus.ACTIVE]: 'Active',
  [UserStatus.INACTIVE]: 'Inactive',
  [UserStatus.PENDING]: 'Pending Activation',
  [UserStatus.SUSPENDED]: 'Suspended',
  [UserStatus.LOCKED]: 'Locked',
  [UserStatus.ARCHIVED]: 'Archived',
};

/**
 * Status descriptions
 */
export const USER_STATUS_DESCRIPTIONS = {
  [UserStatus.ACTIVE]: 'User account is active and can access the system',
  [UserStatus.INACTIVE]: 'User account is temporarily disabled',
  [UserStatus.PENDING]: 'User account is pending activation',
  [UserStatus.SUSPENDED]: 'User account is suspended due to policy violations',
  [UserStatus.LOCKED]: 'User account is locked due to security reasons',
  [UserStatus.ARCHIVED]: 'User account is archived and cannot be accessed',
};
