import { Resolver, Query, Mutation, Args, Context, ID } from '@nestjs/graphql';
import { UseGuards, ValidationPipe } from '@nestjs/common';

import { JwtAuthGuard, RolesGuard } from '@app/security/guards';
import { Roles } from '@app/security/decorators/roles.decorator';
import { UserRole } from '@app/common/enums/status.enum';
import { RequestWithUser } from '@app/security/interfaces/request-with-user.interface';

import { EmployeeService } from '../services/employee.service';
import { CreateEmployeeDto } from '../dto/create-employee.dto';
import { UpdateEmployeeDto } from '../dto/update-employee.dto';
import { EmployeeQueryDto, EmployeeStatsQueryDto } from '../dto/employee-query.dto';
import {
  EmployeeResponseDto,
  EmployeeListResponseDto,
  EmployeeStatsResponseDto,
} from '../dto/employee-response.dto';

@Resolver('Employee')
@UseGuards(JwtAuthGuard, RolesGuard)
export class EmployeeResolver {
  constructor(private readonly employeeService: EmployeeService) {}

  @Query('employees')
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.MANAGER, UserRole.EMPLOYEE, UserRole.SUPER_ADMIN)
  async getEmployees(
    @Args('query', { nullable: true }) query: EmployeeQueryDto = {},
    @Context('req') req: RequestWithUser,
  ): Promise<EmployeeListResponseDto> {
    return this.employeeService.findAll(query, req.user.tenantId);
  }

  @Query('employee')
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.MANAGER, UserRole.EMPLOYEE, UserRole.SUPER_ADMIN)
  async getEmployee(
    @Args('id', { type: () => ID }) id: string,
    @Args('include', { type: () => [String], nullable: true }) include?: string[],
    @Context('req') req: RequestWithUser,
  ): Promise<EmployeeResponseDto> {
    return this.employeeService.findById(id, req.user.tenantId, include);
  }

  @Query('employeeByEmployeeId')
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.MANAGER, UserRole.EMPLOYEE, UserRole.SUPER_ADMIN)
  async getEmployeeByEmployeeId(
    @Args('employeeId') employeeId: string,
    @Args('include', { type: () => [String], nullable: true }) include?: string[],
    @Context('req') req: RequestWithUser,
  ): Promise<EmployeeResponseDto> {
    return this.employeeService.findByEmployeeId(employeeId, req.user.tenantId, include);
  }

  @Query('employeeStats')
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.MANAGER, UserRole.SUPER_ADMIN)
  async getEmployeeStats(
    @Args('query', { nullable: true }) query: EmployeeStatsQueryDto = {},
    @Context('req') req: RequestWithUser,
  ): Promise<EmployeeStatsResponseDto> {
    return this.employeeService.getStats(query, req.user.tenantId);
  }

  @Mutation('createEmployee')
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.SUPER_ADMIN)
  async createEmployee(
    @Args('input', ValidationPipe) createEmployeeDto: CreateEmployeeDto,
    @Context('req') req: RequestWithUser,
  ): Promise<EmployeeResponseDto> {
    return this.employeeService.create(
      createEmployeeDto,
      req.user.tenantId,
      req.user.id,
    );
  }

  @Mutation('updateEmployee')
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.SUPER_ADMIN)
  async updateEmployee(
    @Args('id', { type: () => ID }) id: string,
    @Args('input', ValidationPipe) updateEmployeeDto: UpdateEmployeeDto,
    @Context('req') req: RequestWithUser,
  ): Promise<EmployeeResponseDto> {
    return this.employeeService.update(
      id,
      updateEmployeeDto,
      req.user.tenantId,
      req.user.id,
    );
  }

  @Mutation('deleteEmployee')
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.SUPER_ADMIN)
  async deleteEmployee(
    @Args('id', { type: () => ID }) id: string,
    @Args('reason', { nullable: true }) reason?: string,
    @Context('req') req: RequestWithUser,
  ): Promise<boolean> {
    await this.employeeService.remove(
      id,
      req.user.tenantId,
      req.user.id,
      reason,
    );
    return true;
  }
}
