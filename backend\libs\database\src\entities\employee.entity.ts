import {
  Entity,
  Column,
  Index,
  OneToMany,
  ManyToOne,
  OneToOne,
  JoinColumn,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { EncryptedEntity } from './base.entity';
import { Tenant } from './tenant.entity';
import { User } from './user.entity';
import { Department } from './department.entity';
import { Position } from './position.entity';
import { EmployeeContact } from './employee-contact.entity';
import { EmployeeAddress } from './employee-address.entity';
import { EmployeeDocument } from './employee-document.entity';
import { EmployeeEmergencyContact } from './employee-emergency-contact.entity';
import { EmployeeEducation } from './employee-education.entity';
import { EmployeeExperience } from './employee-experience.entity';
import { EmployeeSkill } from './employee-skill.entity';
import { EmployeeCertification } from './employee-certification.entity';
import { Payslip } from './payslip.entity';
import { EmployeeBenefit } from './employee-benefit.entity';
import { EmployeeDeduction } from './employee-deduction.entity';
import { EmployeeStatus, EmploymentType, Gender, MaritalStatus } from '@app/common/enums/status.enum';

@Entity('employees')
@Index(['employeeId'], { unique: true })
@Index(['email'], { unique: true })
@Index(['tenantId', 'status'])
@Index(['departmentId', 'status'])
export class Employee extends EncryptedEntity {
  @Column({
    type: 'varchar',
    length: 50,
    unique: true,
    comment: 'Unique employee identifier',
  })
  employeeId: string;

  @Column({
    type: 'varchar',
    length: 255,
    unique: true,
    comment: 'Employee email address (encrypted)',
  })
  @Index()
  email: string;

  @Column({
    type: 'varchar',
    length: 100,
    comment: 'First name (encrypted)',
  })
  firstName: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Middle name (encrypted)',
  })
  middleName?: string;

  @Column({
    type: 'varchar',
    length: 100,
    comment: 'Last name (encrypted)',
  })
  lastName: string;

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
    comment: 'Preferred name (encrypted)',
  })
  preferredName?: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Date of birth (encrypted)',
  })
  dateOfBirth?: Date;

  @Column({
    type: 'enum',
    enum: Gender,
    nullable: true,
    comment: 'Gender',
  })
  gender?: Gender;

  @Column({
    type: 'enum',
    enum: MaritalStatus,
    nullable: true,
    comment: 'Marital status',
  })
  maritalStatus?: MaritalStatus;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Nationality',
  })
  nationality?: string;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: 'Phone number (encrypted)',
  })
  phoneNumber?: string;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: 'Personal phone number (encrypted)',
  })
  personalPhoneNumber?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Profile picture URL',
  })
  profilePicture?: string;

  @Column({
    type: 'enum',
    enum: EmployeeStatus,
    default: EmployeeStatus.ACTIVE,
    comment: 'Employee status',
  })
  @Index()
  status: EmployeeStatus;

  @Column({
    type: 'enum',
    enum: EmploymentType,
    default: EmploymentType.FULL_TIME,
    comment: 'Employment type',
  })
  employmentType: EmploymentType;

  @Column({
    type: 'date',
    comment: 'Date of joining',
  })
  @Index()
  dateOfJoining: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Date of leaving',
  })
  dateOfLeaving?: Date;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Probation end date',
  })
  probationEndDate?: Date;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether probation is completed',
  })
  isProbationCompleted: boolean;

  @Column({
    type: 'uuid',
    comment: 'Department ID',
  })
  departmentId: string;

  @Column({
    type: 'uuid',
    comment: 'Position ID',
  })
  positionId: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Manager employee ID',
  })
  managerId?: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    comment: 'Base salary (encrypted)',
  })
  baseSalary?: number;

  @Column({
    type: 'varchar',
    length: 10,
    default: 'USD',
    comment: 'Salary currency',
  })
  salaryCurrency: string;

  @Column({
    type: 'varchar',
    length: 20,
    default: 'monthly',
    comment: 'Salary frequency (monthly, bi-weekly, weekly)',
  })
  salaryFrequency: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Work location',
  })
  workLocation?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Time zone',
  })
  timeZone?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Work schedule configuration',
  })
  workSchedule?: {
    hoursPerWeek?: number;
    workDays?: string[];
    startTime?: string;
    endTime?: string;
    isFlexible?: boolean;
  };

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Social Security Number (encrypted)',
  })
  socialSecurityNumber?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Tax ID (encrypted)',
  })
  taxId?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Passport number (encrypted)',
  })
  passportNumber?: string;

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
    comment: 'Visa status',
  })
  visaStatus?: string;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Visa expiry date',
  })
  visaExpiryDate?: Date;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Emergency contact information (encrypted)',
  })
  emergencyContact?: {
    name?: string;
    relationship?: string;
    phoneNumber?: string;
    email?: string;
    address?: string;
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Banking information (encrypted)',
  })
  bankingInfo?: {
    bankName?: string;
    accountNumber?: string;
    routingNumber?: string;
    accountType?: string;
    swiftCode?: string;
  };

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Additional employee metadata',
  })
  metadata?: Record<string, any>;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Notes about the employee',
  })
  notes?: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Associated user account',
  })
  userId?: string;

  // Relationships
  @ManyToOne(() => Tenant, { eager: false })
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @OneToOne(() => User, { eager: false, nullable: true })
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @ManyToOne(() => Department, { eager: false })
  @JoinColumn({ name: 'department_id' })
  department: Department;

  @ManyToOne(() => Position, { eager: false })
  @JoinColumn({ name: 'position_id' })
  position: Position;

  @ManyToOne(() => Employee, { nullable: true })
  @JoinColumn({ name: 'manager_id' })
  manager?: Employee;

  @OneToMany(() => Employee, employee => employee.manager)
  directReports: Employee[];

  @OneToMany(() => EmployeeContact, contact => contact.employee, { cascade: true })
  contacts: EmployeeContact[];

  @OneToMany(() => EmployeeAddress, address => address.employee, { cascade: true })
  addresses: EmployeeAddress[];

  @OneToMany(() => EmployeeDocument, document => document.employee, { cascade: true })
  documents: EmployeeDocument[];

  @OneToMany(() => EmployeeEmergencyContact, contact => contact.employee, { cascade: true })
  emergencyContacts: EmployeeEmergencyContact[];

  @OneToMany(() => EmployeeEducation, education => education.employee, { cascade: true })
  education: EmployeeEducation[];

  @OneToMany(() => EmployeeExperience, experience => experience.employee, { cascade: true })
  experience: EmployeeExperience[];

  @OneToMany(() => EmployeeSkill, skill => skill.employee, { cascade: true })
  skills: EmployeeSkill[];

  @OneToMany(() => EmployeeCertification, certification => certification.employee, { cascade: true })
  certifications: EmployeeCertification[];

  // Payroll relationships
  @OneToMany(() => Payslip, payslip => payslip.employee)
  payslips: Payslip[];

  @OneToMany(() => EmployeeBenefit, benefit => benefit.employee, { cascade: true })
  employeeBenefits: EmployeeBenefit[];

  @OneToMany(() => EmployeeDeduction, deduction => deduction.employee, { cascade: true })
  employeeDeductions: EmployeeDeduction[];

  // Virtual properties
  get fullName(): string {
    const parts = [this.firstName, this.middleName, this.lastName].filter(Boolean);
    return parts.join(' ');
  }

  get displayName(): string {
    return this.preferredName || this.firstName;
  }

  get age(): number | null {
    if (!this.dateOfBirth) return null;
    const today = new Date();
    const birthDate = new Date(this.dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  }

  get yearsOfService(): number {
    const today = new Date();
    const joinDate = new Date(this.dateOfJoining);
    return Math.floor((today.getTime() - joinDate.getTime()) / (1000 * 60 * 60 * 24 * 365.25));
  }

  get isActive(): boolean {
    return this.status === EmployeeStatus.ACTIVE;
  }

  get isProbationPeriod(): boolean {
    if (!this.probationEndDate) return false;
    return new Date() < this.probationEndDate;
  }

  @BeforeInsert()
  @BeforeUpdate()
  normalizeEmail() {
    if (this.email) {
      this.email = this.email.toLowerCase().trim();
    }
  }

  @BeforeInsert()
  @BeforeUpdate()
  setEncryptionFlag() {
    // Mark as encrypted if contains PII data
    this.isEncrypted = !!(
      this.email ||
      this.firstName ||
      this.lastName ||
      this.dateOfBirth ||
      this.phoneNumber ||
      this.socialSecurityNumber ||
      this.taxId ||
      this.passportNumber
    );
  }
}
