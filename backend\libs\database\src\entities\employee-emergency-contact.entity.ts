import { Entity, Column, Index, ManyToOne, Join<PERSON><PERSON>umn } from 'typeorm';
import { EncryptedEntity } from './base.entity';
import { Employee } from './employee.entity';

@Entity('employee_emergency_contacts')
@Index(['employeeId'])
export class EmployeeEmergencyContact extends EncryptedEntity {
  @Column({ type: 'uuid', comment: 'Employee ID' })
  employeeId: string;

  @Column({ type: 'varchar', length: 255, comment: 'Contact name (encrypted)' })
  name: string;

  @Column({ type: 'varchar', length: 100, comment: 'Relationship' })
  relationship: string;

  @Column({ type: 'varchar', length: 20, comment: 'Phone number (encrypted)' })
  phoneNumber: string;

  @Column({ type: 'varchar', length: 255, nullable: true, comment: 'Email (encrypted)' })
  email?: string;

  @Column({ type: 'boolean', default: false, comment: 'Primary emergency contact' })
  isPrimary: boolean;

  @ManyToOne(() => Employee, employee => employee.emergencyContacts, { eager: false })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;
}
