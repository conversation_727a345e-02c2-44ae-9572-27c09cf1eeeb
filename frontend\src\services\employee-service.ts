import { apiClient } from './api-client';

export interface Employee {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  employeeId: string;
  hireDate: string;
  status: 'active' | 'inactive' | 'terminated';
  profilePicture?: string;
  location?: string;
  position: {
    id: string;
    title: string;
    department: {
      id: string;
      name: string;
    };
  };
  manager?: {
    id: string;
    firstName: string;
    lastName: string;
  };
  personalInfo?: {
    dateOfBirth?: string;
    address?: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
    emergencyContact?: {
      name: string;
      relationship: string;
      phone: string;
    };
  };
  workInfo?: {
    workLocation: string;
    workSchedule: string;
    employmentType: 'full_time' | 'part_time' | 'contract' | 'intern';
    salary?: number;
    benefits?: string[];
  };
}

export interface CreateEmployeeRequest {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  positionId: string;
  managerId?: string;
  hireDate: string;
  employmentType: 'full_time' | 'part_time' | 'contract' | 'intern';
  salary?: number;
  workLocation: string;
  personalInfo?: {
    dateOfBirth?: string;
    address?: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
    emergencyContact?: {
      name: string;
      relationship: string;
      phone: string;
    };
  };
}

export interface UpdateEmployeeRequest extends Partial<CreateEmployeeRequest> {
  status?: 'active' | 'inactive' | 'terminated';
}

export interface EmployeeSearchParams {
  page?: number;
  limit?: number;
  search?: string;
  department?: string;
  status?: string;
  position?: string;
  manager?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface EmployeeListResponse {
  data: Employee[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface Department {
  id: string;
  name: string;
  description?: string;
  managerId?: string;
  parentDepartmentId?: string;
  employeeCount: number;
}

export interface Position {
  id: string;
  title: string;
  description?: string;
  departmentId: string;
  level: string;
  requirements?: string[];
  responsibilities?: string[];
}

class EmployeeService {
  /**
   * Get paginated list of employees
   */
  async getEmployees(params: EmployeeSearchParams = {}): Promise<EmployeeListResponse> {
    const response = await apiClient.get('/employees', { params });
    return response.data;
  }

  /**
   * Get employee by ID
   */
  async getEmployee(id: string): Promise<Employee> {
    const response = await apiClient.get(`/employees/${id}`);
    return response.data;
  }

  /**
   * Create new employee
   */
  async createEmployee(data: CreateEmployeeRequest): Promise<Employee> {
    const response = await apiClient.post('/employees', data);
    return response.data;
  }

  /**
   * Update employee
   */
  async updateEmployee(id: string, data: UpdateEmployeeRequest): Promise<Employee> {
    const response = await apiClient.patch(`/employees/${id}`, data);
    return response.data;
  }

  /**
   * Delete employee
   */
  async deleteEmployee(id: string): Promise<void> {
    await apiClient.delete(`/employees/${id}`);
  }

  /**
   * Upload employee profile picture
   */
  async uploadProfilePicture(id: string, file: File): Promise<{ profilePicture: string }> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await apiClient.post(`/employees/${id}/profile-picture`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  /**
   * Get employee's direct reports
   */
  async getDirectReports(managerId: string): Promise<Employee[]> {
    const response = await apiClient.get(`/employees/${managerId}/direct-reports`);
    return response.data;
  }

  /**
   * Get employee's organizational chart
   */
  async getOrganizationalChart(employeeId: string): Promise<{
    employee: Employee;
    manager?: Employee;
    directReports: Employee[];
    peers: Employee[];
  }> {
    const response = await apiClient.get(`/employees/${employeeId}/org-chart`);
    return response.data;
  }

  /**
   * Get all departments
   */
  async getDepartments(): Promise<Department[]> {
    const response = await apiClient.get('/departments');
    return response.data;
  }

  /**
   * Get department by ID
   */
  async getDepartment(id: string): Promise<Department> {
    const response = await apiClient.get(`/departments/${id}`);
    return response.data;
  }

  /**
   * Get employees in a department
   */
  async getDepartmentEmployees(departmentId: string): Promise<Employee[]> {
    const response = await apiClient.get(`/departments/${departmentId}/employees`);
    return response.data;
  }

  /**
   * Get all positions
   */
  async getPositions(): Promise<Position[]> {
    const response = await apiClient.get('/positions');
    return response.data;
  }

  /**
   * Get position by ID
   */
  async getPosition(id: string): Promise<Position> {
    const response = await apiClient.get(`/positions/${id}`);
    return response.data;
  }

  /**
   * Get positions in a department
   */
  async getDepartmentPositions(departmentId: string): Promise<Position[]> {
    const response = await apiClient.get(`/departments/${departmentId}/positions`);
    return response.data;
  }

  /**
   * Search employees
   */
  async searchEmployees(query: string): Promise<Employee[]> {
    const response = await apiClient.get('/employees/search', {
      params: { q: query },
    });
    return response.data;
  }

  /**
   * Get employee statistics
   */
  async getEmployeeStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    newHires: number;
    departures: number;
    byDepartment: Array<{
      departmentId: string;
      departmentName: string;
      count: number;
    }>;
    byPosition: Array<{
      positionId: string;
      positionTitle: string;
      count: number;
    }>;
  }> {
    const response = await apiClient.get('/employees/stats');
    return response.data;
  }

  /**
   * Export employees data
   */
  async exportEmployees(
    format: 'csv' | 'xlsx' | 'pdf' = 'xlsx',
    filters?: EmployeeSearchParams
  ): Promise<Blob> {
    const response = await apiClient.get('/employees/export', {
      params: { format, ...filters },
      responseType: 'blob',
    });
    return response.data;
  }

  /**
   * Bulk import employees
   */
  async importEmployees(file: File): Promise<{
    success: number;
    failed: number;
    errors: Array<{
      row: number;
      error: string;
    }>;
  }> {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await apiClient.post('/employees/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  /**
   * Get employee onboarding checklist
   */
  async getOnboardingChecklist(employeeId: string): Promise<Array<{
    id: string;
    title: string;
    description: string;
    completed: boolean;
    dueDate?: string;
    assignedTo?: string;
    category: 'hr' | 'it' | 'manager' | 'employee';
  }>> {
    const response = await apiClient.get(`/employees/${employeeId}/onboarding`);
    return response.data;
  }

  /**
   * Update onboarding checklist item
   */
  async updateOnboardingItem(
    employeeId: string,
    itemId: string,
    completed: boolean
  ): Promise<void> {
    await apiClient.patch(`/employees/${employeeId}/onboarding/${itemId}`, {
      completed,
    });
  }

  /**
   * Get employee documents
   */
  async getEmployeeDocuments(employeeId: string): Promise<Array<{
    id: string;
    name: string;
    type: string;
    size: number;
    uploadedAt: string;
    uploadedBy: string;
    category: 'contract' | 'identification' | 'certification' | 'other';
  }>> {
    const response = await apiClient.get(`/employees/${employeeId}/documents`);
    return response.data;
  }

  /**
   * Upload employee document
   */
  async uploadDocument(
    employeeId: string,
    file: File,
    category: string
  ): Promise<void> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('category', category);
    
    await apiClient.post(`/employees/${employeeId}/documents`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * Delete employee document
   */
  async deleteDocument(employeeId: string, documentId: string): Promise<void> {
    await apiClient.delete(`/employees/${employeeId}/documents/${documentId}`);
  }

  /**
   * Get employee performance summary
   */
  async getPerformanceSummary(employeeId: string): Promise<{
    currentRating: number;
    goalProgress: number;
    completedReviews: number;
    pendingReviews: number;
    recentFeedback: Array<{
      rating: number;
      comment: string;
      date: string;
      reviewer: string;
    }>;
  }> {
    const response = await apiClient.get(`/employees/${employeeId}/performance`);
    return response.data;
  }
}

export const employeeService = new EmployeeService();
