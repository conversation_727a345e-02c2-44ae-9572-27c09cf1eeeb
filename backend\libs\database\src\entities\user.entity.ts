import {
  <PERSON><PERSON><PERSON>,
  Column,
  Index,
  OneToMany,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rt,
  BeforeUpdate,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { UserRole } from '@app/common/enums/user-role.enum';
import { UserStatus } from '@app/common/enums/status.enum';
import { EncryptedEntity } from './base.entity';
import { Tenant } from './tenant.entity';
import { Employee } from './employee.entity';
import { Session } from './session.entity';
import { RefreshToken } from './refresh-token.entity';
import { UserPermission } from './user-permission.entity';
import { MfaBackupCode } from './mfa-backup-code.entity';

@Entity('users')
@Index(['email', 'tenantId'], { unique: true })
@Index(['employeeId'], { unique: true, where: 'employee_id IS NOT NULL' })
export class User extends EncryptedEntity {
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'User email address (encrypted)',
  })
  @Index()
  email: string;

  @Column({
    type: 'varchar',
    length: 255,
    select: false,
    comment: 'Hashed password',
  })
  @Exclude()
  password: string;

  @Column({
    type: 'varchar',
    length: 100,
    comment: 'First name (encrypted)',
  })
  firstName: string;

  @Column({
    type: 'varchar',
    length: 100,
    comment: 'Last name (encrypted)',
  })
  lastName: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.EMPLOYEE,
    comment: 'User role in the system',
  })
  @Index()
  role: UserRole;

  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE,
    comment: 'User account status',
  })
  @Index()
  status: UserStatus;

  @Column({
    type: 'varchar',
    length: 20,
    nullable: true,
    comment: 'Phone number (encrypted)',
  })
  phoneNumber?: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Profile picture URL',
  })
  profilePicture?: string;

  @Column({
    type: 'varchar',
    length: 10,
    default: 'en',
    comment: 'Preferred language',
  })
  preferredLanguage: string;

  @Column({
    type: 'varchar',
    length: 50,
    default: 'UTC',
    comment: 'User timezone',
  })
  timezone: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Last login timestamp',
  })
  lastLoginAt?: Date;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Last password change timestamp',
  })
  passwordChangedAt?: Date;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether MFA is enabled',
  })
  mfaEnabled: boolean;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    select: false,
    comment: 'MFA secret key (encrypted)',
  })
  @Exclude()
  mfaSecret?: string;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Failed login attempts counter',
  })
  failedLoginAttempts: number;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Account locked until timestamp',
  })
  lockedUntil?: Date;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether email is verified',
  })
  emailVerified: boolean;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Email verification token',
  })
  emailVerificationToken?: string;

  @Column({
    type: 'timestamp with time zone',
    nullable: true,
    comment: 'Email verification token expiry',
  })
  emailVerificationExpiry?: Date;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'User preferences and settings',
  })
  preferences?: Record<string, any>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Custom permissions for this user',
  })
  permissions?: string[];

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'Associated employee record',
  })
  employeeId?: string;

  // Relationships
  @ManyToOne(() => Tenant, { eager: false })
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @ManyToOne(() => Employee, { eager: false, nullable: true })
  @JoinColumn({ name: 'employee_id' })
  employee?: Employee;

  @OneToMany(() => Session, session => session.user, { cascade: true })
  sessions: Session[];

  @OneToMany(() => RefreshToken, token => token.user, { cascade: true })
  refreshTokens: RefreshToken[];

  @OneToMany(() => UserPermission, permission => permission.user, { cascade: true })
  userPermissions: UserPermission[];

  @OneToMany(() => MfaBackupCode, code => code.user, { cascade: true })
  mfaBackupCodes: MfaBackupCode[];

  // Virtual properties
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`.trim();
  }

  get isLocked(): boolean {
    return this.lockedUntil ? this.lockedUntil > new Date() : false;
  }

  get isActive(): boolean {
    return this.status === UserStatus.ACTIVE && !this.isLocked;
  }

  @BeforeInsert()
  @BeforeUpdate()
  normalizeEmail() {
    if (this.email) {
      this.email = this.email.toLowerCase().trim();
    }
  }

  @BeforeInsert()
  @BeforeUpdate()
  setEncryptionFlag() {
    // Mark as encrypted if contains PII data
    this.isEncrypted = !!(this.email || this.firstName || this.lastName || this.phoneNumber);
  }
}
