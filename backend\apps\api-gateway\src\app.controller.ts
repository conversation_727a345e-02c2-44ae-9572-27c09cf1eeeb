import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AppService } from './app.service';
import { JwtAuthGuard } from '@app/security/guards/jwt-auth.guard';
import { RolesGuard } from '@app/security/guards/roles.guard';
import { Roles } from '@app/security/decorators/roles.decorator';
import { UserRole } from '@app/common/enums/user-role.enum';
import { Public } from '@app/security/decorators/public.decorator';

@ApiTags('Application')
@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Public()
  @Get()
  @ApiOperation({ 
    summary: 'Get application information',
    description: 'Returns basic information about the PeopleNest HRMS API'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Application information retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'PeopleNest HRMS API' },
        version: { type: 'string', example: '1.0.0' },
        description: { type: 'string', example: 'AI-Enabled Enterprise Human Resource Management System' },
        environment: { type: 'string', example: 'development' },
        timestamp: { type: 'string', format: 'date-time' },
        uptime: { type: 'number', example: 3600 },
        status: { type: 'string', example: 'healthy' }
      }
    }
  })
  getAppInfo() {
    return this.appService.getAppInfo();
  }

  @Public()
  @Get('version')
  @ApiOperation({ 
    summary: 'Get API version',
    description: 'Returns the current version of the API'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'API version retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        version: { type: 'string', example: '1.0.0' },
        buildDate: { type: 'string', format: 'date-time' },
        gitCommit: { type: 'string', example: 'abc123def456' }
      }
    }
  })
  getVersion() {
    return this.appService.getVersion();
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SUPER_ADMIN)
  @Get('system-info')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ 
    summary: 'Get system information',
    description: 'Returns detailed system information (Admin only)'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'System information retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        nodeVersion: { type: 'string', example: 'v20.10.0' },
        platform: { type: 'string', example: 'linux' },
        architecture: { type: 'string', example: 'x64' },
        memory: {
          type: 'object',
          properties: {
            used: { type: 'number', example: 134217728 },
            total: { type: 'number', example: 1073741824 }
          }
        },
        cpu: {
          type: 'object',
          properties: {
            model: { type: 'string', example: 'Intel(R) Core(TM) i7-9750H CPU @ 2.60GHz' },
            cores: { type: 'number', example: 8 }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  getSystemInfo() {
    return this.appService.getSystemInfo();
  }
}
