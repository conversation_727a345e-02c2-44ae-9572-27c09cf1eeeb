import { SetMetadata } from '@nestjs/common';
import { UserRole } from '@app/common/enums/user-role.enum';

export const ROLES_KEY = 'roles';

/**
 * Decorator to specify required roles for accessing a route
 * 
 * @param roles - Array of roles that can access this route
 * 
 * @example
 * ```typescript
 * @Roles(UserRole.ADMIN, UserRole.HR)
 * @Get('sensitive-data')
 * getSensitiveData() {
 *   return this.service.getSensitiveData();
 * }
 * ```
 */
export const Roles = (...roles: UserRole[]) => SetMetadata(ROLES_KEY, roles);
