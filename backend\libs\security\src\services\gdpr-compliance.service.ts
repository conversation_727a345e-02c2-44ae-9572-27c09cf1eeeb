import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';

import { User } from '@app/database/entities/user.entity';
import { Employee } from '@app/database/entities/employee.entity';
import { AuditLog } from '@app/database/entities/audit-log.entity';
import { EncryptionService } from './encryption.service';
import { AuditService } from './audit.service';

export interface GDPRRequest {
  id: string;
  type: 'access' | 'rectification' | 'erasure' | 'portability' | 'restriction' | 'objection';
  subjectId: string;
  subjectEmail: string;
  tenantId: string;
  requestedBy: string;
  requestDate: Date;
  status: 'pending' | 'processing' | 'completed' | 'rejected';
  completionDate?: Date;
  reason?: string;
  metadata?: Record<string, any>;
}

export interface DataSubjectRights {
  rightToAccess: boolean;
  rightToRectification: boolean;
  rightToErasure: boolean;
  rightToPortability: boolean;
  rightToRestriction: boolean;
  rightToObject: boolean;
}

export interface ConsentRecord {
  id: string;
  subjectId: string;
  tenantId: string;
  purpose: string;
  consentGiven: boolean;
  consentDate: Date;
  withdrawalDate?: Date;
  legalBasis: 'consent' | 'contract' | 'legal_obligation' | 'vital_interests' | 'public_task' | 'legitimate_interests';
  dataCategories: string[];
  retentionPeriod: number; // in days
  metadata?: Record<string, any>;
}

@Injectable()
export class GDPRComplianceService {
  private readonly logger = new Logger(GDPRComplianceService.name);
  private readonly gdprRequests = new Map<string, GDPRRequest>();
  private readonly consentRecords = new Map<string, ConsentRecord>();

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Employee)
    private readonly employeeRepository: Repository<Employee>,
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
    private readonly encryptionService: EncryptionService,
    private readonly auditService: AuditService,
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly entityManager: EntityManager,
  ) {}

  /**
   * Handle data subject access request (Article 15)
   */
  async handleAccessRequest(subjectId: string, requestedBy: string, tenantId: string): Promise<{
    personalData: Record<string, any>;
    processingPurposes: string[];
    dataCategories: string[];
    recipients: string[];
    retentionPeriod: string;
    rights: DataSubjectRights;
  }> {
    this.logger.log(`Processing access request for subject ${subjectId}`);

    // Create GDPR request record
    const requestId = this.createGDPRRequest({
      type: 'access',
      subjectId,
      subjectEmail: '',
      tenantId,
      requestedBy,
    });

    try {
      // Gather all personal data
      const personalData = await this.gatherPersonalData(subjectId, tenantId);
      
      // Get processing information
      const processingInfo = await this.getProcessingInformation(subjectId, tenantId);
      
      // Complete the request
      this.completeGDPRRequest(requestId, { personalData, processingInfo });

      // Audit the access
      await this.auditService.logDataAccess({
        userId: requestedBy,
        resource: 'gdpr_access_request',
        resourceId: subjectId,
        action: 'read',
        metadata: { requestId, subjectId },
      });

      return {
        personalData,
        processingPurposes: processingInfo.purposes,
        dataCategories: processingInfo.categories,
        recipients: processingInfo.recipients,
        retentionPeriod: processingInfo.retentionPeriod,
        rights: this.getDataSubjectRights(),
      };
    } catch (error) {
      this.rejectGDPRRequest(requestId, error.message);
      throw error;
    }
  }

  /**
   * Handle data rectification request (Article 16)
   */
  async handleRectificationRequest(
    subjectId: string,
    corrections: Record<string, any>,
    requestedBy: string,
    tenantId: string,
  ): Promise<void> {
    this.logger.log(`Processing rectification request for subject ${subjectId}`);

    const requestId = this.createGDPRRequest({
      type: 'rectification',
      subjectId,
      subjectEmail: '',
      tenantId,
      requestedBy,
    });

    try {
      // Validate corrections
      await this.validateRectificationData(corrections);

      // Apply corrections
      await this.applyDataCorrections(subjectId, corrections, tenantId);

      // Complete the request
      this.completeGDPRRequest(requestId, { corrections });

      // Audit the rectification
      await this.auditService.logUserAction({
        userId: requestedBy,
        action: 'gdpr_rectification',
        resource: 'personal_data',
        resourceId: subjectId,
        success: true,
        metadata: { requestId, corrections },
      });

      // Emit event for downstream processing
      this.eventEmitter.emit('gdpr.rectification.completed', {
        subjectId,
        corrections,
        requestId,
      });
    } catch (error) {
      this.rejectGDPRRequest(requestId, error.message);
      throw error;
    }
  }

  /**
   * Handle data erasure request (Article 17 - Right to be forgotten)
   */
  async handleErasureRequest(
    subjectId: string,
    requestedBy: string,
    tenantId: string,
    reason: string,
  ): Promise<void> {
    this.logger.log(`Processing erasure request for subject ${subjectId}`);

    const requestId = this.createGDPRRequest({
      type: 'erasure',
      subjectId,
      subjectEmail: '',
      tenantId,
      requestedBy,
    });

    try {
      // Check if erasure is legally permissible
      const canErase = await this.validateErasureRequest(subjectId, tenantId, reason);
      
      if (!canErase.allowed) {
        throw new Error(`Erasure not permitted: ${canErase.reason}`);
      }

      // Perform data erasure
      const erasureResult = await this.performDataErasure(subjectId, tenantId);

      // Complete the request
      this.completeGDPRRequest(requestId, { erasureResult });

      // Audit the erasure
      await this.auditService.logUserAction({
        userId: requestedBy,
        action: 'gdpr_erasure',
        resource: 'personal_data',
        resourceId: subjectId,
        success: true,
        metadata: { requestId, reason, erasureResult },
      });

      // Emit event for downstream processing
      this.eventEmitter.emit('gdpr.erasure.completed', {
        subjectId,
        requestId,
        erasureResult,
      });
    } catch (error) {
      this.rejectGDPRRequest(requestId, error.message);
      throw error;
    }
  }

  /**
   * Handle data portability request (Article 20)
   */
  async handlePortabilityRequest(
    subjectId: string,
    requestedBy: string,
    tenantId: string,
    format: 'json' | 'csv' | 'xml' = 'json',
  ): Promise<{
    data: string;
    format: string;
    filename: string;
  }> {
    this.logger.log(`Processing portability request for subject ${subjectId}`);

    const requestId = this.createGDPRRequest({
      type: 'portability',
      subjectId,
      subjectEmail: '',
      tenantId,
      requestedBy,
    });

    try {
      // Gather portable data
      const portableData = await this.gatherPortableData(subjectId, tenantId);

      // Format data according to request
      const formattedData = await this.formatPortableData(portableData, format);

      // Complete the request
      this.completeGDPRRequest(requestId, { format, dataSize: formattedData.length });

      // Audit the portability request
      await this.auditService.logDataAccess({
        userId: requestedBy,
        resource: 'gdpr_portability_request',
        resourceId: subjectId,
        action: 'read',
        metadata: { requestId, format },
      });

      return {
        data: formattedData,
        format,
        filename: `personal_data_${subjectId}_${Date.now()}.${format}`,
      };
    } catch (error) {
      this.rejectGDPRRequest(requestId, error.message);
      throw error;
    }
  }

  /**
   * Record consent for data processing
   */
  async recordConsent(consent: Omit<ConsentRecord, 'id'>): Promise<string> {
    const consentId = this.encryptionService.generateSecureToken();
    
    const consentRecord: ConsentRecord = {
      id: consentId,
      ...consent,
    };

    this.consentRecords.set(consentId, consentRecord);

    // Audit consent recording
    await this.auditService.logUserAction({
      userId: consent.subjectId,
      action: 'consent_recorded',
      resource: 'consent',
      resourceId: consentId,
      success: true,
      metadata: {
        purpose: consent.purpose,
        legalBasis: consent.legalBasis,
        dataCategories: consent.dataCategories,
      },
    });

    this.logger.log(`Consent recorded for subject ${consent.subjectId}, purpose: ${consent.purpose}`);
    return consentId;
  }

  /**
   * Withdraw consent
   */
  async withdrawConsent(consentId: string, subjectId: string): Promise<void> {
    const consent = this.consentRecords.get(consentId);
    
    if (!consent || consent.subjectId !== subjectId) {
      throw new Error('Consent record not found or unauthorized');
    }

    consent.consentGiven = false;
    consent.withdrawalDate = new Date();

    // Audit consent withdrawal
    await this.auditService.logUserAction({
      userId: subjectId,
      action: 'consent_withdrawn',
      resource: 'consent',
      resourceId: consentId,
      success: true,
      metadata: { purpose: consent.purpose },
    });

    // Emit event for downstream processing
    this.eventEmitter.emit('gdpr.consent.withdrawn', {
      consentId,
      subjectId,
      purpose: consent.purpose,
    });

    this.logger.log(`Consent withdrawn for subject ${subjectId}, consent: ${consentId}`);
  }

  /**
   * Check if consent is valid for processing
   */
  isConsentValid(consentId: string): boolean {
    const consent = this.consentRecords.get(consentId);
    
    if (!consent || !consent.consentGiven || consent.withdrawalDate) {
      return false;
    }

    // Check if consent has expired based on retention period
    const expiryDate = new Date(consent.consentDate);
    expiryDate.setDate(expiryDate.getDate() + consent.retentionPeriod);

    return new Date() <= expiryDate;
  }

  /**
   * Automated data retention cleanup
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async performDataRetentionCleanup(): Promise<void> {
    this.logger.log('Starting automated data retention cleanup');

    try {
      // Find expired consent records
      const expiredConsents = Array.from(this.consentRecords.values())
        .filter(consent => !this.isConsentValid(consent.id));

      for (const consent of expiredConsents) {
        await this.handleDataRetentionExpiry(consent);
      }

      // Clean up old audit logs based on retention policy
      const retentionDays = this.configService.get<number>('AUDIT_LOG_RETENTION_DAYS', 2555);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      await this.auditLogRepository.delete({
        createdAt: { $lt: cutoffDate } as any,
      });

      this.logger.log('Data retention cleanup completed');
    } catch (error) {
      this.logger.error('Data retention cleanup failed:', error);
    }
  }

  /**
   * Generate GDPR compliance report
   */
  async generateComplianceReport(tenantId: string): Promise<{
    totalRequests: number;
    requestsByType: Record<string, number>;
    averageProcessingTime: number;
    consentMetrics: {
      totalConsents: number;
      activeConsents: number;
      withdrawnConsents: number;
    };
    dataBreaches: number;
    complianceScore: number;
  }> {
    const requests = Array.from(this.gdprRequests.values())
      .filter(req => req.tenantId === tenantId);

    const consents = Array.from(this.consentRecords.values())
      .filter(consent => consent.tenantId === tenantId);

    const requestsByType = requests.reduce((acc, req) => {
      acc[req.type] = (acc[req.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const completedRequests = requests.filter(req => req.status === 'completed');
    const averageProcessingTime = completedRequests.length > 0
      ? completedRequests.reduce((sum, req) => {
          const processingTime = req.completionDate!.getTime() - req.requestDate.getTime();
          return sum + processingTime;
        }, 0) / completedRequests.length / (1000 * 60 * 60 * 24) // Convert to days
      : 0;

    const activeConsents = consents.filter(consent => this.isConsentValid(consent.id)).length;
    const withdrawnConsents = consents.filter(consent => consent.withdrawalDate).length;

    // Calculate compliance score based on various factors
    const complianceScore = this.calculateComplianceScore(requests, consents);

    return {
      totalRequests: requests.length,
      requestsByType,
      averageProcessingTime,
      consentMetrics: {
        totalConsents: consents.length,
        activeConsents,
        withdrawnConsents,
      },
      dataBreaches: 0, // This would come from a separate breach tracking system
      complianceScore,
    };
  }

  // Private helper methods
  private createGDPRRequest(request: Omit<GDPRRequest, 'id' | 'requestDate' | 'status'>): string {
    const requestId = this.encryptionService.generateSecureToken();
    
    const gdprRequest: GDPRRequest = {
      id: requestId,
      requestDate: new Date(),
      status: 'pending',
      ...request,
    };

    this.gdprRequests.set(requestId, gdprRequest);
    return requestId;
  }

  private completeGDPRRequest(requestId: string, result: any): void {
    const request = this.gdprRequests.get(requestId);
    if (request) {
      request.status = 'completed';
      request.completionDate = new Date();
      request.metadata = result;
    }
  }

  private rejectGDPRRequest(requestId: string, reason: string): void {
    const request = this.gdprRequests.get(requestId);
    if (request) {
      request.status = 'rejected';
      request.reason = reason;
      request.completionDate = new Date();
    }
  }

  private async gatherPersonalData(subjectId: string, tenantId: string): Promise<Record<string, any>> {
    // This would gather data from all relevant tables
    const user = await this.userRepository.findOne({
      where: { id: subjectId, tenantId },
    });

    const employee = await this.employeeRepository.findOne({
      where: { userId: subjectId, tenantId },
    });

    return {
      user: user || {},
      employee: employee || {},
      // Add other data sources as needed
    };
  }

  private async getProcessingInformation(subjectId: string, tenantId: string): Promise<{
    purposes: string[];
    categories: string[];
    recipients: string[];
    retentionPeriod: string;
  }> {
    // This would return information about how the data is processed
    return {
      purposes: ['Employment management', 'Payroll processing', 'Performance evaluation'],
      categories: ['Identity data', 'Contact data', 'Employment data', 'Financial data'],
      recipients: ['HR department', 'Payroll service', 'Management'],
      retentionPeriod: '7 years after employment termination',
    };
  }

  private getDataSubjectRights(): DataSubjectRights {
    return {
      rightToAccess: true,
      rightToRectification: true,
      rightToErasure: true,
      rightToPortability: true,
      rightToRestriction: true,
      rightToObject: true,
    };
  }

  private async validateRectificationData(corrections: Record<string, any>): Promise<void> {
    // Validate that the corrections are appropriate and legal
    // This would include business logic validation
  }

  private async applyDataCorrections(
    subjectId: string,
    corrections: Record<string, any>,
    tenantId: string,
  ): Promise<void> {
    // Apply the corrections to the relevant data
    // This would update multiple tables as needed
  }

  private async validateErasureRequest(
    subjectId: string,
    tenantId: string,
    reason: string,
  ): Promise<{ allowed: boolean; reason?: string }> {
    // Check legal grounds for erasure
    // Consider legal obligations, legitimate interests, etc.
    return { allowed: true };
  }

  private async performDataErasure(subjectId: string, tenantId: string): Promise<{
    tablesAffected: string[];
    recordsDeleted: number;
    recordsAnonymized: number;
  }> {
    // Perform the actual data erasure or anonymization
    // This would be a complex operation affecting multiple tables
    return {
      tablesAffected: ['users', 'employees', 'audit_logs'],
      recordsDeleted: 0,
      recordsAnonymized: 1,
    };
  }

  private async gatherPortableData(subjectId: string, tenantId: string): Promise<Record<string, any>> {
    // Gather data that the subject has a right to port
    return this.gatherPersonalData(subjectId, tenantId);
  }

  private async formatPortableData(data: Record<string, any>, format: string): Promise<string> {
    switch (format) {
      case 'json':
        return JSON.stringify(data, null, 2);
      case 'csv':
        // Convert to CSV format
        return this.convertToCSV(data);
      case 'xml':
        // Convert to XML format
        return this.convertToXML(data);
      default:
        return JSON.stringify(data, null, 2);
    }
  }

  private convertToCSV(data: Record<string, any>): string {
    // Simple CSV conversion - would need more sophisticated implementation
    const headers = Object.keys(data).join(',');
    const values = Object.values(data).map(v => JSON.stringify(v)).join(',');
    return `${headers}\n${values}`;
  }

  private convertToXML(data: Record<string, any>): string {
    // Simple XML conversion - would need more sophisticated implementation
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n<personalData>\n';
    for (const [key, value] of Object.entries(data)) {
      xml += `  <${key}>${JSON.stringify(value)}</${key}>\n`;
    }
    xml += '</personalData>';
    return xml;
  }

  private async handleDataRetentionExpiry(consent: ConsentRecord): Promise<void> {
    // Handle expired consent - anonymize or delete data as appropriate
    this.logger.log(`Handling data retention expiry for consent ${consent.id}`);
  }

  private calculateComplianceScore(requests: GDPRRequest[], consents: ConsentRecord[]): number {
    // Calculate a compliance score based on various metrics
    let score = 100;

    // Deduct points for delayed responses
    const delayedRequests = requests.filter(req => {
      if (req.status === 'completed' && req.completionDate) {
        const processingDays = (req.completionDate.getTime() - req.requestDate.getTime()) / (1000 * 60 * 60 * 24);
        return processingDays > 30; // GDPR requires response within 30 days
      }
      return false;
    });

    score -= (delayedRequests.length / requests.length) * 20;

    // Deduct points for rejected requests
    const rejectedRequests = requests.filter(req => req.status === 'rejected');
    score -= (rejectedRequests.length / requests.length) * 10;

    return Math.max(0, Math.min(100, score));
  }
}
