import { composeConfigs } from 'storybook/internal/preview-api';
import actionsAddon from '@storybook/addon-actions';
import backgroundsAddon from '@storybook/addon-backgrounds';
import * as docsAddon from '@storybook/addon-docs/preview';
import highlightAddon from '@storybook/addon-highlight';
import measureAddon from '@storybook/addon-measure';
import outlineAddon from '@storybook/addon-outline';
import viewportAddon from '@storybook/addon-viewport';

var preview_default=composeConfigs([actionsAddon(),docsAddon,backgroundsAddon(),viewportAddon(),measureAddon(),outlineAddon(),highlightAddon()]);

export { preview_default as default };
