"use strict";var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __export=(target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})},__copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod)),__toCommonJS=mod=>__copyProps(__defProp({},"__esModule",{value:!0}),mod);var react_18_exports={};__export(react_18_exports,{renderElement:()=>renderElement,unmountElement:()=>unmountElement});module.exports=__toCommonJS(react_18_exports);var React=__toESM(require("react")),ReactDOM=__toESM(require("react-dom/client")),nodes=new Map;function getIsReactActEnvironment(){return globalThis.IS_REACT_ACT_ENVIRONMENT}var WithCallback=({callback,children})=>{let once=React.useRef();return React.useLayoutEffect(()=>{once.current!==callback&&(once.current=callback,callback())},[callback]),children};typeof Promise.withResolvers>"u"&&(Promise.withResolvers=()=>{let resolve=null,reject=null;return{promise:new Promise((res,rej)=>{resolve=res,reject=rej}),resolve,reject}});var renderElement=async(node,el,rootOptions)=>{let root=await getReactRoot(el,rootOptions);if(getIsReactActEnvironment()){root.render(node);return}let{promise,resolve}=Promise.withResolvers();return root.render(React.createElement(WithCallback,{callback:resolve},node)),promise},unmountElement=(el,shouldUseNewRootApi)=>{let root=nodes.get(el);root&&(root.unmount(),nodes.delete(el))},getReactRoot=async(el,rootOptions)=>{let root=nodes.get(el);return root||(root=ReactDOM.createRoot(el,rootOptions),nodes.set(el,root)),root};0&&(module.exports={renderElement,unmountElement});
