import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Department } from '@app/database';

@Injectable()
export class DepartmentService {
  constructor(
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
  ) {}

  // TODO: Implement department management methods
  async findAll(tenantId: string): Promise<Department[]> {
    return this.departmentRepository.find({
      where: { tenantId },
      order: { name: 'ASC' },
    });
  }

  async findById(id: string, tenantId: string): Promise<Department> {
    return this.departmentRepository.findOne({
      where: { id, tenantId },
    });
  }
}
