import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';

// Core Entities
import { User } from './entities/user.entity';
import { Tenant } from './entities/tenant.entity';
import { RefreshToken } from './entities/refresh-token.entity';
import { Session } from './entities/session.entity';
import { AuditLog } from './entities/audit-log.entity';
import { Permission } from './entities/permission.entity';
import { UserPermission } from './entities/user-permission.entity';
import { MfaBackupCode } from './entities/mfa-backup-code.entity';

// Employee Management Entities
import { Employee } from './entities/employee.entity';
import { Department } from './entities/department.entity';
import { Position } from './entities/position.entity';
import { EmployeeDocument } from './entities/employee-document.entity';
import { EmployeeContact } from './entities/employee-contact.entity';
import { EmployeeAddress } from './entities/employee-address.entity';
import { EmployeeEmergencyContact } from './entities/employee-emergency-contact.entity';
import { EmployeeEducation } from './entities/employee-education.entity';
import { EmployeeExperience } from './entities/employee-experience.entity';
import { EmployeeSkill } from './entities/employee-skill.entity';
import { EmployeeCertification } from './entities/employee-certification.entity';

// Payroll Entities
import { PayrollPeriod } from './entities/payroll-period.entity';
import { Payslip } from './entities/payslip.entity';
import { PayrollItem } from './entities/payroll-item.entity';
import { TaxConfiguration } from './entities/tax-configuration.entity';
import { BenefitPlan } from './entities/benefit-plan.entity';
import { EmployeeBenefit } from './entities/employee-benefit.entity';
import { Deduction } from './entities/deduction.entity';
import { EmployeeDeduction } from './entities/employee-deduction.entity';

// Time Management Entities
import { TimeEntry } from './entities/time-entry.entity';
import { LeaveType } from './entities/leave-type.entity';
import { LeaveRequest } from './entities/leave-request.entity';
import { Holiday } from './entities/holiday.entity';
import { WorkSchedule } from './entities/work-schedule.entity';
import { Attendance } from './entities/attendance.entity';

// Performance Management Entities
import { PerformanceReview } from './entities/performance-review.entity';
import { Goal } from './entities/goal.entity';
import { Feedback } from './entities/feedback.entity';
import { PerformanceMetric } from './entities/performance-metric.entity';

// Recruitment Entities
import { JobPosting } from './entities/job-posting.entity';
import { Candidate } from './entities/candidate.entity';
import { Application } from './entities/application.entity';
import { Interview } from './entities/interview.entity';

// Notification Entities
import { Notification } from './entities/notification.entity';
import { NotificationTemplate } from './entities/notification-template.entity';

// Document Management Entities
import { Document } from './entities/document.entity';
import { DocumentVersion } from './entities/document-version.entity';
import { DocumentAccess } from './entities/document-access.entity';

// Reporting Entities
import { Report } from './entities/report.entity';
import { ReportSchedule } from './entities/report-schedule.entity';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([
      // Core Entities
      User,
      Tenant,
      RefreshToken,
      Session,
      AuditLog,
      Permission,
      UserPermission,
      MfaBackupCode,
      
      // Employee Management Entities
      Employee,
      Department,
      Position,
      EmployeeDocument,
      EmployeeContact,
      EmployeeAddress,
      EmployeeEmergencyContact,
      EmployeeEducation,
      EmployeeExperience,
      EmployeeSkill,
      EmployeeCertification,
      
      // Payroll Entities
      PayrollPeriod,
      Payslip,
      PayrollItem,
      TaxConfiguration,
      BenefitPlan,
      EmployeeBenefit,
      Deduction,
      EmployeeDeduction,
      
      // Time Management Entities
      TimeEntry,
      LeaveType,
      LeaveRequest,
      Holiday,
      WorkSchedule,
      Attendance,
      
      // Performance Management Entities
      PerformanceReview,
      Goal,
      Feedback,
      PerformanceMetric,
      
      // Recruitment Entities
      JobPosting,
      Candidate,
      Application,
      Interview,
      
      // Notification Entities
      Notification,
      NotificationTemplate,
      
      // Document Management Entities
      Document,
      DocumentVersion,
      DocumentAccess,
      
      // Reporting Entities
      Report,
      ReportSchedule,
    ]),
  ],
  exports: [TypeOrmModule],
})
export class DatabaseModule {}
