# PeopleNest AI/ML Service

## Overview

The PeopleNest AI/ML Service is a comprehensive Python FastAPI microservice that provides artificial intelligence and machine learning capabilities for the PeopleNest HRMS system. This service enables intelligent HR operations through advanced analytics, natural language processing, and predictive modeling.

## Features

### 🧠 Core AI Services

1. **Resume Parsing & Analysis**
   - Multi-format document support (PDF, DOCX, images with OCR)
   - NLP-powered extraction of contact information, experience, education, and skills
   - Job matching and candidate ranking
   - Skill gap analysis and quality assessment

2. **Sentiment Analysis**
   - Advanced sentiment analysis using transformer models
   - Emotion detection and workplace issue identification
   - Employee feedback analysis with department-wise insights
   - Performance review sentiment analysis

3. **Predictive Analytics**
   - Employee attrition prediction with risk factor analysis
   - Performance forecasting with confidence intervals
   - Trend analysis with statistical insights
   - Employee risk profiling with comprehensive scoring

4. **Natural Language Queries**
   - Intent classification for HR data exploration
   - Entity extraction for HR-specific terms
   - SQL query generation from natural language
   - Conversational response formatting

5. **Anomaly Detection**
   - Payroll fraud detection using statistical and ML methods
   - Compliance violation monitoring
   - Pattern-based anomaly detection
   - Risk assessment and fraud prevention

## Architecture

### Technology Stack

- **Framework**: FastAPI (Python 3.9+)
- **ML Libraries**: 
  - Hugging Face Transformers
  - scikit-learn
  - spaCy
  - TensorFlow/PyTorch
- **Document Processing**: PyPDF2, python-docx, pytesseract
- **Database**: PostgreSQL (via SQLAlchemy)
- **Authentication**: JWT with RBAC integration
- **Monitoring**: Prometheus metrics, structured logging

### Service Structure

```
backend/services/ai-service/
├── app/
│   ├── api/
│   │   └── routes.py              # API endpoints
│   ├── core/
│   │   ├── config.py              # Configuration management
│   │   ├── auth.py                # Authentication & authorization
│   │   ├── exceptions.py          # Custom exceptions
│   │   └── logging_config.py      # Logging configuration
│   ├── models/
│   │   ├── resume.py              # Resume parsing models
│   │   ├── sentiment.py           # Sentiment analysis models
│   │   ├── predictive.py          # Predictive analytics models
│   │   ├── nlp.py                 # NLP query models
│   │   └── anomaly.py             # Anomaly detection models
│   └── services/
│       ├── resume_parser.py       # Resume parsing service
│       ├── sentiment_analyzer.py  # Sentiment analysis service
│       ├── predictive_analytics.py # Predictive analytics service
│       ├── nlp_query.py           # NLP query service
│       └── anomaly_detection.py   # Anomaly detection service
├── main.py                        # FastAPI application
└── requirements.txt               # Python dependencies
```

## API Endpoints

### Resume Processing
- `POST /api/v1/resume/parse` - Parse resume file
- `POST /api/v1/resume/batch-parse` - Batch resume parsing
- `POST /api/v1/resume/analyze` - Analyze resume against job requirements

### Sentiment Analysis
- `POST /api/v1/sentiment/analyze` - Analyze text sentiment
- `POST /api/v1/sentiment/batch-analyze` - Batch sentiment analysis
- `POST /api/v1/sentiment/feedback` - Analyze employee feedback
- `POST /api/v1/sentiment/performance-review` - Analyze performance reviews

### Predictive Analytics
- `POST /api/v1/predict/attrition` - Predict employee attrition
- `POST /api/v1/predict/performance` - Forecast performance
- `POST /api/v1/predict/trends` - Analyze trends
- `POST /api/v1/predict/risk-profile` - Generate risk profile

### Natural Language Queries
- `POST /api/v1/query/nlp` - Process natural language query
- `GET /api/v1/query/suggestions` - Get query suggestions
- `POST /api/v1/query/explain` - Explain query processing

### Anomaly Detection
- `POST /api/v1/anomaly/detect` - Detect payroll anomalies

### System Management
- `GET /health` - Health check
- `GET /api/v1/models/status` - Model status
- `POST /api/v1/models/initialize` - Initialize models
- `GET /api/v1/analytics/usage` - Usage analytics

## Configuration

### Environment Variables

```bash
# Service Configuration
AI_SERVICE_HOST=0.0.0.0
AI_SERVICE_PORT=8003
DEBUG=true

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/peoplenest_ai

# Authentication
JWT_SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256
AUTH_SERVICE_URL=http://localhost:8001

# AI/ML Configuration
MODEL_CACHE_DIR=/app/models
ENABLE_GPU=false
MAX_BATCH_SIZE=32

# External APIs
OPENAI_API_KEY=your-openai-key
HUGGINGFACE_API_KEY=your-hf-key

# Monitoring
ENABLE_METRICS=true
LOG_LEVEL=INFO
```

## Installation & Setup

### Prerequisites

- Python 3.9+
- PostgreSQL 12+
- Redis (for caching)
- Docker (optional)

### Local Development

1. **Clone and navigate to the service directory**
   ```bash
   cd backend/services/ai-service
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Run the service**
   ```bash
   python main.py
   ```

### Docker Deployment

```bash
# Build image
docker build -t peoplenest-ai-service .

# Run container
docker run -p 8003:8003 --env-file .env peoplenest-ai-service
```

## Usage Examples

### Resume Parsing

```python
import requests

# Parse a resume
with open('resume.pdf', 'rb') as f:
    response = requests.post(
        'http://localhost:8003/api/v1/resume/parse',
        files={'file': f},
        data={'extract_skills': True}
    )
    
resume_data = response.json()
print(f"Extracted skills: {resume_data['skills']}")
```

### Sentiment Analysis

```python
# Analyze employee feedback
response = requests.post(
    'http://localhost:8003/api/v1/sentiment/analyze',
    json={
        'text': 'I really enjoy working with my team and the company culture is great!',
        'context': 'employee_feedback'
    }
)

sentiment = response.json()
print(f"Sentiment: {sentiment['sentiment']} (confidence: {sentiment['confidence']})")
```

### Predictive Analytics

```python
# Predict employee attrition
response = requests.post(
    'http://localhost:8003/api/v1/predict/attrition',
    json={
        'employee_data': {
            'tenure_months': 24,
            'satisfaction_score': 3.2,
            'performance_rating': 4.1,
            'salary': 75000,
            'department': 'Engineering'
        }
    }
)

prediction = response.json()
print(f"Attrition probability: {prediction['probability']:.2%}")
```

## Security

### Authentication & Authorization

- JWT-based authentication with role-based access control
- Permission-based endpoint access
- Rate limiting per user and endpoint
- Data access filtering based on user roles

### Data Protection

- Encryption of sensitive data in transit and at rest
- PII data anonymization for ML training
- Audit logging for all AI operations
- Secure model storage and versioning

## Monitoring & Observability

### Health Checks

- Service health endpoint with dependency checks
- Model initialization status monitoring
- Performance metrics collection

### Logging

- Structured JSON logging with correlation IDs
- Request/response logging with performance metrics
- Error tracking and alerting
- Model performance monitoring

### Metrics

- Prometheus metrics for service monitoring
- Custom metrics for AI model performance
- Usage analytics and reporting
- Resource utilization tracking

## Development

### Adding New AI Services

1. Create service class in `app/services/`
2. Define Pydantic models in `app/models/`
3. Add API routes in `app/api/routes.py`
4. Update service initialization in `main.py`
5. Add tests and documentation

### Model Management

- Models are loaded asynchronously during startup
- Automatic fallback to rule-based systems if models fail
- Model versioning and A/B testing support
- Performance monitoring and alerting

## Testing

```bash
# Run unit tests
pytest tests/

# Run integration tests
pytest tests/integration/

# Run performance tests
pytest tests/performance/

# Generate coverage report
pytest --cov=app tests/
```

## Contributing

1. Follow PEP 8 style guidelines
2. Add type hints for all functions
3. Include comprehensive docstrings
4. Write tests for new features
5. Update documentation

## License

This project is part of the PeopleNest HRMS system and is proprietary software.
