"""
Custom exceptions for the AI service.
"""

from typing import Op<PERSON>, Dict, Any
from fastapi import HTTPException, status


class AIServiceException(Exception):
    """Base exception for AI service errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)


class ResumeParsingError(AIServiceException):
    """Exception raised when resume parsing fails."""
    pass


class SentimentAnalysisError(AIServiceException):
    """Exception raised when sentiment analysis fails."""
    pass


class PredictiveAnalysisError(AIServiceException):
    """Exception raised when predictive analysis fails."""
    pass


class ModelLoadingError(AIServiceException):
    """Exception raised when ML model loading fails."""
    pass


class DataProcessingError(AIServiceException):
    """Exception raised when data processing fails."""
    pass


class ValidationError(AIServiceException):
    """Exception raised when input validation fails."""
    pass


class ConfigurationError(AIServiceException):
    """Exception raised when configuration is invalid."""
    pass


class ExternalAPIError(AIServiceException):
    """Exception raised when external API calls fail."""
    pass


class DatabaseError(AIServiceException):
    """Exception raised when database operations fail."""
    pass


class AuthenticationError(AIServiceException):
    """Exception raised when authentication fails."""
    pass


class AuthorizationError(AIServiceException):
    """Exception raised when authorization fails."""
    pass


class RateLimitError(AIServiceException):
    """Exception raised when rate limits are exceeded."""
    pass


class ResourceNotFoundError(AIServiceException):
    """Exception raised when requested resource is not found."""
    pass


class ServiceUnavailableError(AIServiceException):
    """Exception raised when service is temporarily unavailable."""
    pass


# HTTP Exception mappings
def map_to_http_exception(exception: AIServiceException) -> HTTPException:
    """Map custom exceptions to HTTP exceptions."""
    
    exception_mapping = {
        ValidationError: status.HTTP_400_BAD_REQUEST,
        ResourceNotFoundError: status.HTTP_404_NOT_FOUND,
        AuthenticationError: status.HTTP_401_UNAUTHORIZED,
        AuthorizationError: status.HTTP_403_FORBIDDEN,
        RateLimitError: status.HTTP_429_TOO_MANY_REQUESTS,
        ServiceUnavailableError: status.HTTP_503_SERVICE_UNAVAILABLE,
        ConfigurationError: status.HTTP_500_INTERNAL_SERVER_ERROR,
        ModelLoadingError: status.HTTP_500_INTERNAL_SERVER_ERROR,
        DatabaseError: status.HTTP_500_INTERNAL_SERVER_ERROR,
        ExternalAPIError: status.HTTP_502_BAD_GATEWAY,
    }
    
    status_code = exception_mapping.get(type(exception), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return HTTPException(
        status_code=status_code,
        detail={
            "error": exception.__class__.__name__,
            "message": exception.message,
            "details": exception.details,
        }
    )


def setup_exception_handlers(app):
    """Setup global exception handlers for the FastAPI app."""
    
    @app.exception_handler(AIServiceException)
    async def ai_service_exception_handler(request, exc: AIServiceException):
        """Handle custom AI service exceptions."""
        http_exc = map_to_http_exception(exc)
        return http_exc
    
    @app.exception_handler(ValueError)
    async def value_error_handler(request, exc: ValueError):
        """Handle ValueError exceptions."""
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "error": "ValueError",
                "message": str(exc),
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request, exc: Exception):
        """Handle general exceptions."""
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "InternalServerError",
                "message": "An unexpected error occurred",
                "details": {"exception_type": exc.__class__.__name__}
            }
        )
