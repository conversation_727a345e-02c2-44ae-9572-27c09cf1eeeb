import {
  <PERSON><PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';
import { Application } from './application.entity';
import { Interview } from './interview.entity';

export enum CandidateStatus {
  NEW = 'new',
  SCREENING = 'screening',
  INTERVIEWING = 'interviewing',
  OFFER_EXTENDED = 'offer_extended',
  HIRED = 'hired',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn',
  ON_HOLD = 'on_hold',
}

@Entity('candidates')
@Index(['tenantId', 'email'])
@Index(['tenantId', 'status'])
export class Candidate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ name: 'first_name', length: 255 })
  firstName: string;

  @Column({ name: 'last_name', length: 255 })
  lastName: string;

  @Column({ length: 255, unique: true })
  email: string;

  @Column({ length: 20, nullable: true })
  phone: string;

  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ length: 255, nullable: true })
  city: string;

  @Column({ length: 255, nullable: true })
  state: string;

  @Column({ length: 20, nullable: true, name: 'postal_code' })
  postalCode: string;

  @Column({ length: 255, nullable: true })
  country: string;

  @Column({
    type: 'enum',
    enum: CandidateStatus,
    default: CandidateStatus.NEW,
  })
  status: CandidateStatus;

  @Column({ type: 'text', nullable: true, name: 'resume_text' })
  resumeText: string;

  @Column({ length: 500, nullable: true, name: 'resume_file_path' })
  resumeFilePath: string;

  @Column({ length: 500, nullable: true, name: 'cover_letter_path' })
  coverLetterPath: string;

  @Column({ length: 500, nullable: true, name: 'linkedin_url' })
  linkedinUrl: string;

  @Column({ length: 500, nullable: true, name: 'portfolio_url' })
  portfolioUrl: string;

  @Column({ type: 'json', nullable: true })
  skills: string[];

  @Column({ type: 'json', nullable: true })
  experience: Array<{
    company: string;
    position: string;
    startDate: string;
    endDate?: string;
    description?: string;
  }>;

  @Column({ type: 'json', nullable: true })
  education: Array<{
    institution: string;
    degree: string;
    field: string;
    graduationYear?: number;
    gpa?: number;
  }>;

  @Column({ type: 'json', nullable: true })
  certifications: Array<{
    name: string;
    issuer: string;
    issueDate?: string;
    expiryDate?: string;
  }>;

  @Column({ type: 'decimal', precision: 12, scale: 2, nullable: true, name: 'expected_salary' })
  expectedSalary: number;

  @Column({ length: 50, nullable: true, name: 'salary_currency' })
  salaryCurrency: string;

  @Column({ type: 'boolean', default: false, name: 'willing_to_relocate' })
  willingToRelocate: boolean;

  @Column({ type: 'integer', nullable: true, name: 'notice_period_days' })
  noticePeriodDays: number;

  @Column({ type: 'json', nullable: true, name: 'ai_analysis' })
  aiAnalysis: {
    skillsMatch?: number;
    experienceMatch?: number;
    overallScore?: number;
    strengths?: string[];
    gaps?: string[];
    recommendations?: string[];
  };

  @OneToMany(() => Application, application => application.candidate)
  applications: Application[];

  @OneToMany(() => Interview, interview => interview.candidate)
  interviews: Interview[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  // Computed properties
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }

  get totalExperienceYears(): number {
    if (!this.experience || this.experience.length === 0) return 0;
    
    return this.experience.reduce((total, exp) => {
      const startDate = new Date(exp.startDate);
      const endDate = exp.endDate ? new Date(exp.endDate) : new Date();
      const years = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 365);
      return total + years;
    }, 0);
  }
}
