import { Entity, Column, Index, ManyToOne, Join<PERSON><PERSON>umn } from 'typeorm';
import { TenantAwareEntity } from './base.entity';
import { Employee } from './employee.entity';

@Entity('employee_experience')
@Index(['employeeId'])
export class EmployeeExperience extends TenantAwareEntity {
  @Column({ type: 'uuid', comment: 'Employee ID' })
  employeeId: string;

  @Column({ type: 'varchar', length: 255, comment: 'Company name' })
  company: string;

  @Column({ type: 'varchar', length: 255, comment: 'Job title' })
  title: string;

  @Column({ type: 'text', nullable: true, comment: 'Job description' })
  description?: string;

  @Column({ type: 'date', comment: 'Start date' })
  startDate: Date;

  @Column({ type: 'date', nullable: true, comment: 'End date' })
  endDate?: Date;

  @Column({ type: 'boolean', default: false, comment: 'Current job' })
  isCurrent: boolean;

  @ManyToOne(() => Employee, employee => employee.experience, { eager: false })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;
}
