"use strict";var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod));var import_react=__toESM(require("react")),import_components=require("storybook/internal/components"),import_docs_tools=require("storybook/internal/docs-tools"),import_manager_api=require("storybook/internal/manager-api"),import_theming=require("storybook/internal/theming"),import_blocks=require("@storybook/blocks");import_manager_api.addons.register(import_docs_tools.ADDON_ID,api=>{import_manager_api.addons.add(import_docs_tools.PANEL_ID,{title:"Code",type:import_manager_api.types.PANEL,paramKey:import_docs_tools.PARAM_KEY,disabled:parameters=>!parameters?.docs?.codePanel,match:({viewMode})=>viewMode==="story",render:({active})=>{let parameter=(0,import_manager_api.useParameter)(import_docs_tools.PARAM_KEY,{source:{code:""},theme:"dark"}),[codeSnippet,setSourceCode]=import_react.default.useState({});(0,import_manager_api.useChannel)({[import_docs_tools.SNIPPET_RENDERED]:({source,format})=>{setSourceCode({source,format})}});let isDark=(0,import_theming.useTheme)().base!=="light";return import_react.default.createElement(import_components.AddonPanel,{active:!!active},import_react.default.createElement(SourceStyles,null,import_react.default.createElement(import_blocks.Source,{...parameter.source,code:parameter.source.code||codeSnippet.source,format:parameter.source.format||codeSnippet.format,dark:isDark})))}})});var SourceStyles=import_theming.styled.div(()=>({height:"100%",[`> :first-child${import_theming.ignoreSsrWarning}`]:{margin:0,height:"100%",boxShadow:"none"}}));
