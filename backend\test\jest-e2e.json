{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "moduleNameMapping": {"^@app/common/(.*)$": "<rootDir>/../libs/common/src/$1", "^@app/common$": "<rootDir>/../libs/common/src", "^@app/database/(.*)$": "<rootDir>/../libs/database/src/$1", "^@app/database$": "<rootDir>/../libs/database/src", "^@app/security/(.*)$": "<rootDir>/../libs/security/src/$1", "^@app/security$": "<rootDir>/../libs/security/src"}, "setupFilesAfterEnv": ["<rootDir>/setup-e2e.ts"], "testTimeout": 30000, "collectCoverageFrom": ["**/*.(t|j)s", "!**/*.spec.ts", "!**/*.e2e-spec.ts", "!**/node_modules/**", "!**/dist/**", "!**/coverage/**"], "coverageDirectory": "./coverage-e2e", "coverageReporters": ["text", "lcov", "html"]}