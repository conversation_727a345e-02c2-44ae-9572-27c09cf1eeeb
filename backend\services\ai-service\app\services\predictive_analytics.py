"""
Predictive analytics service for HR insights.
Provides employee attrition prediction, performance forecasting, and trend analysis.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import asyncio
import pickle
import joblib

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier, GradientBoostingRegressor
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, mean_squared_error, r2_score
from sklearn.cluster import KMeans
import xgboost as xgb
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

from app.core.config import settings
from app.core.exceptions import PredictiveAnalysisError, ModelLoadingError
from app.models.predictive import (
    AttritionPrediction, PerformanceForecast, TrendAnalysis,
    EmployeeRiskProfile, PredictiveInsights
)

logger = logging.getLogger(__name__)


class PredictiveAnalytics:
    """Advanced predictive analytics for HR data."""
    
    def __init__(self):
        self.attrition_model = None
        self.performance_model = None
        self.salary_model = None
        self.engagement_model = None
        self.scalers = {}
        self.encoders = {}
        self.feature_importance = {}
        self.model_metrics = {}
        self.initialized = False
        
    async def initialize(self):
        """Initialize predictive models."""
        try:
            # Initialize models
            await self._initialize_attrition_model()
            await self._initialize_performance_model()
            await self._initialize_salary_model()
            await self._initialize_engagement_model()
            
            # Load pre-trained models if available
            await self._load_pretrained_models()
            
            self.initialized = True
            logger.info("Predictive analytics service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize predictive analytics: {e}")
            raise ModelLoadingError(f"Initialization failed: {e}")
    
    async def predict_attrition(self, employee_data: Dict[str, Any]) -> AttritionPrediction:
        """Predict employee attrition probability."""
        if not self.initialized:
            await self.initialize()
        
        try:
            # Prepare features
            features = await self._prepare_attrition_features(employee_data)
            
            # Make prediction
            if self.attrition_model:
                probability = self.attrition_model.predict_proba([features])[0][1]
                prediction = self.attrition_model.predict([features])[0]
            else:
                # Fallback to rule-based prediction
                probability, prediction = await self._rule_based_attrition_prediction(employee_data)
            
            # Calculate risk factors
            risk_factors = await self._analyze_attrition_risk_factors(employee_data, features)
            
            # Generate recommendations
            recommendations = await self._generate_attrition_recommendations(
                employee_data, probability, risk_factors
            )
            
            return AttritionPrediction(
                employee_id=employee_data.get('employee_id'),
                probability=probability,
                prediction=bool(prediction),
                risk_level=self._categorize_risk_level(probability),
                risk_factors=risk_factors,
                recommendations=recommendations,
                confidence=self._calculate_prediction_confidence(features),
                predicted_at=datetime.utcnow(),
                model_version=getattr(self.attrition_model, 'version', '1.0'),
            )
            
        except Exception as e:
            logger.error(f"Attrition prediction failed: {e}")
            raise PredictiveAnalysisError(f"Attrition prediction failed: {e}")
    
    async def forecast_performance(self, employee_data: Dict[str, Any], 
                                 forecast_periods: int = 4) -> PerformanceForecast:
        """Forecast employee performance for future periods."""
        if not self.initialized:
            await self.initialize()
        
        try:
            # Prepare features
            features = await self._prepare_performance_features(employee_data)
            
            # Generate forecasts
            forecasts = []
            current_date = datetime.utcnow()
            
            for period in range(1, forecast_periods + 1):
                if self.performance_model:
                    predicted_score = self.performance_model.predict([features])[0]
                else:
                    predicted_score = await self._rule_based_performance_prediction(employee_data)
                
                # Add some realistic variance
                variance = np.random.normal(0, 0.1)
                predicted_score = max(0, min(5, predicted_score + variance))
                
                forecast_date = current_date + timedelta(days=period * 90)  # Quarterly
                forecasts.append({
                    'period': period,
                    'date': forecast_date,
                    'predicted_score': predicted_score,
                    'confidence_interval': self._calculate_confidence_interval(predicted_score),
                })
            
            # Analyze trends
            trend_analysis = await self._analyze_performance_trends(employee_data, forecasts)
            
            # Generate improvement suggestions
            improvement_areas = await self._identify_improvement_areas(employee_data, forecasts)
            
            return PerformanceForecast(
                employee_id=employee_data.get('employee_id'),
                forecasts=forecasts,
                trend_direction=trend_analysis['direction'],
                trend_strength=trend_analysis['strength'],
                improvement_areas=improvement_areas,
                confidence=self._calculate_forecast_confidence(features),
                forecasted_at=datetime.utcnow(),
                model_version=getattr(self.performance_model, 'version', '1.0'),
            )
            
        except Exception as e:
            logger.error(f"Performance forecasting failed: {e}")
            raise PredictiveAnalysisError(f"Performance forecasting failed: {e}")
    
    async def analyze_trends(self, data: List[Dict[str, Any]], 
                           trend_type: str = 'general') -> TrendAnalysis:
        """Analyze trends in HR data."""
        try:
            df = pd.DataFrame(data)
            
            if df.empty:
                raise PredictiveAnalysisError("No data provided for trend analysis")
            
            # Perform trend analysis based on type
            if trend_type == 'attrition':
                trends = await self._analyze_attrition_trends(df)
            elif trend_type == 'performance':
                trends = await self._analyze_performance_trends_data(df)
            elif trend_type == 'salary':
                trends = await self._analyze_salary_trends(df)
            elif trend_type == 'engagement':
                trends = await self._analyze_engagement_trends(df)
            else:
                trends = await self._analyze_general_trends(df)
            
            # Statistical analysis
            statistical_insights = await self._perform_statistical_analysis(df, trend_type)
            
            # Seasonal patterns
            seasonal_patterns = await self._detect_seasonal_patterns(df)
            
            # Predictions
            future_predictions = await self._predict_future_trends(df, trend_type)
            
            return TrendAnalysis(
                trend_type=trend_type,
                data_points=len(df),
                time_range={
                    'start': df['date'].min() if 'date' in df.columns else None,
                    'end': df['date'].max() if 'date' in df.columns else None,
                },
                trends=trends,
                statistical_insights=statistical_insights,
                seasonal_patterns=seasonal_patterns,
                future_predictions=future_predictions,
                confidence=self._calculate_trend_confidence(df),
                analyzed_at=datetime.utcnow(),
            )
            
        except Exception as e:
            logger.error(f"Trend analysis failed: {e}")
            raise PredictiveAnalysisError(f"Trend analysis failed: {e}")
    
    async def generate_employee_risk_profile(self, employee_data: Dict[str, Any]) -> EmployeeRiskProfile:
        """Generate comprehensive risk profile for an employee."""
        try:
            # Get attrition prediction
            attrition_pred = await self.predict_attrition(employee_data)
            
            # Get performance forecast
            performance_forecast = await self.forecast_performance(employee_data)
            
            # Calculate various risk scores
            risk_scores = {
                'attrition_risk': attrition_pred.probability,
                'performance_risk': await self._calculate_performance_risk(performance_forecast),
                'engagement_risk': await self._calculate_engagement_risk(employee_data),
                'flight_risk': await self._calculate_flight_risk(employee_data),
                'burnout_risk': await self._calculate_burnout_risk(employee_data),
            }
            
            # Overall risk score
            overall_risk = np.mean(list(risk_scores.values()))
            
            # Risk factors
            risk_factors = await self._identify_comprehensive_risk_factors(employee_data, risk_scores)
            
            # Mitigation strategies
            mitigation_strategies = await self._generate_mitigation_strategies(risk_scores, risk_factors)
            
            # Priority level
            priority = self._determine_priority_level(overall_risk, risk_scores)
            
            return EmployeeRiskProfile(
                employee_id=employee_data.get('employee_id'),
                overall_risk_score=overall_risk,
                risk_scores=risk_scores,
                risk_level=self._categorize_risk_level(overall_risk),
                priority=priority,
                risk_factors=risk_factors,
                mitigation_strategies=mitigation_strategies,
                last_updated=datetime.utcnow(),
                next_review_date=datetime.utcnow() + timedelta(days=30),
            )
            
        except Exception as e:
            logger.error(f"Risk profile generation failed: {e}")
            raise PredictiveAnalysisError(f"Risk profile generation failed: {e}")
    
    async def generate_predictive_insights(self, organization_data: List[Dict[str, Any]]) -> PredictiveInsights:
        """Generate organization-wide predictive insights."""
        try:
            df = pd.DataFrame(organization_data)
            
            # Workforce analytics
            workforce_insights = await self._analyze_workforce_composition(df)
            
            # Attrition insights
            attrition_insights = await self._analyze_organizational_attrition(df)
            
            # Performance insights
            performance_insights = await self._analyze_organizational_performance(df)
            
            # Compensation insights
            compensation_insights = await self._analyze_compensation_trends(df)
            
            # Diversity insights
            diversity_insights = await self._analyze_diversity_metrics(df)
            
            # Predictive recommendations
            recommendations = await self._generate_organizational_recommendations(
                workforce_insights, attrition_insights, performance_insights
            )
            
            # Key metrics
            key_metrics = {
                'total_employees': len(df),
                'predicted_attrition_rate': attrition_insights.get('predicted_rate', 0),
                'average_performance_score': performance_insights.get('average_score', 0),
                'engagement_score': workforce_insights.get('engagement_score', 0),
                'diversity_index': diversity_insights.get('diversity_index', 0),
            }
            
            return PredictiveInsights(
                organization_id=organization_data[0].get('organization_id') if organization_data else None,
                key_metrics=key_metrics,
                workforce_insights=workforce_insights,
                attrition_insights=attrition_insights,
                performance_insights=performance_insights,
                compensation_insights=compensation_insights,
                diversity_insights=diversity_insights,
                recommendations=recommendations,
                confidence=self._calculate_insights_confidence(df),
                generated_at=datetime.utcnow(),
                data_period={
                    'start': df['date'].min() if 'date' in df.columns else None,
                    'end': df['date'].max() if 'date' in df.columns else None,
                },
            )
            
        except Exception as e:
            logger.error(f"Predictive insights generation failed: {e}")
            raise PredictiveAnalysisError(f"Insights generation failed: {e}")
    
    async def train_custom_model(self, training_data: List[Dict[str, Any]], 
                               model_type: str, target_column: str) -> Dict[str, Any]:
        """Train a custom predictive model."""
        try:
            df = pd.DataFrame(training_data)
            
            if df.empty or target_column not in df.columns:
                raise PredictiveAnalysisError("Invalid training data or target column")
            
            # Prepare features
            features, target = await self._prepare_training_data(df, target_column)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                features, target, test_size=0.2, random_state=42
            )
            
            # Train model based on type
            if model_type == 'classification':
                model = await self._train_classification_model(X_train, y_train)
            elif model_type == 'regression':
                model = await self._train_regression_model(X_train, y_train)
            else:
                raise PredictiveAnalysisError(f"Unsupported model type: {model_type}")
            
            # Evaluate model
            metrics = await self._evaluate_model(model, X_test, y_test, model_type)
            
            # Save model
            model_path = await self._save_custom_model(model, model_type, target_column)
            
            return {
                'model_type': model_type,
                'target_column': target_column,
                'metrics': metrics,
                'model_path': model_path,
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'trained_at': datetime.utcnow().isoformat(),
            }
            
        except Exception as e:
            logger.error(f"Custom model training failed: {e}")
            raise PredictiveAnalysisError(f"Model training failed: {e}")
    
    # Helper methods for feature preparation
    async def _prepare_attrition_features(self, employee_data: Dict[str, Any]) -> List[float]:
        """Prepare features for attrition prediction."""
        features = []
        
        # Demographic features
        features.append(employee_data.get('age', 30))
        features.append(1 if employee_data.get('gender') == 'Male' else 0)
        features.append(employee_data.get('years_at_company', 1))
        features.append(employee_data.get('years_in_role', 1))
        features.append(employee_data.get('years_with_manager', 1))
        
        # Job-related features
        features.append(employee_data.get('job_level', 1))
        features.append(employee_data.get('salary', 50000))
        features.append(employee_data.get('performance_rating', 3))
        features.append(employee_data.get('satisfaction_score', 3))
        features.append(employee_data.get('work_life_balance', 3))
        
        # Engagement features
        features.append(employee_data.get('training_hours_last_year', 0))
        features.append(employee_data.get('promotions_last_5_years', 0))
        features.append(employee_data.get('overtime_frequency', 0))
        features.append(employee_data.get('travel_frequency', 0))
        features.append(employee_data.get('stock_options', 0))
        
        return features
    
    async def _prepare_performance_features(self, employee_data: Dict[str, Any]) -> List[float]:
        """Prepare features for performance prediction."""
        features = []
        
        # Historical performance
        features.append(employee_data.get('last_performance_rating', 3))
        features.append(employee_data.get('average_performance_3_years', 3))
        features.append(employee_data.get('performance_trend', 0))  # -1, 0, 1
        
        # Development features
        features.append(employee_data.get('training_hours_last_year', 0))
        features.append(employee_data.get('certifications_count', 0))
        features.append(employee_data.get('mentoring_participation', 0))
        
        # Work environment
        features.append(employee_data.get('team_size', 5))
        features.append(employee_data.get('manager_rating', 3))
        features.append(employee_data.get('peer_feedback_score', 3))
        features.append(employee_data.get('project_complexity', 3))
        
        # Personal factors
        features.append(employee_data.get('years_experience', 5))
        features.append(employee_data.get('education_level', 3))  # 1-5 scale
        features.append(employee_data.get('motivation_score', 3))
        
        return features
    
    async def _rule_based_attrition_prediction(self, employee_data: Dict[str, Any]) -> Tuple[float, int]:
        """Rule-based attrition prediction as fallback."""
        risk_score = 0.0
        
        # Age factor
        age = employee_data.get('age', 30)
        if age < 25 or age > 55:
            risk_score += 0.1
        
        # Tenure factor
        tenure = employee_data.get('years_at_company', 1)
        if tenure < 1:
            risk_score += 0.2
        elif tenure > 10:
            risk_score += 0.1
        
        # Performance factor
        performance = employee_data.get('performance_rating', 3)
        if performance < 2:
            risk_score += 0.3
        elif performance > 4:
            risk_score -= 0.1
        
        # Satisfaction factor
        satisfaction = employee_data.get('satisfaction_score', 3)
        if satisfaction < 2:
            risk_score += 0.4
        elif satisfaction > 4:
            risk_score -= 0.2
        
        # Work-life balance
        wlb = employee_data.get('work_life_balance', 3)
        if wlb < 2:
            risk_score += 0.2
        
        # Salary competitiveness
        salary = employee_data.get('salary', 50000)
        market_rate = employee_data.get('market_rate', 55000)
        if salary < market_rate * 0.9:
            risk_score += 0.2
        
        probability = min(max(risk_score, 0.0), 1.0)
        prediction = 1 if probability > 0.5 else 0
        
        return probability, prediction
    
    async def _rule_based_performance_prediction(self, employee_data: Dict[str, Any]) -> float:
        """Rule-based performance prediction as fallback."""
        base_score = employee_data.get('last_performance_rating', 3.0)
        
        # Adjust based on factors
        adjustments = 0.0
        
        # Training impact
        training_hours = employee_data.get('training_hours_last_year', 0)
        if training_hours > 40:
            adjustments += 0.2
        elif training_hours > 20:
            adjustments += 0.1
        
        # Experience factor
        experience = employee_data.get('years_experience', 5)
        if experience > 10:
            adjustments += 0.1
        
        # Manager support
        manager_rating = employee_data.get('manager_rating', 3)
        if manager_rating > 4:
            adjustments += 0.1
        elif manager_rating < 2:
            adjustments -= 0.2
        
        # Work-life balance impact
        wlb = employee_data.get('work_life_balance', 3)
        if wlb < 2:
            adjustments -= 0.3
        elif wlb > 4:
            adjustments += 0.1
        
        predicted_score = base_score + adjustments
        return max(1.0, min(5.0, predicted_score))
    
    # Model initialization methods
    async def _initialize_attrition_model(self):
        """Initialize attrition prediction model."""
        self.attrition_model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=42
        )
    
    async def _initialize_performance_model(self):
        """Initialize performance prediction model."""
        self.performance_model = GradientBoostingRegressor(
            n_estimators=100,
            max_depth=6,
            random_state=42
        )
    
    async def _initialize_salary_model(self):
        """Initialize salary prediction model."""
        self.salary_model = LinearRegression()
    
    async def _initialize_engagement_model(self):
        """Initialize engagement prediction model."""
        self.engagement_model = RandomForestRegressor(
            n_estimators=50,
            max_depth=8,
            random_state=42
        )
    
    async def _load_pretrained_models(self):
        """Load pre-trained models if available."""
        try:
            # This would load models from disk in a real implementation
            # For now, we'll skip this step
            pass
        except Exception as e:
            logger.warning(f"Could not load pre-trained models: {e}")
    
    # Utility methods
    def _categorize_risk_level(self, probability: float) -> str:
        """Categorize risk level based on probability."""
        if probability < 0.3:
            return 'low'
        elif probability < 0.6:
            return 'medium'
        elif probability < 0.8:
            return 'high'
        else:
            return 'critical'
    
    def _calculate_prediction_confidence(self, features: List[float]) -> float:
        """Calculate confidence in prediction."""
        # Simple confidence calculation based on feature completeness
        non_zero_features = sum(1 for f in features if f != 0)
        confidence = non_zero_features / len(features)
        return min(max(confidence, 0.1), 0.95)
    
    def _calculate_confidence_interval(self, predicted_score: float) -> Dict[str, float]:
        """Calculate confidence interval for prediction."""
        margin = 0.3  # 95% confidence interval
        return {
            'lower': max(0, predicted_score - margin),
            'upper': min(5, predicted_score + margin),
        }
